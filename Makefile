projectName = docvault


apigo:
	 goctl api go -api ./api/convention/main.api -dir ./api --remote https://gitee.com/damengde/goctl_api_tempalte_new.git --branch v1.1.0 --style go_zero

buildapi: cleanapi
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o ${projectName}api ./api

buildrpc: cleanrpc
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o ${projectName}rpc ./rpc

cleanapi:
	rm -rf ${projectName}api

cleanrpc:
	rm -rf ${projectName}rpc


# 部署到79
deploy79api: buildapi
	./goblin --name ${projectName}api --host **************

deploy79rpc: buildrpc
	./goblin --name ${projectName}rpc --host **************

# 部署到242
deploy242api: buildapi
	./goblin --name ${projectName}api --host *************** --remote_path /data/project --supervisor=false

deploy242rpc: buildrpc
	./goblin --name ${projectName}rpc --host *************** --remote_path /data/project --supervisor=false

# 部署到150
deploy150api: buildapi
	./goblin --name ${projectName}api --host *************** --password Zj123456,,

deploy150rpc: buildrpc
	./goblin --name ${projectName}rpc --host *************** --password Zj123456,,

grpcgoapi:
	protoc --go_out ./api/internal/adapter/grpc/pb --go-grpc_out ./api/internal/adapter/grpc/pb ./api/internal/adapter/grpc/protos/${proto}.proto

zrpcgo:
	goctl rpc protoc ./rpc/${projectName}.proto --go_out=./rpc/pb --go-grpc_out=./rpc/pb --zrpc_out=./rpc --style go_zero -m 
	protoc --go_out ./api/internal/adapter/grpc/pb --go-grpc_out ./api/internal/adapter/grpc/pb ./rpc/${projectName}.proto


# 添加源码上游
add-source-upstream:
	git remote add upstream http://***************/xinghe-project-team/paperless-project/back-end/${projectName}.git