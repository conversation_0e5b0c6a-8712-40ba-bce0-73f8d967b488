-- 数据库迁移SQL脚本
-- 为 SaveRecycleInfo 和 SaveDisposalInfo 新增 ApprovalInfo 字段
-- 生成时间: 2025-01-29

-- 检查并添加 recycle_records 表的新字段
ALTER TABLE `recycle_records`
ADD COLUMN IF NOT EXISTS `workflow_id` varchar(64) COMMENT '流程ID' AFTER `other_reason`;

ALTER TABLE `recycle_records`
ADD COLUMN IF NOT EXISTS `approval_info` JSON COMMENT '审批信息' AFTER `workflow_id`;

-- 检查并添加 disposal_records 表的新字段
ALTER TABLE `disposal_records`
ADD COLUMN IF NOT EXISTS `workflow_id` varchar(64) COMMENT '流程ID' AFTER `reason`;

ALTER TABLE `disposal_records`
ADD COLUMN IF NOT EXISTS `approval_info` JSON COMMENT '审批信息' AFTER `workflow_id`;

-- 如果表不存在，创建 recycle_records 表
CREATE TABLE IF NOT EXISTS `recycle_records` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `distribute_record_id` varchar(64) DEFAULT NULL COMMENT '关联的发放记录ID',
  `recycle_by` varchar(64) DEFAULT NULL COMMENT '回收人,用户id',
  `recycle_date` bigint(20) DEFAULT NULL COMMENT '回收日期',
  `recycle_reason` text COMMENT '回收原因',
  `other_reason` text COMMENT '其他原因',
  `workflow_id` varchar(64) DEFAULT NULL COMMENT '流程ID',
  `approval_info` json DEFAULT NULL COMMENT '审批信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_distribute_record_id` (`distribute_record_id`)
) ENGINE=InnoDB DEFAULT  COMMENT='回收记录表';

-- 如果表不存在，创建 disposal_records 表
CREATE TABLE IF NOT EXISTS `disposal_records` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `distribute_record_id` varchar(64) DEFAULT NULL COMMENT '关联的发放记录ID',
  `disposal_by` varchar(64) DEFAULT NULL COMMENT '处置人,用户id',
  `disposal_date` bigint(20) DEFAULT NULL COMMENT '处置日期',
  `reason` text COMMENT '处置方式',
  `workflow_id` varchar(64) DEFAULT NULL COMMENT '流程ID',
  `approval_info` json DEFAULT NULL COMMENT '审批信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_distribute_record_id` (`distribute_record_id`)
) ENGINE=InnoDB DEFAULT  COMMENT='处置记录表';

-- 如果表不存在，创建 recycle_record_files 表
CREATE TABLE IF NOT EXISTS `recycle_record_files` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `record_id` varchar(64) DEFAULT NULL COMMENT '关联RecycleRecord的ID',
  `file_id` varchar(64) DEFAULT NULL COMMENT '文件id',
  `file_form` int(11) DEFAULT NULL COMMENT '文件形式,1电子文件 | 2纸质文件',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`)
) ENGINE=InnoDB DEFAULT  COMMENT='回收记录文件表';

-- 如果表不存在，创建 disposal_record_files 表
CREATE TABLE IF NOT EXISTS `disposal_record_files` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `record_id` varchar(64) DEFAULT NULL COMMENT '关联DisposalRecord的ID',
  `file_id` varchar(64) DEFAULT NULL COMMENT '文件id',
  `file_form` int(11) DEFAULT NULL COMMENT '文件形式,1电子文件 | 2纸质文件',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`)
) ENGINE=InnoDB DEFAULT  COMMENT='处置记录文件表';

-- 如果表不存在，创建 recycle_record_permissions 表
CREATE TABLE IF NOT EXISTS `recycle_record_permissions` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `file_record_id` varchar(64) DEFAULT NULL COMMENT '关联RecycleRecordFile的ID',
  `user_id` varchar(64) DEFAULT NULL COMMENT '接收人ID',
  PRIMARY KEY (`id`),
  KEY `idx_file_record_id` (`file_record_id`)
) ENGINE=InnoDB DEFAULT  COMMENT='回收记录权限表';

-- 如果表不存在，创建 disposal_record_permissions 表
CREATE TABLE IF NOT EXISTS `disposal_record_permissions` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `file_record_id` varchar(64) DEFAULT NULL COMMENT '关联DisposalRecordFile的ID',
  `user_id` varchar(64) DEFAULT NULL COMMENT '接收人ID',
  PRIMARY KEY (`id`),
  KEY `idx_file_record_id` (`file_record_id`)
) ENGINE=InnoDB DEFAULT  COMMENT='处置记录权限表';
