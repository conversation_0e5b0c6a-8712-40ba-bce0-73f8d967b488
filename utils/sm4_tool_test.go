package utils

import (
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestNewSm4Tool(t *testing.T) {
	sm4Tool := NewSm4Tool()
	encryptedData := `MmI3MWJkZmZkOTg2YTA2ZDYwOWEwOTY0ZThjZGM4YWVhOGNiZmE2YjhmZDBmOGU2MmM3MjM1MTM1M2FkZTg0MTk5ZGZkNmRlNjBiZTc2YTRjMmNkYWVjYzg5MmI3ODA4Y2MwMGMzZmNjY2VhNGViNWEzY2U0MGE2ODQ4YWFkYmU0YTVmNTRhNjI0ZDQ3MTI4OWYzNDFkOGYwZDYxN2U5OWY0ODkwN2M4YWE5ZDE4OTk0ODNlOWQyZDUzMGUwYzIxOGRmMDE5MDQ3ODA1NWQ5NTcwMmQ0YzJlNDExZGYzMGI4ZDkzMGUzZjdhOWIyYzNiNzA5YmNhMDQwYjc0MzYzMjM1ZTQ2MmZiM2RmMGFkZWY4MDI1YjJiYjk4YTEyMTkzMTcyYjRhNDU2MmUzNjllYjFhOTUzNjExNmI4NzllYzkxMzM4OWMwMmMyZGRiYjc0MmY5N2U5ZTlmM2FmNDhjYTM5NWM4ZDA5MjU2N2EyNWZlNzc3OTA2NjI2YjU1YmJmZTI3MzNkZWMwMjYwODk5MTE1NTc3NDIwMGNjN2ZiYmUwNGQ2NzZmZDUzZDdlYmY4OTVmYzMyYzJjYjVkNzQ4NDZjNjNiMTEwODMzNWEzNTI4OTJiZTMyYmUwNzhiNDZhZTZiZTExYTViYzViNTAyYzExZmZlMzRiMTZkOTIzMDE1MzZlMGMyNWZkOTE5MDZiMGQ2MjNmNTIyN2QzYmM5YzBlNTEyNGI1MTFlZWU2OWNlNTIyMGQwYzAzMTA2ZDlmYzk0ODYyYWRmNmQ2NDE1MmY5OTNhODc5MGM4NzZhZjgxZDFhZTI3NzhkMGZiNWQwYzMwYTdmYmNkNjJmNDZkZjZjOGFmZGM1Nzc4MzRiNjU5MzZmODRkNTI5ZDMwYjhmZGFhNzViMWQ1ZDdhYTQxNDA4Mjk4MjBhZDdhYzMyNDQ1MDc5NGYwOWI5NDhhOTgzMDRiZDk4ZTljZDhiZjM3OWM2MjRkZGY4YTMxNjZlYjIzMzg4NzQ5ZTViZGRiNDc1Y2JhY2ZhODA4N2RmOTQ0M2ZmMDg3MmY2ODZkZTM1ZjAyMjZkMGUwNWM5NjBlZWE4YWZlNGE2YzBjYjdjNjNmMDg5MDNlMzQ1MGJhZDExMzUyZGJlM2FlMWEyMTkyNmJm`
	plainText := `{"url":"/saas/api/v1/auth/login","method":"post","data":{"p_data":"ZmNmMjNjYWFjMmU1ZTg0MmNiZDc2MDQyZjg2ZDI3NjczNDFiZGE0NjgyMDU2OGQ2ZGU5MDY1YzE2OTZmN2FkMTA0ODViMDA1ZGQzY2NhMTU2M2RhZWY2YTI5YWFmZGVmMDVkY2ZkNTkxYTI1MWQ3NjdjMjY0ZGQ4YWY0NmYwZTliNTc1YWYxYzg5OWVkYWY1M2JmM2NmY2ExOGRlMWEzOGM0ZDE3ZDEyMWQ2NzIzNDY4NTNlNWFkNWU5MTIxZjNlMmNiZGQxODQxODRkZTFmOTI5OTdiZDk4MDU3YTNhNWQ1NTFlZWZjZDc2YmVlZmJlNDBmMWEzZWUyODdlZGI1NQ=="},"query":{}}`

	convey.Convey("test decrypt", t, func() {
		result, err := sm4Tool.Decrypt(encryptedData)
		convey.So(err, convey.ShouldBeNil)
		convey.So(string(result), convey.ShouldEqual, plainText)
	})

	convey.Convey("test encrypt", t, func() {
		result, err := sm4Tool.Encrypt([]byte(plainText))
		convey.So(err, convey.ShouldBeNil)
		convey.So(string(result), convey.ShouldEqual, encryptedData)
	})
}
