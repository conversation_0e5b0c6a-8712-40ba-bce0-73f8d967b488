package utils

import (
	"fmt"
	"net/url"
	"testing"
	"time"
)

func TestMD5Hex(t *testing.T) {
	milli := time.Now().UnixMilli()

	fmt.Println(milli)
	hex := MD5Hex(fmt.Sprintf("%s%d%d%s", "chencong", milli, milli, "XZtBfeFOvMRH1BeuHlkC9XrqJN6spjWm"))

	fmt.Println(hex)
	// Original URL path
	path := "/conference/main/main-details"

	// Parameters
	params := url.Values{}
	params.Set("id", "544202377268326130")
	params.Set("type", "1")
	params.Set("reject", "true")

	// Create the URL
	u := &url.URL{
		Path:     path,
		RawQuery: params.Encode(),
	}
	// Get the encoded URL
	encodedURL := u.String()
	fmt.Println(fmt.Sprintf("http://92.168.110.242/front/#/auth-login?clientId=zy&userCode=chencong&ts=%d&reqId=%d&sign=%s&redirect=%s", milli, milli, hex, encodedURL))

	//	result, err := teemo.SendHttpPostRequest[map[string]interface{}](context.Background(), "http://127.0.0.1:8899/south/api/v1/sso/authentication", nil, fmt.Sprintf(`{
	//    "clientId": "zy",
	//    "userCode": "%s",
	//    "ts": %d,
	//    "reqId": "%d",
	//    "sign": "%s"
	//}`, "chencong", milli, milli, hex))
	//	if err != nil {
	//		fmt.Println(err)
	//	}
	//	fmt.Println(result)
}

//http://172.22.102.11/front/#/auth-login?clientId=zy&userCode=yangwenxiao&ts=1733793909635&reqId=lGvgu9TP1733793909635&sign=d400be5352d244e56e909b9c32bfba04&redirect=%2Fconference%2Fmain%2Fmain-details%3Fid%3D543477829338162699%26type%3D1
