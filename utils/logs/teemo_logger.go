package logs

import (
	"context"
	"time"

	"gitee.com/damengde/teemo"
	"github.com/zeromicro/go-zero/core/logc"
)

type TeemoLogger struct {
	level teemo.LogLevel
	// 慢请求日志
	slowThreshold time.Duration
}

func NewTeemoLogger(level teemo.LogLevel, slowThreshold time.Duration) *TeemoLogger {
	return &TeemoLogger{
		level:         level,
		slowThreshold: slowThreshold,
	}
}

// LogMode 设置日志级别
func (l *TeemoLogger) LogMode(level teemo.LogLevel) teemo.Logger {
	newLog := *l
	newLog.level = level
	return &newLog
}

func (l *TeemoLogger) Debugf(ctx context.Context, msg string, args ...interface{}) {
	if l.level >= teemo.Debug {
		logc.Infof(ctx, msg, args...)
	}
}

// Infof 记录信息级别的日志
func (l *TeemoLogger) Infof(ctx context.Context, msg string, args ...interface{}) {
	if l.level >= teemo.Info {
		logc.Infof(ctx, msg, args...)
	}
}

// Errorf 记录错误级别的日志
func (l *TeemoLogger) Errorf(ctx context.Context, msg string, args ...interface{}) {
	if l.level >= teemo.Error {
		// 实现错误日志输出逻辑
		logc.Errorf(ctx, msg, args...)
	}
}

// Slowf 记录慢请求信息
func (l *TeemoLogger) Slowf(ctx context.Context, begin time.Time, url string) {
	if l.level <= teemo.Silent {
		return
	}

	elapsed := time.Since(begin)

	if elapsed > l.slowThreshold {
		logc.Slowf(ctx, "[%s] Request took %s for URL: %s",
			time.Now().Format(time.RFC3339),
			elapsed.String(),
			url,
		)
	}
}
