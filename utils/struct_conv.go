package utils

import (
	"encoding/json"
	"fmt"
)

func StructJsonTagToStrMap(obj interface{}) (map[string]string, error) {
	b, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	// 将 JSON 字符串反序列化为 map[string]interface{}
	var tempMap map[string]interface{}
	if err := json.Unmarshal(b, &tempMap); err != nil {
		return nil, err
	}
	var result = make(map[string]string)
	// 将 map[string]interface{} 转换为 map[string]string
	for key, value := range tempMap {
		result[key] = fmt.Sprintf("%v", value)
	}
	return result, nil
}
