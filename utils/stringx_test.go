package utils

import (
	"reflect"
	"testing"
)

func TestCamelToKebab(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "简单驼峰",
			input:    "helloWorld",
			expected: "Hello-World",
		},
		{
			name:     "包含数字",
			input:    "userId123",
			expected: "User-Id-123",
		},
		{
			name:     "单个单词",
			input:    "hello",
			expected: "Hello",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
		{
			name:     "复杂组合",
			input:    "userHttpRequestId123",
			expected: "User-Http-Request-Id-123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CamelToKebab(tt.input)
			if result != tt.expected {
				t.<PERSON><PERSON>("CamelToKebab(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestKebabToCamel(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "简单中线",
			input:    "hello-world",
			expected: "HelloWorld",
		},
		{
			name:     "包含数字",
			input:    "user-id-123",
			expected: "UserId123",
		},
		{
			name:     "单个单词",
			input:    "hello",
			expected: "Hello",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
		{
			name:     "复杂组合",
			input:    "user-http-request-id-123",
			expected: "UserHttpRequestId123",
		},
		{
			name:     "复杂组合2",
			input:    "User-Http-Request-Id-123",
			expected: "UserHttpRequestId123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := KebabToCamel(tt.input)
			if result != tt.expected {
				t.Errorf("KebabToCamel(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestSplitStringIgnoreEmpty(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		sep      string
		expected []string
	}{
		{
			name:     "简单测试",
			input:    "a,b,c,d",
			sep:      ",",
			expected: []string{"a", "b", "c", "d"},
		},
		{
			name:     "空字符串",
			input:    "",
			sep:      ",",
			expected: []string{},
		},
		{
			name:     "包含空字符串",
			input:    "a,,b,c,d",
			sep:      ",",
			expected: []string{"a", "b", "c", "d"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SplitStringIgnoreEmpty(tt.input, tt.sep)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("name: %s, SplitStringIgnoreEmpty(%q, %q) = %v, want %v, result: %v", tt.name, tt.input, tt.sep, result, tt.expected, result)
			}
		})
	}
}
