package utils

import (
	"strings"
	"unicode"
)

// CamelToKebab 将驼峰命名转换为中线命名，保持首字母大写
// 例如：helloWorld -> Hello-World
// userId123 -> User-Id-123
func CamelToKebab(s string) string {
	if s == "" {
		return ""
	}

	var result strings.Builder
	var isNewWord = true

	for i, r := range s {
		// 处理大写字母和数字
		if i > 0 {
			prev := rune(s[i-1])
			if unicode.IsUpper(r) || (unicode.IsNumber(r) && !unicode.IsNumber(prev)) {
				result.WriteRune('-')
				isNewWord = true
			}
		}

		// 写入当前字符
		if isNewWord {
			result.WriteRune(unicode.ToUpper(r))
			isNewWord = false
		} else {
			result.WriteRune(unicode.ToLower(r))
		}
	}
	return result.String()
}

// KebabToCamel 将中线命名转换为驼峰命名，保持首字母大写
// 例如：hello-world -> HelloWorld
// user-id-123 -> UserId123
func KebabToCamel(s string) string {
	if s == "" {
		return ""
	}

	words := strings.Split(s, "-")
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}
	return strings.Join(words, "")
}

// 切割字符串为切片，忽略书组中的空字符串
func SplitStringIgnoreEmpty(s string, sep string) []string {
	parts := strings.Split(s, sep)
	var result []string = make([]string, 0)
	for _, part := range parts {
		if part != "" {
			result = append(result, part)
		}
	}
	return result
}
