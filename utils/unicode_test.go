package utils

import "testing"
import . "github.com/smartystreets/goconvey/convey"

func TestUnicode2Chinese(t *testing.T) {
	Convey("Given a string with Unicode escape sequences", t, func() {
		input := "这是一个\\u4e2d\\u6587\\u7684\\u4f8b\\u5b50"
		expected := "这是一个中文的例子"

		Convey("When Unicode2Chinese is called", func() {
			result := Unicode2Chinese(input)

			<PERSON>vey("It should return the correct Chinese string", func() {
				So(result, ShouldEqual, expected)
			})
		})
	})

	<PERSON>vey("Given a string with invalid Unicode escape sequences", t, func() {
		input := "这是一个\\u4e2d\\u6587\\u7684\\u4f8b\\u5b50XXXX"
		expected := "这是一个中文的例子XXXX"

		Convey("When Unicode2Chinese is called", func() {
			result := Unicode2Chinese(input)

			Convey("It should return the string with invalid sequences unchanged", func() {
				So(result, ShouldEqual, expected)
			})
		})
	})

	<PERSON>vey("Given a string without Unicode escape sequences", t, func() {
		input := "这是一个中文的例子"
		expected := "这是一个中文的例子"

		Convey("When Unicode2Chinese is called", func() {
			result := Unicode2Chinese(input)

			Convey("It should return the original string", func() {
				So(result, ShouldEqual, expected)
			})
		})
	})
}
