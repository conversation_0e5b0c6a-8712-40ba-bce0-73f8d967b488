package utils

import (
	"regexp"
	"strconv"
	"strings"
)

// Unicode2Chinese unicode转中文，转换失败的返回原文
func Unicode2Chinese(unicodeStr string) string {
	// 正则表达式匹配 \u 开头的 Unicode 序列
	re := regexp.MustCompile(`\\u[0-9a-fA-F]{4}`)
	matches := re.FindAllString(unicodeStr, -1)

	// 替换每个 Unicode 序列
	for _, match := range matches {
		// 将 \u 序列转换为字符
		char, err := strconv.Unquote(`"` + match + `"`)
		if err != nil {
			// 失败跳过
			continue
		}
		// 替换原字符串中的 Unicode 序列
		unicodeStr = strings.Replace(unicodeStr, match, char, 1)
	}

	return unicodeStr
}
