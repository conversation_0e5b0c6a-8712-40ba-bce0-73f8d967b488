package utils

import (
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestStructJsonTagToStrMap(t *testing.T) {
	convey.Convey("test", t, func() {
		type Person struct {
			Name      string `json:"name"`
			Age       int    `json:"age"`
			IsStudent bool   `json:"isStudent"`
		}
		p := Person{Name: "张三", Age: 30, IsStudent: false}
		m, err := StructJsonTagToStrMap(p)
		if err != nil {
			t.Error(err)
			return
		}
		convey.So(m["name"], convey.ShouldEqual, "张三")
		convey.So(m["age"], convey.ShouldEqual, "30")
		convey.So(m["isStudent"], convey.ShouldEqual, "false")
	})
}
