# DocVault - 文档存证服务

DocVault 是一个基于 Go 语言和 `go-zero` 微服务框架构建的现代化、高可用的分布式文档存证解决方案。

项目采用领域驱动设计（DDD）思想，将业务逻辑与技术实现解耦，并通过 API 网关与核心 RPC 服务分离的架构，确保了系统的高性能、高扩展性和高可维护性。

## ✨ 功能特性

- **API 网关**: 提供统一的 HTTP/S 接口，负责请求路由、认证、限流等。
- **核心业务服务**: 通过 gRPC 提供高性能的内部服务，处理核心业务逻辑。
- **领域驱动设计**: 清晰的领域分层，使业务逻辑更加内聚和稳定。
- **多数据库支持**: 集成了关系型数据库（如 MySQL）与图数据库（NebulaGraph），以应对复杂的业务场景。
- **消息队列集成**: 使用 Kafka 进行异步任务处理和事件驱动通信。
- **分布式缓存**: 使用 Redis 加速热点数据访问。

## 🏗️ 系统架构

系统遵循经典的微服务架构模式，主要由两部分组成：**API 网关**和**RPC 服务**。

```mermaid
graph TD
    subgraph Client
        A[用户/客户端]
    end

    subgraph "API Gateway (api/)"
        B(HTTP/S API)
    end

    subgraph "Core Services (rpc/)"
        C(gRPC Service)
    end

    subgraph "Infrastructure"
        D[MySQL]
        E[DocvaultGraph]
        F[Redis]
        G[Kafka]
    end

    A -- HTTP/S Request --> B
    B -- gRPC Call --> C
    C -- CRUD --> D
    C -- Graph Query --> E
    C -- Cache --> F
    C -- Pub/Sub --> G
```

- **API Gateway (`api/`)**: 作为系统的统一入口，负责处理外部的 HTTP 请求。它本身不包含复杂的业务逻辑，而是通过 gRPC 调用后端的 `rpc` 服务来完成实际的工作。
- **RPC Service (`rpc/`)**: 实现了项目所有的核心业务逻辑，如用户操作、文档签名、工作流处理等。它与数据库、缓存、消息队列等基础设施直接交互。
- **Domain Layer (`domain/`)**: 定义了项目的核心业务模型和规则，是独立于任何技术实现的纯粹的业务逻辑层。
- **Infrastructure Layer (`infrastructure/`)**: 提供了 `domain` 层所需接口的具体技术实现，例如数据库的仓储实现、第三方服务的客户端等。

## 🛠️ 技术栈

- **语言**: Go
- **框架**: [go-zero](https://github.com/zeromicro/go-zero)
- **通信**: gRPC, HTTP/S
- **数据库**: MySQL/PostgreSQL, NebulaGraph
- **消息队列**: Kafka
- **缓存**: Redis
- **配置中心**: etcd (由 `go-zero` 默认集成)

## 📁 目录结构

```
/docvault
├── .git/               # Git 版本控制
├── .tool-versions      # asdf 工具版本定义
├── Makefile            # 自动化构建脚本
├── README.md           # 项目说明文档
├── go.mod              # Go 模块依赖定义
├── go.sum              # Go 模块依赖校验和
│
├── api/                # API 网关服务 (HTTP/S)
│   ├── docvault.go     # 服务入口
│   ├── convention/     # API 描述文件 (.api)
│   ├── etc/            # 配置文件 (.yaml)
│   └── internal/       # 内部私有代码
│       ├── adapter/    # 适配器 (e.g., gRPC client)
│       ├── config/     # 配置加载
│       ├── handler/    # HTTP Handler (路由 & 请求处理)
│       ├── logic/      # 业务逻辑 (调用 RPC)
│       ├── middleware/ # 中间件
│       ├── respx/      # 统一响应格式
│       ├── svc/        # 服务上下文 (依赖注入)
│       └── types/      # API 请求/响应结构体
│
├── rpc/                # 核心 RPC 服务 (gRPC)
│   ├── docvault.go     # 服务入口
│   ├── docvault.proto  # gRPC 接口定义
│   ├── client/         # gRPC Client 封装
│   ├── etc/            # 配置文件 (.yaml)
│   ├── internal/       # 内部私有代码
│   │   ├── config/     # 配置加载
│   │   ├── interceptor/# gRPC 拦截器
│   │   ├── logic/      # 核心业务逻辑实现
│   │   ├── server/     # gRPC Server 实现
│   │   └── svc/        # 服务上下文 (依赖注入)
│   └── pb/             # gRPC 生成的代码
│
├── consts/             # 全局常量
│
├── domain/             # 领域驱动设计 (DDD) - 核心领域层
│   ├── aggregate/      # 聚合根
│   ├── entity/         # 实体
│   ├── factory/        # 工厂
│   └── value/          # 值对象
│
├── infrastructure/     # 领域驱动设计 (DDD) - 基础设施层
│   ├── adapter/        # 适配器
│   │   ├── addons/     # 基础设施插件 (Redis, ID生成)
│   │   ├── clientx/    # 第三方服务客户端
│   │   ├── grpc/       # gRPC 相关基础设施
│   │   ├── kqs/        # Kafka 生产者/消费者
│   │   └── mapper/     # 数据库操作 (ORM)
│   └── referenceimpl/  # 领域接口的能力实现
│
└── utils/              # 通用工具库
    ├── logs/           # 日志工具
    ├── statusx/        # 状态码/错误码
    └── ...             # 其他工具 (MD5, SM4, 结构体转换等)
```

## 🚀 快速开始

### 1. 环境准备

- Go (版本请参考 `go.mod`)
- Docker & Docker Compose
- etcd
- Redis
- Kafka
- MySQL / NebulaGraph

### 2. 克隆项目

```bash
git clone <your-project-repo-url>
cd docvault
```

### 3. 配置

1.  根据你的本地环境，修改 `api/etc/docvault.yaml` 和 `rpc/etc/docvault.yaml` 中的数据库、Redis、Kafka、etcd 等连接信息。

### 4. 启动服务

```bash
 go run ./rpc/docvault.go -f ./rpc/etc/docvault.yaml &  go run ./api/docvault.go -f ./api/etc/docvault.yaml

```

现在，API 网关应该在配置文件中指定的端口上运行，并已连接到 RPC 服务。

### 5. Makefile 命令

- `make apigo`: 根据 api 文件生成 logic
- `make buildapi`: 构建api服务 linux amd64包
- `make buildrpc`: 构建rpc服务 linux amd64包
- `make deploy150api`: 部署api服务到 150
- `make deploy150rpc`: 部署rpc服务到 150
- `make grpcgoapi`: 根据 api 里 proto 生成客户端代码
- `make zrpcgo`: 根据 rpc 定义 proto  在 rpc 中生成服务端和客户端代码，在 api 中生成客户端代码
---

