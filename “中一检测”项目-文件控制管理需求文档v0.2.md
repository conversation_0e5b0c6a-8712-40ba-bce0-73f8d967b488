**体系管理-文件控制管理**

**需求文档**

**	**

版本历史

| 版本 | 作者 | 日期 | 修订描述 | 变更记录 |
| --- | --- | --- | --- | --- |
| 0.1 | 毛金达 | 2025.7.8 | 新建 | 新建体系管理-文件控制管理需求文档，完成初版文档 |
| 0.2 | 毛金达 | 2025.7.9 | 更新 | 1.在内外部文件库的发放回收中，补充“外发：电子文件一次下载变更记录”<br/>2.在发放回收和处置中，修改补充当文件权限选择外发时的逻辑 |
|  |  |  | |  |
|  |  |  | |  |


**目录**

1.引言	1

1.1编写目的	1

1.2读者对象	1

1.3编写原则	1

2.需求概述	1

2.1需求背景	1

2.2需求目标	1

2.3使用对象和权限	1

3.主要功能	2

4.全局需求	4

4.1输入限制	4

4.2列表操作列	4

4.3在线预览水印	4

4.4文件下载水印	4

4.4.1内部文件下载	4

4.4.2外部文件下载	4

4.4.3内部文件内部发放	4

4.4.4内部文件外部发放	4

4.5审批原因选项	5

4.5.1文件发放申请原因	5

4.5.2文件回收申请原因	5

4.5.3文件作废申请原因	5

4.5.4文件作废申请原因	5

4.5.5文件作废申请原因	5

4.6系统管理-字典管理控制字段	5

5.内部文件库	5

5.1权限说明	5

5.2筛选	5

5.3表单	6

5.4新增/修订	7

5.5导入	8

5.6发放回收	8

5.6.1发放回收	9

5.6.2发放回收记录	9

5.6.3内发：纸质文件-一次下载变更记录	10

5.6.4外发：电子文件-一次下载变更记录	10

5.6.5借阅记录	10

5.7详情	11

5.8作废	11

5.9借阅	11

5.10查阅	11

5.11下载	11

5.12变更记录	11

5.13编号规则	12

6.外部文件库	12

6.1权限说明	12

6.2集团库	12

6.2.1筛选	13

6.2.2表单	13

6.2.3批量纳入	14

6.2.4新增/修订	15

6.2.5导入	16

6.2.6发放回收	17

6.2.6.1发放回收	17

6.2.6.2发放回收记录	17

6.2.6.3内发：纸质文件-一次下载变更记录	18

6.2.6.4外发：电子文件-一次下载变更记录	18

6.2.6.5借阅记录	19

6.2.7纳入	19

6.2.8详情	19

6.2.9作废	19

6.2.10借阅	19

6.2.11查阅	19

6.2.12下载	20

6.2.13变更记录	20

6.3公司库	20

6.3.1筛选	21

6.3.2表单	21

6.3.3发放回收	22

6.3.3.1发放回收	22

6.3.3.2发放回收记录	22

6.3.3.3内发：纸质文件-一次下载变更记录	23

6.3.3.4外发：电子文件-一次下载变更记录	24

6.3.3.5借阅记录	24

6.3.4纳入	24

6.3.5详情	24

6.3.6作废	24

6.3.7借阅	25

6.3.8查阅	25

6.3.9下载	25

6.3.10变更记录	25

6.4编号规则	26

7.书籍库	26

7.1权限说明	26

7.2筛选	26

7.3表单	27

7.4新增/编辑	27

7.5导入	28

7.6预览	29

7.7领用	29

7.8借用	29

7.9领用归还	29

7.10借用归还	29

7.11删除	29

7.12编号规则	29

8.发放回收和处置	29

8.1权限说明	29

8.2筛选	30

8.3表单	30

8.4新增/编辑	31

8.4.1发放清单	32

8.5发放和回收详情	33

8.6纸质文件处置详情	33

8.7回收	34

8.7.1发放清单	35

8.8纸质文件处置	35

8.8.1纸质文件处置清单	36

8.9回收记录	36

8.10处置记录	36

9.借阅和交还	37

9.1权限说明	37

9.2筛选	37

9.3表单	37

9.4新增/编辑	38

9.4.1借阅清单	39

9.5借阅详情	39

9.6回收	40

10.作废申请	40

10.1权限说明	40

10.2筛选	40

10.3表单	41

10.4新增/编辑	42

10.4.1作废清单	42

10.5详情	43

11.内部作废文件库	43

11.1权限说明	43

11.2筛选	43

11.3表单	44

11.4借阅	44

11.5作废详情	45

12.外部作废文件库	45

12.1权限说明	45

12.2集团库	45

12.2.1筛选	45

12.2.2表单	46

12.2.3借阅	46

12.2.4作废详情	46

12.3公司库	47

12.3.1筛选	47

12.3.2表单	47

12.3.3借阅	48

12.3.4作废详情	48



# 1.引言
## 1.1编写目的
编写本文档是为了帮助项目小组明确本项目的详细需求，以细化项目计划，评估项目成本，实现系统设计和开发，编写测试用例，减少需求变更，保证项目开发成功。

## 1.2读者对象
本文档适合以下人员阅读：

+ 本项目负责人和项目经理；
+ 本项目相关业务需求人员和测试人员；
+ 本项目相关前后端开发人员；
+ 业务需求相关领导；
+ 经授权的本项目相关合作公司人员；
+ 其他相关人员。

## 1.3编写原则
本文档将作为测试和需求变更的依据。对于本文档指明应达到的需求系统未实现的，属于缺陷；对于本文档未细化的需求，若开发完成后需要调整，属于需求变更，需重新进行需求评审并排期。

本文档将在开发过程中随着需求的变更而更改和修订，每一版本变更均会留痕。

# 2.需求概述
## 2.1需求背景
    lims系统中，需要模块分控管理集团和各子公司文件，因此新建文件控制管理大模块，用于全系统文件管控需求。

## 2.2需求目标
通过建立文件控制管理模块，系统性的管理全系统文件，方便用户管理、使用文件。

## 2.3使用对象和权限
通过角色管理模块建立三个角色：集团文件管理员、子公司文件管理员、普通文件用户。集团文件管理员用于管理集团文件库，子公司文件管理员用于管理子公司文件库，普通文件用户可查看自己所属子公司/集团的文件。

# 3.主要功能
文件控制管理一期完成10个子模块，2个模块建立目录不开发，下面进行逐一说明。

| <font style="color:rgb(0, 0, 0);">功能模块</font> | <font style="color:rgb(0, 0, 0);">一级功能</font> | <font style="color:rgb(0, 0, 0);">二级功能</font> |
| --- | --- | --- |
| <font style="color:rgb(0, 0, 0);">内部文件库</font> | <font style="color:rgb(0, 0, 0);">筛选列表</font> | 筛选数据 |
| | | 新增 |
| | | 导入 |
| | | 导出 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 修订 |
| | | 详情 |
| | | 发放回收 |
| | | 作废 |
| | | 借阅 |
| | | 查阅 |
| | | 下载 |
| | | 变更记录 |
| <font style="color:rgb(0, 0, 0);">外部文件库</font> | <font style="color:rgb(0, 0, 0);">集团库</font> | 筛选数据 |
| | | 批量纳入 |
| | | 新增 |
| | | 导入 |
| | | 导出 |
| | | 列表 |
| | | 修订 |
| | | 详情 |
| | | 发放回收 |
| | | 纳入子公司 |
| | | 作废 |
| | | 借阅 |
| | | 查阅 |
| | | 下载 |
| | | 变更记录 |
| | <font style="color:rgb(0, 0, 0);">公司库</font> | 筛选数据 |
| | | 导出 |
| | | 修订 |
| | | 详情 |
| | | 发放回收 |
| | | 作废 |
| | | 借阅 |
| | | 查阅 |
| | | 下载 |
| | | 变更记录 |
| <font style="color:rgb(0, 0, 0);">书籍库</font> | <font style="color:rgb(0, 0, 0);">筛选</font> | 筛选数据 |
| | | 新增 |
| | | 导入 |
| | | 导出 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 编辑 |
| | | 详情 |
| | | 领用 |
| | | 借用 |
| | | 归还 |
| <font style="color:rgb(0, 0, 0);">发放回收和处置</font> | <font style="color:rgb(0, 0, 0);">筛选</font> | 筛选数据 |
| | | 导出 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 发放和回收详情 |
| | | 纸质文件处置详情 |
| | | 回收 |
| | | 纸质文件处置 |
| <font style="color:rgb(0, 0, 0);">借阅和交还</font> | <font style="color:rgb(0, 0, 0);">筛选</font> | 筛选数据 |
| | | 导出 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 回收 |
| | | 借阅详情 |
| <font style="color:rgb(0, 0, 0);">作废申请</font> | <font style="color:rgb(0, 0, 0);">筛选</font> | 筛选数据 |
| | | 导出 |
| | | 新增 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 编辑 |
| | | 撤销 |
| | | 详情 |
| | | 删除 |
| <font style="color:rgb(0, 0, 0);">内部作废文件库</font> | <font style="color:rgb(0, 0, 0);">筛选</font> | 筛选数据 |
| | | 导出 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 借阅 |
| | | 作废详情 |
| <font style="color:rgb(0, 0, 0);">外部作废文件库</font> | <font style="color:rgb(0, 0, 0);">筛选</font> | 筛选数据 |
| | | 导出 |
| | <font style="color:rgb(0, 0, 0);">列表</font> | 台账 |
| | | 借阅 |
| | | 作废详情 |
| <font style="color:rgb(0, 0, 0);">内部文件审查（一期不做）</font> | <font style="color:rgb(0, 0, 0);">审查计划</font> |  |
| | <font style="color:rgb(0, 0, 0);">审查任务</font> |  |
| <font style="color:rgb(0, 0, 0);">外部文件审查（一期不做）</font> | |  |


# 4.全局需求
## 4.1输入限制
无特殊说明的短文本输入框，限制50个字，长文本框限制500个字。

## 4.2列表操作列
所有列表的操作列固定在列表最右侧，不需要滚动即可操作。

## 4.3在线预览水印
所有文件的在线预览，需要打上水印：预览日期 预览用户名 本文件涉密 禁止拍照或截图。

## 4.4文件下载水印
### 4.4.1内部文件下载
所有文件下载为本地文件后，需要打上水印“文件下载后单次生效，可能存在变更，实际文件以系统内为准”。

### **4.4.2外部文件下载**
外部文件下载后自动加水印“中一检测 内部收藏”。

### 4.4.3内部文件内部发放
内部文件内部发放下载后自动加水印“XXXXXX有限公司（公司全称） 内部使用文件”。

### 4.4.4内部文件外部发放
内部文件外部发放下载后自动加水印，形式如下：

参考文件，变更不予通知/作废不予回收。

发放日期：XXXXXX

接收方：XXXXXXXXX·严禁转发”

发放日期为该文件通过发放审批的日期，接收方为审批时填写的人员。

## 4.5审批原因选项
文件管理体系内，发起审批项需要填写原因，选项如下所示。

### 4.5.1文件发放申请原因
新员工入职、岗位/职责调整、文件版本更新、新增业务/流程实施、跨部门协作需求、其他 (请说明)。

### 4.5.2**文件回收申请原因**
员工离职/调离、岗位/职责调整、文件版本更新、文件正式作废、文件过期失效、错误发放纠正、定期清理、其他 (请说明)。

### 4.5.3**文件作废申请原因**
版本更新替代、内容错误/失效、业务/流程不适用、相关标准/法规废止、业务/流程终止、组织架构变更、技术/设备淘汰、合并/整合、其他 (请说明)。

### 4.5.4**文件作废申请原因**
项目参考/研究、问题调查/分析、审计/检查准备、培训/学习需要、其他 (请说明)。

### 4.5.5**文件作废申请原因**
新标准发布、标准变更、业务需求、其他（请说明）。

## 4.6系统管理-字典管理控制字段
外部文件库-认证方式、外部文件库-文件类别、内部文件库-文件类别、外部文件库-所属领域、书籍库-书籍类别。



# 5.内部文件库
## 5.1权限说明
内部文件库各子公司单独管理，数据互相隔离，由各子公司文件管理员进行管理，普通用户根据账号所在组织架构决定显示的数据，仅显示当前账号所在的子公司数据。

文件管理员：新增、导入、导出、修订、详情、发放回收、作废、借阅、查询、下载、变更记录

普通用户：借阅、查询、下载

## 5.2筛选
列表顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择编制部门</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">即将作废、即将实施、有效、拟修订</font> |
| <font style="color:rgb(0, 0, 0);">是否有附件</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">是、否</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>5.4新增/修订 |
| <font style="color:rgb(0, 0, 0);">导入</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现导入页面，详见</font>5.5导入 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |




## 5.3表单
表单根据集文件编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">编制部门</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">编制人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">即将作废：已通过作废审批，未到作废日期</font><br/><font style="color:rgb(0, 0, 0);">即将实施：已通过新增或修订审批，未到实施日期</font><br/><font style="color:rgb(0, 0, 0);">有效：文件在实施日期内</font><br/><font style="color:rgb(0, 0, 0);">拟修订：修订审批中</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">修订：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">详情：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">发放回收：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">作废：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">借阅：所有人可见</font><br/><font style="color:rgb(0, 0, 0);">查阅：所有人可见</font><br/><font style="color:rgb(0, 0, 0);">下载：所有人可见</font><br/><font style="color:rgb(0, 0, 0);">变更记录：文件管理员可见</font> |




## 5.4新增/修订
拥有新增/修订权限的用户可以点击新增增加内部文件库数据，或点击修订修改数据，新增和修订不需要审批。注意，若为修订的，原版本/版次自动进入作废文件库，不会删除，点击按钮出现弹窗，

修订后，文件的版本/版次自动变化，若文件类别不变，文件编号不变，文件类别发生变化，则文件编号需要重新生成。

信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | 编号规则见5.13编号规则 |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">第一版本为A/0,修订后为A/1，依次类推，A/9之后变为B/0</font> |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">在“系统管理-字典管理”中新增“内部文件库-文件类别”，根据字典内容显示</font> |
| <font style="color:rgb(0, 0, 0);">编制部门</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">来源部门数据</font> |
| <font style="color:rgb(0, 0, 0);">编制人</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">选择部门后才可以选择</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期选择</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择日期</font> | |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期选择</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择日期</font> | <font style="color:rgb(0, 0, 0);">数据通过审批后，在实施日期前，数据状态为“待实施”，通过审批时间在实施日期之后的为“有效”</font> |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">原版本版次</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">上传文件</font> | <font style="color:rgb(0, 0, 0);">文件上传组件</font> | | | | <font style="color:rgb(0, 0, 0);">仅允许上传一个文件，仅允许上传PDF文件</font> |
| <font style="color:rgb(0, 0, 0);">相关文件</font> | <font style="color:rgb(0, 0, 0);">仅有标题</font> | | | | <font style="color:rgb(0, 0, 0);">无内容，为二期留空</font> |
| <font style="color:rgb(0, 0, 0);">相关表单</font> | <font style="color:rgb(0, 0, 0);">仅有标题</font> | | | | <font style="color:rgb(0, 0, 0);">无内容，为二期留空</font> |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将新增文件，是否确认？”，点击确认后数据更新至列表</font> |




## 5.5导入
拥有导入权限的用户可以点击导入批量增加内部文件库数据，导入不需要审批，导入后生成的文件编号按照台账内的顺序进行生成，版本版次默认为A/0。点击按钮出现弹窗，信息如下：



| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">上传台账-下载模板</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">下载导入模板</font> |
| <font style="color:rgb(0, 0, 0);">上传台账</font> | <font style="color:rgb(0, 0, 0);">文件上传</font> | | | | <font style="color:rgb(0, 0, 0);">仅允许上传一个文件，仅能上传xls/xlsx文件</font> |
| <font style="color:rgb(0, 0, 0);">上传文件</font> | <font style="color:rgb(0, 0, 0);">文件上传</font> | | | | <font style="color:rgb(0, 0, 0);">允许上传多个文件，仅能上传pdf文件</font> |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将新增文件，是否确认？”，点击确认后数据更新至列表</font> |


页面提示“提示：台账中的文件名称需和上传文件的名称保持一致，否则会导致导入失败”。

1.若导入的台账清单比上传的文件多或相等，则生成对应的台账数据，且将文件保存到与台账中文件名称相同的附件中

2.若导入的台账清单比上传的文件少，则提示台账与文件不匹配，提交失败

3.若上传的文件名称中有台账的文件名称中不存在的文件，则提示台账与文件不匹配，提交失败。

4.若上传的文件名称中有与现有文件重名的，提示“有文件已存在，导入失败，请检查后重新导入！”

提交成功后按照导入的台账及文件生成数据。

## 5.6发放回收
点击列表内的发放回收按钮，出现弹窗，可操作单条数据的权限分配、回收，同时可查看该数据的各项记录：

### 5.6.1发放回收
发放回收分为四个权限，内发：电子文件-查询，内发-电子文件查询/下载，内发：纸质文件-一次下载，外发：电子文件-一次下载。每一个权限下方为选人组件，选人组件左侧显示未选中人员，根据组织架构显示人员，默认全部收起，支持人员模糊搜索，选人组件右侧显示选中人员，根据组织架构显示人员，默认全部展开，支持人员模糊搜索。选中人员移动后点击提交，弹窗提示“确认发起发放/回收申请？确认后将发起审批流程”，发起审批后同步在“文件控制管理-发放申请”和“文件控制管理-发放回收和处置”生成对应的数据。若在操作时有人员在审批流程中，则人员置灰不可选择。举例：选择张三授权“内发：电子文件-查询”权限发起审批后，此时张三显示在选人组件左侧且置灰，通过审批后再次进入此页面则显示在右侧。发放操作需要生成消息通知，接收人未点消息通知的为“未签收”，点击后为“已签收”。

<font style="color:#000000;">内发：电子文件-查询、电子文件-查询/下载、纸质文件-一次下载：需要选择发放原因，必填。填写完成确认后发起审批。</font>

<font style="color:#000000;">外发：电子文件-一次下载：需要选择发放原因，并且填写接收方，均为必填项。填写完成确认后发起审批。</font>

### 5.6.2发放回收记录
列表显示每一次发放回收的记录，只显示通过审批的数据，需要同步“文件控制管理-发放申请”和“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">操作时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">记录该次操作中对“内发：电子文件-查询”权限的人员变更，共三种状态，以张三举例：</font><br/><font style="color:rgb(0, 0, 0);">张三（未签收）：本次操作将文件发放给张三，但张三并未查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（已签收）：本次操作将文件发放给张三，张三已查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（回收）：本次操作将文件权限从张三处收回。</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询/下载</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">记录该次操作中对“内发：电子文件-查询”权限的人员变更，共三种状态，以张三举例：</font><br/><font style="color:rgb(0, 0, 0);">张三（未签收）：本次操作将文件发放给张三，但张三并未查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（已签收）：本次操作将文件发放给张三，张三已查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（回收）：本次操作将文件权限从张三处收回。</font> |
| <font style="color:rgb(0, 0, 0);">变更人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


### 5.6.3内发：纸质文件-一次下载变更记录
内发：纸质文件-一次下载变更记录，只显示通过发放审批的数据，每次发放审批通过时生成一条，需要同步“文件控制管理-发放回收和处置”的记录。默认按照发放申请时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">纸质文件接收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">纸质文件状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">未回收：未发起回收审批</font><br/><font style="color:rgb(0, 0, 0);">回收中：正在回收流程中</font><br/><font style="color:rgb(0, 0, 0);">已回收：已完成回收审批</font> |
| <font style="color:rgb(0, 0, 0);">文件处置状态</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">仅在纸质文件状态为“已回收”时有数据。</font><br/><font style="color:rgb(0, 0, 0);">未处置（点击处置）：此状态下可点击此按钮发起文件处置流程，出现弹窗，填写处置方法，发起审批。</font><br/><font style="color:rgb(0, 0, 0);">处置中：此状态下已发起文件处置流程但未通过审批，按钮不可用。</font><br/><font style="color:rgb(0, 0, 0);">已处置：XXXX：此状态为通过了文件处置流程，括号内为提交审批时填写的处置方法</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起发放审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">发放申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起发放审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">回收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起回收审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">回收申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起回收审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font><br/><font style="color:rgb(0, 0, 0);">需要注意的是弱文件处置状态为“未处置（点击处置）”，详情中无数据</font> |


### 5.6.4外发：电子文件-一次下载变更记录
列表显示外发：电子文件-一次下载的发放记录，只显示通过审批的数据，需要同步“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">接收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">文件选择的接收人</font> |
| <font style="color:rgb(0, 0, 0);">纸质文件接收方</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起审批时填写的接收方</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起发放审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">发放申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起发放审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">是否下载</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">文件是否已被下载</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


### 5.6.5借阅记录
列表显示纸质文件的回收记录，只显示通过审批的数据，需要同步“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">操作时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">借阅人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">借阅期限</font> | <font style="color:rgb(0, 0, 0);">日期区间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD至YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


## 5.7详情
页面同5.6发放回收，所有操作不可用，仅可查看。

## 5.8作废
点击列表作废按钮，出现弹窗，选择作废原因，<font style="color:#000000;">填写完成确认后发起审批。</font>

## 5.9借阅
判断用户当前是否有借阅权限，有借阅权限时点击借阅可预览文件。

无权限时弹窗提示无借阅权限，询问是否需要发起借阅审批，确认后需要填写借阅日期（区间），借阅原因，填写后发起审批。发起流程后在9.借阅和交还中会同步生成记录。

若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

## 5.10查阅
判断用户是否有文件的查阅权限，有查阅权限时点击查阅可预览文件。

无查阅权限时弹窗提示“无查阅权限，是否发起查阅申请”。确认后打开查阅申请页面，详见发放回收和处置8.4.新增/编辑.文件固定为当前文件不可修改，填写必填内容后发起借阅申请流程。文件管理员可选择所有权限。若为普通用户，仅能选择“内发：电子文件-查询”不可更改。发起流程后在8.发放回收和处置中会同步生成记录。

若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

## 5.11下载
判断用户是否有文件的下载权限，有下载权限时点击可下载文件。

无下载权限时弹窗提示“无下载权限，是否发起下载申请”。确认后打开下载申请页面，详见发放回收和处置8.4.新增/编辑.文件固定为当前文件不可修改，填写必填内容后发起借阅申请流程。文件管理员可选择所有权限。若为普通用户，仅能选择“外发：电子文件-一次下载”不可更改。发起流程后在8.发放回收和处置中会同步生成记录。

若有多个下载权限（内发：电子文件-查询/下载、外发：电子文件-一次下载、内发：纸质文件-一次下载），弹窗让当前用户选择要下载的选项，一次下载的权限下载后即失效。

## 5.12变更记录
点击变更记录显示弹窗，列表显示该文件变化历史列表，列表按照更新日期倒序排列

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">编制部门</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">编制人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">操作类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">新增、修订、作废</font> |


## 5.13编号规则
集团库与各个子公司的内部文件库编码各自独立，编码规则为：组织代码+“/”+字典库内部文件类别一级分类代码+“-”+字典库内部文件类别子分类代码+“-”+序号（序号为实位）。

举例：浙江中一检测研究院股份有限公司（ZYZJ），新增了规范性文件（SG）-技术规范类（JG）-管理（GL）分类下的文件，且是首次新增该分类，那么该文件在新增至浙江中一的内部文件库时自动生成文件编码ZYZJ/SG-JG-GL-1。

每个公司必须保持同类别中各文件顺序码是连续的，除非某个文件彻底作废了，并且没有新的版次替代他。

若某个分类中有文件作废了或者修改了分类，最新的文件编号需要在文件分类中的最后一位生成，不能插在中间缺失位。

# 6.外部文件库
## 6.1权限说明
外部文件库分为集团库和公司库，集团库仅集团账号权限和各子公司管理员可见，由集团管理员进行管理，公司库由各子公司单独管理，数据互相隔离，由各子公司文件管理员进行管理，普通用户根据账号所在组织架构决定显示的数据，仅显示当前账号所在的子公司数据。

集团文件管理员：只有集团库，新增、导入、导出、修订、详情、发放回收、作废、借阅、查询、下载、变更记录

集团普通用户：只有集团库，借阅、查询、下载

子公司文件管理员：

集团库：批量纳入、纳入子公司、借阅、查询、下载；

子公司库：导出、详情、发放回收、作废、借阅、查询、下载、变更记录；

子公司普通用户：只有公司库，借阅、查询、下载

## 6.2集团库
拥有集团库权限的用户可在外部文件库主页顶部看到集团库。

### 6.2.1筛选
顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入集团文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择所属领域</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">输入原文件号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文部门</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择认证方式</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">是否有附件</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">是、否</font> |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">即将作废、即将实施、有效、拟修订</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">批量纳入</font> | | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现批量纳入页面，详见</font>6.2.3批量纳入 |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>6.2.4新增/修订 |
| <font style="color:rgb(0, 0, 0);">导入</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现导入页面，详见</font>6.2.5导入 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


### 6.2.2表单
表单根据集团文件编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">集团文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">所属领域</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文部门</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">认证方式</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">状态</font> | | <font style="color:rgb(0, 0, 0);">即将作废：已通过作废审批，未到作废日期</font><br/><font style="color:rgb(0, 0, 0);">即将实施：已通过新增或修订审批，未到实施日期</font><br/><font style="color:rgb(0, 0, 0);">有效：文件在实施日期内</font><br/><font style="color:rgb(0, 0, 0);">拟修订：修订审批中</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">修订：集团文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">详情：集团文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">发放回收：集团文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">纳入子公司：子公司文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">作废：集团文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">借阅：集团文件管理员、集团普通用户、子公司文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">查阅：集团文件管理员、集团普通用户、子公司文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">下载：集团文件管理员、集团普通用户、子公司文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">变更记录：集团文件管理员可见</font> |


### 6.2.3批量纳入
点击批量纳入按钮，出现弹窗，可批量操作集团库数据进入子公司库通过审批后生效，审批流程通过后纳入至发起流程的文件管理员所属子公司。需要注意的是，当纳入申请通过时，子公司文件编号与集团编号独立，编号规则见6.4编号规则。纳入清单仅能搜索到未被纳入至当前子公司的数据。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">自动获取申请用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD，自动获取当前日期</font> |
| <font style="color:rgb(0, 0, 0);">纳入清单</font> | <font style="color:rgb(0, 0, 0);">可编辑表</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">通过筛选集团文件编号和集团文件名称选择文件，对选中文件进行可增加文件和删除文件，</font>填写“原文件编号”（非必填），“原版本/版次”（非必填），不填写时纳入后字段内容为空，其余内容均为集团库内容。 |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将发起审批，是否确认提交？”，点击确认后提交至审批流程，审批流程通过后数据更新至列表</font> |


### 6.2.4新增/修订
拥有新增/修订权限的用户可以点击新增增加文件库数据，或点击修订修改数据，新增和修订不需要审批。集团库修订时，同步修订纳入至各子公司的文件数据，只修改“版本/版次“，不修改“文件编号”、“原文件编号”、“原版本/版次”，注意，若为修订的，原版本/版次自动进入作废文件库，不会删除。需要注意，子公司的版本/版次和集团库的版本/版次同步升级，但不相同。举例：集团库版本/版次位A/3，子公司首次纳入时版本/版次为A/0，当集团库修订一次后，集团库版本/版次更新为A/4，子公司更新为A/1。

修订后，文件的版本/版次自动变化，若文件类别不变，文件编号不变，文件类别发生变化，则文件编号需要重新生成。

点击按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">集团文件编号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | 编号规则见6.4编号规则 |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">第一版本为A/0,修订后为A/1，依次类推，A/9之后变为B/0</font> |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">在“系统管理-字典管理”中新增“外部文件库-文件类别”，根据字典内容显示</font> |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">所属领域</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">在“系统管理-字典管理”中新增“外部文件库-所属领域”，根据字典内容显示</font> |
| <font style="color:rgb(0, 0, 0);">发文部门</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">来源部门数据</font> |
| <font style="color:rgb(0, 0, 0);">发文号</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">选择部门后才可以选择</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期选择</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择日期</font> | |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期选择</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择日期</font> | <font style="color:rgb(0, 0, 0);">数据通过审批后，在实施日期前，数据状态为“待实施”，通过审批时间在实施日期之后的为“有效”</font> |
| <font style="color:rgb(0, 0, 0);">认证方式</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">在“系统管理-字典管理”中新增“外部文件库-认证方式”，根据字典内容显示</font> |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">原版本版次</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">上传文件</font> | <font style="color:rgb(0, 0, 0);">文件上传组件</font> | | | | <font style="color:rgb(0, 0, 0);">仅允许上传一个文件，仅允许上传PDF文件</font> |
| <font style="color:rgb(0, 0, 0);">相关文件</font> | <font style="color:rgb(0, 0, 0);">仅有标题</font> | | | | <font style="color:rgb(0, 0, 0);">无内容，为二期留空</font> |
| <font style="color:rgb(0, 0, 0);">相关表单</font> | <font style="color:rgb(0, 0, 0);">仅有标题</font> | | | | <font style="color:rgb(0, 0, 0);">无内容，为二期留空</font> |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将新增文件，是否确认？”，点击确认后数据更新至列表</font> |




### 6.2.5导入
拥有导入权限的用户可以点击导入批量增加外部文件库集团库数据，导入不需要审批，导入后生成的文件编号按照台账内的顺序进行生成，版本版次默认为A/0。点击按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">上传台账-下载模板</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">下载导入模板</font> |
| <font style="color:rgb(0, 0, 0);">上传台账</font> | <font style="color:rgb(0, 0, 0);">文件上传</font> | | | | <font style="color:rgb(0, 0, 0);">仅允许上传一个文件，仅能上传xls/xlsx文件</font> |
| <font style="color:rgb(0, 0, 0);">上传文件</font> | <font style="color:rgb(0, 0, 0);">文件上传</font> | | | | <font style="color:rgb(0, 0, 0);">允许上传多个文件，仅能上传pdf文件</font> |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将新增文件，是否确认？”，点击确认后数据更新至列表</font> |


页面提示“提示：台账中的文件名称需和上传文件的名称保持一致，否则会导致导入失败”。

1.若导入的台账清单比上传的文件多或相等，则生成对应的台账数据，且将文件保存到与台账中文件名称相同的附件中

2.若导入的台账清单比上传的文件少，则提示台账与文件不匹配，提交失败

3.若上传的文件名称中有台账的文件名称中不存在的文件，则提示台账与文件不匹配，提交失败。

4.若上传的文件名称中有与现有文件重名的，提示“有文件已存在，导入失败，请检查后重新导入！”

提交成功后按照导入的台账及文件生成数据。

### 6.2.6发放回收
点击列表内的发放回收按钮，出现弹窗，可操作单条数据的权限分配、回收，同时可查看该数据的各项记录：

#### 6.2.6.1发放回收
发放回收分为四个权限，内发：电子文件-查询，内发-电子文件查询/下载，内发：纸质文件-一次下载，外发：电子文件-一次下载。每一个权限下方为选人组件，选人组件左侧显示未选中人员，根据组织架构显示人员，默认全部收起，支持人员模糊搜索，选人组件右侧显示选中人员，根据组织架构显示人员，默认全部展开，支持人员模糊搜索。选中人员移动后点击提交，弹窗提示“确认发起发放/回收申请？确认后将发起审批流程”，发起审批后同步在“文件控制管理-发放申请”和“文件控制管理-发放回收和处置”生成对应的数据。若在操作时有人员在审批流程中，则人员置灰不可选择。举例：选择张三授权“内发：电子文件-查询”权限发起审批后，此时张三显示在选人组件左侧且置灰，通过审批后再次进入此页面则显示在右侧。发放操作需要生成消息通知，接收人未点消息通知的为“未签收”，点击后为“已签收”。

<font style="color:#000000;">内发：电子文件-查询、电子文件-查询/下载、纸质文件-一次下载：需要选择发放原因，必填。填写完成确认后发起审批。</font>

<font style="color:#000000;">外发：电子文件-一次下载：需要选择发放原因，并且填写接收方，均为必填项。填写完成确认后发起审批。</font>

#### 6.2.6.2发放回收记录
列表显示每一次发放回收的记录，只显示通过审批的数据，需要同步“文件控制管理-发放申请”和“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">操作时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">记录该次操作中对“内发：电子文件-查询”权限的人员变更，共三种状态，以张三举例：</font><br/><font style="color:rgb(0, 0, 0);">张三（未签收）：本次操作将文件发放给张三，但张三并未查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（已签收）：本次操作将文件发放给张三，张三已查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（回收）：本次操作将文件权限从张三处收回。</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询/下载</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">记录该次操作中对“内发：电子文件-查询/下载”权限的人员变更，共三种状态，以张三举例：</font><br/><font style="color:rgb(0, 0, 0);">张三（未签收）：本次操作将文件发放给张三，但张三并未查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（已签收）：本次操作将文件发放给张三，张三已查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（回收）：本次操作将文件权限从张三处收回。</font> |
| <font style="color:rgb(0, 0, 0);">变更人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


#### 6.2.6.3内发：纸质文件-一次下载变更记录
内发：纸质文件-一次下载变更记录，只显示通过发放审批的数据，每次发放审批通过时生成一条，需要同步“文件控制管理-发放回收和处置”的记录。默认按照发放申请时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">纸质文件接收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">纸质文件状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">未回收：未发起回收审批</font><br/><font style="color:rgb(0, 0, 0);">回收中：正在回收流程中</font><br/><font style="color:rgb(0, 0, 0);">已回收：已完成回收审批</font> |
| <font style="color:rgb(0, 0, 0);">文件处置状态</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">仅在纸质文件状态为“已回收”时有数据。</font><br/><font style="color:rgb(0, 0, 0);">未处置（点击处置）：此状态下可点击此按钮发起文件处置流程，出现弹窗，填写处置方法，发起审批。</font><br/><font style="color:rgb(0, 0, 0);">处置中：此状态下已发起文件处置流程但未通过审批，按钮不可用。</font><br/><font style="color:rgb(0, 0, 0);">已处置：XXXX：此状态为通过了文件处置流程，括号内为提交审批时填写的处置方法</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起发放审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">发放申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起发放审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">回收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起回收审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">回收申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起回收审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font><br/><font style="color:rgb(0, 0, 0);">需要注意的是弱文件处置状态为“未处置（点击处置）”，详情中无数据</font> |


#### 6.2.6.4外发：电子文件-一次下载变更记录
列表显示外发：电子文件-一次下载的发放记录，只显示通过审批的数据，需要同步“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">接收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">文件选择的接收人</font> |
| <font style="color:rgb(0, 0, 0);">纸质文件接收方</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起审批时填写的接收方</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起发放审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">发放申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起发放审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">是否下载</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">文件是否已被下载</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


#### 6.2.6.5借阅记录
列表显示纸质文件的回收记录，只显示通过审批的数据，需要同步“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">操作时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">借阅人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">借阅期限</font> | <font style="color:rgb(0, 0, 0);">日期区间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD至YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


### 6.2.7纳入
点击列表中文件后方“纳入”按钮，出现弹窗，填写“原文件编号”（非必填），“原版本/版次”（非必填），不填写时纳入后字段内容为空，其余内容均为集团库内容，点击提交进入审批流程，审批流程通过后纳入至发起流程的文件管理员所属子公司。子公司文件编号与集团编号独立，编号规则见6.4编号规则。若当前文件已被纳入至当前子公司，则提示“文件已纳入，无法纳入”。

### 6.2.8详情
页面同6.2.6发放回收，所有操作不可用，仅可查看。

### 6.2.9作废
点击列表作废按钮，出现弹窗，选择作废原因，<font style="color:#000000;">填写完成确认后发起审批。集团文件作废后，被纳入至各子公司的文件同步作废，保存作废记录。</font>

### 6.2.10借阅
判断用户当前是否有借阅权限，有借阅权限时点击借阅可预览文件。

无权限时弹窗提示无借阅权限，询问是否需要发起借阅审批，确认后需要填写借阅日期（区间），借阅原因，填写后发起审批。发起流程后在9.借阅和交还中会同步生成记录。

若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

### 6.2.11查阅
判断用户是否有文件的查阅权限，有查阅权限时点击查阅可预览文件。

无查阅权限时弹窗提示“无查阅权限，是否发起查阅申请”。确认后打开查阅申请页面，详见发放回收和处置8.4.新增/编辑.文件固定为当前文件不可修改，填写必填内容后发起借阅申请流程。文件管理员可选择所有权限。若为普通用户，仅能选择“内发：电子文件-查询”不可更改。发起流程后在8.发放回收和处置中会同步生成记录。

若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

### 6.2.12下载
判断用户是否有文件的下载权限，有下载权限时点击可下载文件。

无下载权限时弹窗提示“无下载权限，是否发起下载申请”。确认后打开下载申请页面，详见发放回收和处置8.4.新增/编辑.文件固定为当前文件不可修改，填写必填内容后发起借阅申请流程。文件管理员可选择所有权限。若为普通用户，仅能选择“外发：电子文件-一次下载”不可更改。发起流程后在8.发放回收和处置中会同步生成记录。

若有多个下载权限（内发：电子文件-查询/下载、外发：电子文件-一次下载、内发：纸质文件-一次下载），弹窗让当前用户选择要下载的选项，一次下载的权限下载后即失效。

### 6.2.13变更记录
点击变更记录显示弹窗，列表显示该文件变化历史列表，列表按照更新日期倒序排列

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">集团文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">所属领域</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文部门</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">认证方式</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">操作类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">新增、修订、作废</font> |


## 6.3公司库
有公司库权限的用户可在外部文件库可查看本账号所在公司库，只有公司库权限的用户无法查看集团库。公司库文件与集团库文件相关，集团库文件修订时，公司库文件自动更新除文件编号外内容，且自动更新一个版本/版次。集团库文件作废时，子公司文件同步作废。

### 6.3.1筛选
顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择所属领域</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">输入原文件号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文部门</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择认证方式</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">是否有附件</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">是、否</font> |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">即将作废、即将实施、有效</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


### 6.3.2表单
表单根据文件编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">纳入时根据</font>6.4编号规则<font style="color:rgb(0, 0, 0);">进行编号</font> |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">所属领域</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文部门</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">认证方式</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">表单</font> | | <font style="color:rgb(0, 0, 0);">即将作废：集团主文件或子公司其中之一已通过作废审批，未到作废日期</font><br/><font style="color:rgb(0, 0, 0);">即将实施：纳入的文件已通过新增或修订审批，未到实施日期</font><br/><font style="color:rgb(0, 0, 0);">有效：文件在实施日期内</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">修订：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">详情：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">发放回收：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">作废：文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">借阅：所有人可见</font><br/><font style="color:rgb(0, 0, 0);">查阅：所有人可见</font><br/><font style="color:rgb(0, 0, 0);">下载：所有人可见</font><br/><font style="color:rgb(0, 0, 0);">变更记录：文件管理员可见</font> |


### 6.3.3发放回收
点击列表内的发放回收按钮，出现弹窗，可操作单条数据的权限分配、回收，同时可查看该数据的各项记录：

#### 6.3.3.1发放回收
发放回收分为四个权限，内发：电子文件-查询，内发-电子文件查询/下载，内发：纸质文件-一次下载，外发：电子文件-一次下载。每一个权限下方为选人组件，选人组件左侧显示未选中人员，根据组织架构显示人员，默认全部收起，支持人员模糊搜索，选人组件右侧显示选中人员，根据组织架构显示人员，默认全部展开，支持人员模糊搜索。选中人员移动后点击提交，弹窗提示“确认发起发放/回收申请？确认后将发起审批流程”，发起审批后同步在“文件控制管理-发放申请”和“文件控制管理-发放回收和处置”生成对应的数据。若在操作时有人员在审批流程中，则人员置灰不可选择。举例：选择张三授权“内发：电子文件-查询”权限发起审批后，此时张三显示在选人组件左侧且置灰，通过审批后再次进入此页面则显示在右侧。发放操作需要生成消息通知，接收人未点消息通知的为“未签收”，点击后为“已签收”。

<font style="color:#000000;">内发：电子文件-查询、电子文件-查询/下载、纸质文件-一次下载：需要选择发放原因，必填。填写完成确认后发起审批。</font>

<font style="color:#000000;">外发：电子文件-一次下载：需要选择发放原因，并且填写接收方，均为必填项。填写完成确认后发起审批。</font>

#### 6.3.3.2发放回收记录
列表显示每一次发放回收的记录，只显示通过审批的数据，需要同步“文件控制管理-发放申请”和“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">操作时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">记录该次操作中对“内发：电子文件-查询”权限的人员变更，共三种状态，以张三举例：</font><br/><font style="color:rgb(0, 0, 0);">张三（未签收）：本次操作将文件发放给张三，但张三并未查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（已签收）：本次操作将文件发放给张三，张三已查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（回收）：本次操作将文件权限从张三处收回。</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询/下载</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">记录该次操作中对“内发：电子文件-查询”权限的人员变更，共三种状态，以张三举例：</font><br/><font style="color:rgb(0, 0, 0);">张三（未签收）：本次操作将文件发放给张三，但张三并未查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（已签收）：本次操作将文件发放给张三，张三已查看消息通知。</font><br/><font style="color:rgb(0, 0, 0);">张三（回收）：本次操作将文件权限从张三处收回。</font> |
| <font style="color:rgb(0, 0, 0);">变更人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


#### 6.3.3.3内发：纸质文件-一次下载变更记录
内发：纸质文件-一次下载变更记录，只显示通过发放审批的数据，每次发放审批通过时生成一条，需要同步“文件控制管理-发放回收和处置”的记录。默认按照发放申请时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">纸质文件接收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">纸质文件状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">未回收：未发起回收审批</font><br/><font style="color:rgb(0, 0, 0);">回收中：正在回收流程中</font><br/><font style="color:rgb(0, 0, 0);">已回收：已完成回收审批</font> |
| <font style="color:rgb(0, 0, 0);">文件处置状态</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">仅在纸质文件状态为“已回收”时有数据。</font><br/><font style="color:rgb(0, 0, 0);">未处置（点击处置）：此状态下可点击此按钮发起文件处置流程，出现弹窗，填写处置方法，发起审批。</font><br/><font style="color:rgb(0, 0, 0);">处置中：此状态下已发起文件处置流程但未通过审批，按钮不可用。</font><br/><font style="color:rgb(0, 0, 0);">已处置：XXXX：此状态为通过了文件处置流程，括号内为提交审批时填写的处置方法</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起发放审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">发放申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起发放审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">回收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起回收审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">回收申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起回收审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font><br/><font style="color:rgb(0, 0, 0);">需要注意的是弱文件处置状态为“未处置（点击处置）”，详情中无数据</font> |


#### 6.3.3.4外发：电子文件-一次下载变更记录
列表显示外发：电子文件-一次下载的发放记录，只显示通过审批的数据，需要同步“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">接收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">文件选择的接收人</font> |
| <font style="color:rgb(0, 0, 0);">纸质文件接收方</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起审批时填写的接收方</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">发起发放审批的用户姓名</font> |
| <font style="color:rgb(0, 0, 0);">发放申请时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起发放审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">是否下载</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">文件是否已被下载</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


#### 6.3.3.5借阅记录
列表显示纸质文件的回收记录，只显示通过审批的数据，需要同步“文件控制管理-发放回收和处置”的记录。默认按照操作时间倒序排列：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">操作时间</font> | <font style="color:rgb(0, 0, 0);">时间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD HH:MM:SS，该数据发起审批的时间</font> |
| <font style="color:rgb(0, 0, 0);">借阅人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">借阅期限</font> | <font style="color:rgb(0, 0, 0);">日期区间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD至YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">审批流程</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：点击按钮查看审批申请，显示每个环节的审批人和审批日期，最后一个审批人叫批准人，前面的环节都叫审核人。</font> |


### 6.3.4纳入
点击列表中文件后方“纳入”按钮，出现弹窗，填写“原文件编号”（非必填），“原版本/版次”（非必填），不填写时纳入后字段内容为空，其余内容均为集团库内容，点击提交进入审批流程，审批流程通过后纳入至发起流程的文件管理员所属子公司。子公司文件编号与集团编号独立，编号规则见6.4编号规则。若当前文件已被纳入至当前子公司，则提示“文件已纳入，无法纳入”。

### 6.3.5详情
页面同6.3.3发放回收，所有操作不可用，仅可查看。

### 6.3.6作废
点击列表作废按钮，出现弹窗，选择作废原因，<font style="color:#000000;">填写完成确认后发起审批。集团文件作废后，被纳入至各子公司的文件同步作废，保存作废记录。</font>

### 6.3.7借阅
判断用户当前是否有借阅权限，有借阅权限时点击借阅可预览文件。

无权限时弹窗提示无借阅权限，询问是否需要发起借阅审批，确认后需要填写借阅日期（区间），借阅原因，填写后发起审批。发起流程后在9.借阅和交还中会同步生成记录。

若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

### 6.3.8查阅
判断用户是否有文件的查阅权限，有查阅权限时点击查阅可预览文件。

无查阅权限时弹窗提示“无查阅权限，是否发起查阅申请”。确认后打开查阅申请页面，详见发放回收和处置8.4.新增/编辑.文件固定为当前文件不可修改，填写必填内容后发起借阅申请流程。文件管理员可选择所有权限。若为普通用户，仅能选择“内发：电子文件-查询”不可更改。发起流程后在8.发放回收和处置中会同步生成记录。

若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

### 6.3.9下载
判断用户是否有文件的下载权限，有下载权限时点击可下载文件。

无下载权限时弹窗提示“无下载权限，是否发起下载申请”。确认后打开下载申请页面，详见发放回收和处置8.4.新增/编辑.文件固定为当前文件不可修改，填写必填内容后发起借阅申请流程。文件管理员可选择所有权限。若为普通用户，仅能选择“外发：电子文件-一次下载”不可更改。发起流程后在8.发放回收和处置中会同步生成记录。

若有多个下载权限（内发：电子文件-查询/下载、外发：电子文件-一次下载、内发：纸质文件-一次下载），弹窗让当前用户选择要下载的选项，一次下载的权限下载后即失效。

### 6.3.10变更记录
点击变更记录显示弹窗，列表显示该文件变化历史列表，列表按照更新日期倒序排列

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">集团文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">所属领域</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发文部门</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">认证方式</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">操作类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">纳入、修订、作废</font> |


## 6.4编号规则
集团库与各个子公司的外部文件库编码各自独立，编码规则为：组织代码+“/”+字典库外部文件类别一级分类代码+“-”+字典库外部文件类别子分类代码+“-”+序号（序号为实位）。

举例：浙江中一检测研究院股份有限公司（ZYZJ），从集团外部文件库中纳入了规范性文件（SG）-技术规范类（JG）-管理（GL）分类下的文件，且是首次导入，那么该文件在纳入至浙江中一的外部文件库时自动生成文件编码ZYZJ/SG-JG-GL-1。

每个公司必须保持同类别中各文件顺序码是连续的，除非某个文件彻底作废了，并且没有新的版次替代他。

若某个分类中有文件作废了或者修改了分类，最新的文件编号需要在文件分类中的最后一位生成，不能插在中间缺失位。

# 7.书籍库
## 7.1权限说明
书籍库各子公司单独管理，数据互相隔离，由各子公司文件管理员进行管理，普通用户根据账号所在组织架构决定显示的数据，仅显示当前账号所在的子公司数据。

文件管理员：新增、导入、导出、编辑、预览、领用、借用、领用归还、借用归还、删除

普通用户：预览、领用、借用、领用归还、借用归还

## 7.2筛选
书籍库顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入书籍编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入书籍名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入作者/编者</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入出版社</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择书籍类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择借用状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">未借用、借用中</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>7.4新增/编辑 |
| <font style="color:rgb(0, 0, 0);">导入</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现导入页面，详见</font>7.5导入 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |




## 7.3表单
表单根据集书籍编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">书籍编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">书籍名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">书籍类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">作者/编者</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">出版社</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">总在册数</font> | <font style="color:rgb(0, 0, 0);">整数</font> | |
| <font style="color:rgb(0, 0, 0);">领用数</font> | <font style="color:rgb(0, 0, 0);">整数+悬浮窗</font> | <font style="color:rgb(0, 0, 0);">显示领用数量，鼠标放在数字上悬浮显示领用人姓名清单</font> |
| <font style="color:rgb(0, 0, 0);">借用数</font> | <font style="color:rgb(0, 0, 0);">整数+悬浮窗</font> | <font style="color:rgb(0, 0, 0);">显示领用数量，鼠标放在数字上悬浮显示借用人姓名清单</font> |
| <font style="color:rgb(0, 0, 0);">借用状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">未借用：借用数为0</font><br/><font style="color:rgb(0, 0, 0);">借用中：借用数不为0</font> |
| <font style="color:rgb(0, 0, 0);">余存数</font> | <font style="color:rgb(0, 0, 0);">整数</font> | <font style="color:rgb(0, 0, 0);">总在册数-领用数-借用数</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">编辑：文件管理员可用</font><br/><font style="color:rgb(0, 0, 0);">预览：所有人可用</font><br/><font style="color:rgb(0, 0, 0);">借用：所有人可用</font><br/><font style="color:rgb(0, 0, 0);">领用归还：所有人可用</font><br/><font style="color:rgb(0, 0, 0);">借用归还：所有人可用</font><br/><font style="color:rgb(0, 0, 0);">删除：文件管理员可用，且只有在领用数和借用数均为0是可用</font> |




## 7.4新增/编辑
拥有新增/编辑权限的用户可以点击新增增加书籍库数据，或点击编辑修改数据，新增和编辑不需要审批。点击按钮出现弹窗，信息如下：



| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">书籍编号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | 编号规则见7.12编号规则 |
| <font style="color:rgb(0, 0, 0);">作者/编者</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">书籍名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">出版社</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | |
| <font style="color:rgb(0, 0, 0);">总在册数</font> | <font style="color:rgb(0, 0, 0);">整数</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> | <font style="color:rgb(0, 0, 0);">只能输入整数，在编辑时，修改的数字不可小于借用数+领用数</font> |
| <font style="color:rgb(0, 0, 0);">书籍类别</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">在“系统管理-字典管理”中新增“书籍库-书籍类别”，根据字典内容显示</font> |
| <font style="color:rgb(0, 0, 0);">上传文件</font> | <font style="color:rgb(0, 0, 0);">文件上传组件</font> | | | | <font style="color:rgb(0, 0, 0);">仅允许上传一个文件，仅允许上传PDF文件</font> |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将新增文件，是否确认？”，点击确认后数据更新至列表</font> |


## 7.5导入
拥有导入权限的用户可以点击导入批量增加书籍库数据，导入不需要审批，导入后生成的文件编号按照台账内的顺序进行生成。点击按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">上传台账-下载模板</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">下载导入模板</font> |
| <font style="color:rgb(0, 0, 0);">上传台账</font> | <font style="color:rgb(0, 0, 0);">文件上传</font> | | | | <font style="color:rgb(0, 0, 0);">仅允许上传一个文件，仅能上传xls/xlsx文件</font> |
| <font style="color:rgb(0, 0, 0);">上传文件</font> | <font style="color:rgb(0, 0, 0);">文件上传</font> | | | | <font style="color:rgb(0, 0, 0);">允许上传多个文件，仅能上传pdf文件</font> |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将新增内部文件，是否确认？”，点击确认后数据更新至列表</font> |


页面提示“提示：台账中的文件名称需和上传文件的名称保持一致，否则会导致导入失败”。

1.若导入的台账清单比上传的文件多或相等，则生成对应的台账数据，且将文件保存到与台账中文件名称相同的附件中

2.若导入的台账清单比上传的文件少，则提示台账与文件不匹配，提交失败

3.若上传的文件名称中有台账的文件名称中不存在的文件，则提示台账与文件不匹配，提交失败。

4.若上传的文件名称中有与现有文件重名的，提示“有文件已存在，导入失败，请检查后重新导入！”

提交成功后发起审批，审批通过后按照导入的台账及文件生成数据。

## 7.6预览
点击预览按钮，可预览书籍附件。

## 7.7领用
点击弹窗显示，需要选择领用原因，填写后发起审批。若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。若已领用，则提示“已领用，请勿重复申请”。

## 7.8借用
点击弹窗显示，需要选择借用原因，填写后发起审批。若当前已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。若已借用，则提示“已借用，请勿重复申请”。

## 7.9领用归还
点击弹窗提示是否确认归还，确认后取消领用权限。

## 7.10借用归还
点击弹窗提示是否确认归还，确认后取消借用权限。

## 7.11删除
点击弹窗提示“是否确认删除，删除后无法恢复，请谨慎操作！”，确认后删除该数据。

## 7.12编号规则
集团库与各个子公司的书籍库编码各自独立，编码规则为：组织代码+“/”+字典库书籍类别一级分类代码+“-”+字典库书籍类别子分类代码+“-”+序号（序号为三位数）。

举例：浙江中一检测研究院股份有限公司（ZYZJ），新增了规范性文件（SG）-技术规范类（JG）-管理（GL）分类下的书籍，且是首次新增，那么该书籍在新增至浙江中一的书籍库时自动生成文件编码ZYZJ/SG-JG-GL-001。

每个公司必须保持同类别中各文件顺序码是连续的，除非某个文件彻底作废了，并且没有新的版次替代他。

若某个分类中有文件作废了或者修改了分类，最新的文件编号需要在文件分类中的最后一位生成，不能插在中间缺失位。

# 8.发放回收和处置
## 8.1权限说明
拥有此页面权限的用户，文件管理员可查看当前子公司所有的发放申请，普通用户只能看见自己的申请。在此页面直接进行发放申请和回收操作。需要注意的是，在内外部文件库对单个文件发起的发放回收申请，需要同步在此表内生成记录。

文件管理员：新增、导出、发放回收详情、纸质文件处置详情、撤销、编辑、回收、纸质文件处置、删除

普通用户：新增、导出、发放回收详情、纸质文件处置详情、撤销、编辑、删除

## 8.2筛选
列表顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">内部文件、外部文件</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择发放类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">内部发放、外部发放</font> |
| <font style="color:rgb(0, 0, 0);">选择编制部门</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">待提交、待审批、已驳回、已审批</font> |
| <font style="color:rgb(0, 0, 0);">输入申请人</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>8.4新增/编辑 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


## 8.3表单
表单根据申请日期倒序进行排序。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">发放类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">希望发放日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放份数</font> | | <font style="color:rgb(0, 0, 0);">人员清单，姓名+已签收/未签收/已回收+电子文件/纸质文件</font> |
| <font style="color:rgb(0, 0, 0);">签收份数</font> | | <font style="color:rgb(0, 0, 0);">人员清单，姓名+电子文件/纸质文件</font> |
| <font style="color:rgb(0, 0, 0);">处置份数</font> | | <font style="color:rgb(0, 0, 0);">人员清单，姓名</font> |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">待提交：新增后暂存的状态，该状态仅申请人自己可见，此状态下，申请人可删除数据</font><br/><font style="color:rgb(0, 0, 0);">待审批：审批中的状态，文件管理员可见所有，申请人仅能见自己的数据</font><br/><font style="color:rgb(0, 0, 0);">已驳回：审批被驳回后的状态，该状态文件管理员可见全部，申请人仅可见自己的数据</font><br/><font style="color:rgb(0, 0, 0);">已审批：审批通过的状态，该状态文件管理员可见全部，申请人仅可见自己的数据</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">发放回收详情：所有状态可用，管理员和发起人可用，详见</font>8.5发放详情<br/><font style="color:rgb(0, 0, 0);">纸质文件处置详情：所有状态可用，管理员和发起人可用，详见</font>8.6纸质文件处置详情<br/><font style="color:rgb(0, 0, 0);">编辑：已驳回和待提交状态可用，仅发起人自己可用，可重新修改数据再次审批</font><br/><font style="color:rgb(0, 0, 0);">撤销：待审批状态可用，仅发起人自己可用，点击后提示是否确认，确认后撤销当前流程，数据状态变为待提交</font><br/><font style="color:rgb(0, 0, 0);">回收：已审批状态可用，仅管理员可用，详见</font>8.7回收<br/><font style="color:rgb(0, 0, 0);">纸质文件处置：已审批状态可用，仅管理员可用，详见</font>8.8纸质文件处置<br/><font style="color:rgb(0, 0, 0);">删除：待提交状态可用，仅发起人自己可用，点击后提示是否确认，确认后删除数据</font> |




## 8.4新增/编辑
拥有新增/编辑权限的用户可以点击新增增加发起发放申请，或点击编辑修改数据，新增和编辑均需要审批。点击按钮出现弹窗，信息如下：



| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | --- |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">内部发放、外部发放</font> |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">内部文件、外部文件</font> |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">根据选择的文件类型显示内容，选择内部文件则为内部文件库字典，外部文件则为外部文件库字典。</font> |
| <font style="color:rgb(0, 0, 0);">发放原因</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | 新员工入职、岗位/职责调整、文件版本更新、新增业务/流程实施、跨部门协作需求、其他 (选择其他需要额外输入文本)。 |
| <font style="color:rgb(0, 0, 0);">希望发放日期</font> | <font style="color:rgb(0, 0, 0);">日期选择</font> | | | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">不填写时，审批通过即发放，填写日期若审批通过后到发放日期再发放权限</font> |
| <font style="color:rgb(0, 0, 0);">发放清单</font> | <font style="color:rgb(0, 0, 0);">表单填写</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">见</font>8.4.1发放清单 |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">暂存</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后保存数据为待提交状态</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将发起审批流程”，点击确认后发起流程</font> |


### 8.4.1发放清单
发放清单中可搜索的文件根据上方所选的文件类型+文件类别，若未选择前两项无法选择文件。若选中的“<font style="color:#000000;">内部发放、纸质文件、一次下载”已有人有此权限，则弹窗提示“提交失败，内发：纸质文件-一次下载当前已有用户有此权限，请先回收权限再发放”。</font>

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">输入搜索选择框</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件名称</font> | <font style="color:rgb(0, 0, 0);">可输入查询选项后选择，与文件编号联动，选择文件名称后自动选择文件编号</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">输入搜索选择框</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件编号</font> | <font style="color:rgb(0, 0, 0);">可输入查询选项后选择，与文件名称联动，选择文件编号后自动选择文件名称</font> |
| <font style="color:rgb(0, 0, 0);">版本版次</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">显示所选文件的版本版次</font> |
| <font style="color:rgb(0, 0, 0);">文件形式</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件形式</font> | <font style="color:rgb(0, 0, 0);">根据上方选择的发放类型显示，若为内部发放，可选择纸质文件、电子文件，若未外部发放，仅能选择电子文件</font> |
| <font style="color:rgb(0, 0, 0);">文件权限</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件权限</font> | <font style="color:rgb(0, 0, 0);">根据选项显示</font><br/><font style="color:rgb(0, 0, 0);">1.选择“内部发放、电子文件”，则可选择“查询、查询/下载”两项</font><br/><font style="color:rgb(0, 0, 0);">2.选择“内部发放、纸质文件”，只能选择“一次下载”</font><br/><font style="color:rgb(0, 0, 0);">3.选择“外部发放、电子文件”，只能选择“一次下载”</font> |
| <font style="color:rgb(0, 0, 0);">接收方</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | <font style="color:rgb(0, 0, 0);">请输入</font> | <font style="color:rgb(0, 0, 0);">当选择的文件权限为外部发放-电子文件-一次下载时，才需要填写此项，否则无需填写</font> |
| <font style="color:rgb(0, 0, 0);">接收人</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择人员</font> | 点击后出现选人组件 |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">增加权限：可对单个文件增加文件形式、文件权限、接收人的选项</font><br/><font style="color:rgb(0, 0, 0);">删除权限：删除单个文件中的文件形式、文件权限、接收人的选项</font><br/><font style="color:rgb(0, 0, 0);">权限至少保留一条</font> |
| <font style="color:rgb(0, 0, 0);">删除</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">删除整个文件的选项数据，文件至少保留一条</font> |


## 8.5发放和回收详情
点击发放详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">状态框</font> | <font style="color:rgb(0, 0, 0);">显示在弹窗标题右侧，三种颜色</font><br/><font style="color:rgb(0, 0, 0);">全部回收：绿色</font><br/><font style="color:rgb(0, 0, 0);">部分回收：黄色</font><br/><font style="color:rgb(0, 0, 0);">均未回收：红色</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发放日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别 </font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发放原因</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">希望发放日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录发放时填写的表单数据，不可操作编辑，表单抬头为：序号、文件名称、文件编号、版本/版次、内发：电子文件-查询、内发：电子文件-查询/下载、内发：纸质文件-一次下载、外发：电子文件-一次下载、操作（按钮，点击可查看回收记录，详见</font>8.9回收记录<font style="color:rgb(0, 0, 0);">）</font><br/><font style="color:rgb(0, 0, 0);">三个内发权限内显示人员名称，若为已回收数据则用蓝字+下划线标识，鼠标悬停显示回收日期，外发权限后跟括号，显示提交时填写的接收方</font> |
| <font style="color:rgb(0, 0, 0);">审批记录</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">按顺序正序显示审批人和审批时间，最后一个审批人叫批准人</font> |


## 8.6纸质文件处置详情
点击发放详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">状态框</font> | <font style="color:rgb(0, 0, 0);">显示在弹窗标题右侧，三种颜色</font><br/><font style="color:rgb(0, 0, 0);">全部处置：绿色</font><br/><font style="color:rgb(0, 0, 0);">部分处置：黄色</font><br/><font style="color:rgb(0, 0, 0);">均未处置：红色</font> |
| <font style="color:rgb(0, 0, 0);">发放人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发放日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别 </font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发放原因</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">希望发放日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录发放时内部发放的纸质文件，不可操作编辑，表单抬头为：序号、文件名称、文件编号、版本/版次、文件形式、发放份数、回收份数、处置份数、交还人（显示人员名称，若为已处置数据则用蓝字+下划线标识，鼠标悬停显示处置日期）、操作（按钮，点击可查看处置记录，详见</font>8.10处置记录） |
| <font style="color:rgb(0, 0, 0);">审批记录</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">按顺序正序显示审批人和审批时间，最后一个审批人叫批准人</font> |


## 8.7回收
拥有回收权限的用户可以点击回收，对单条发放申请中发放的数据进行回收，回收需要审批。点击按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | --- |
| <font style="color:rgb(0, 0, 0);">回收人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">回收日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放类型</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">文件类别 </font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">发放原因</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">回收原因</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | 员工离职/调离、岗位/职责调整、文件版本更新、文件正式作废、文件过期失效、错误发放纠正、定期清理、其他 (选择其他需要额外输入文本)。 |
| <font style="color:rgb(0, 0, 0);">发放清单</font> | <font style="color:rgb(0, 0, 0);">表单填写</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">见</font>8.7.1发放清单 |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将发起审批流程”，点击确认后发起流程</font> |


### 8.7.1发放清单
发放清单显示数据回收状态，可操作回收。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">选择</font> | <font style="color:rgb(0, 0, 0);">复选框</font> | <font style="color:rgb(0, 0, 0);">选中标题栏的复选框，可选中所有未被勾选项</font><br/><font style="color:rgb(0, 0, 0);">选中单个文件的复选框，可选中该文件中所有未被勾选项</font> |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询</font> | <font style="color:rgb(0, 0, 0);">复选框+姓名</font> | <font style="color:rgb(0, 0, 0);">显示该次发放申请发放的所有人员姓名，共三种状态</font><br/><font style="color:rgb(0, 0, 0);">未勾选：该人员未回收，可选中数据</font><br/><font style="color:rgb(0, 0, 0);">未勾选置灰：该人员未回收，但已发起回收流程，不可用</font><br/><font style="color:rgb(0, 0, 0);">已勾选置灰：该人员已回收</font> |
| <font style="color:rgb(0, 0, 0);">内发：电子文件-查询/下载</font> | <font style="color:rgb(0, 0, 0);">复选框+姓名</font> | <font style="color:rgb(0, 0, 0);">显示该次发放申请发放的所有人员姓名，共三种状态</font><br/><font style="color:rgb(0, 0, 0);">未勾选：该人员未回收，可选中数据</font><br/><font style="color:rgb(0, 0, 0);">未勾选置灰：该人员未回收，但已发起回收流程，不可用</font><br/><font style="color:rgb(0, 0, 0);">已勾选置灰：该人员已回收</font> |
| <font style="color:rgb(0, 0, 0);">内发：纸质文件-一次下载</font> | <font style="color:rgb(0, 0, 0);">复选框+姓名</font> | <font style="color:rgb(0, 0, 0);">显示该次发放申请发放的所有人员姓名，共三种状态</font><br/><font style="color:rgb(0, 0, 0);">未勾选：该人员未回收，可选中数据</font><br/><font style="color:rgb(0, 0, 0);">未勾选置灰：该人员未回收，但已发起回收流程，不可用</font><br/><font style="color:rgb(0, 0, 0);">已勾选置灰：该人员已回收</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">回收记录：点击出现弹窗，详见</font>8.9回收记录 |


## 8.8纸质文件处置
拥有纸质文件处置权限的用户可以点击纸质文件处置，对单条发放申请中发放的数据进行纸质文件处置，纸质文件处置需要审批。点击按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | --- |
| <font style="color:rgb(0, 0, 0);">处置人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">处置日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">发放类型</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">文件类别 </font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">发放原因</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">处置方式</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请输入</font> |  |
| <font style="color:rgb(0, 0, 0);">纸质文件处置清单</font> | <font style="color:rgb(0, 0, 0);">表单填写</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">见</font>8.8.1纸质文件处置清单 |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将发起审批流程”，点击确认后发起流程</font> |


### 8.8.1纸质文件处置清单
纸质文件处置清单显示处置状态，可操作选人。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">选择</font> | <font style="color:rgb(0, 0, 0);">复选框</font> | <font style="color:rgb(0, 0, 0);">选中标题栏的复选框，可选中所有未被勾选项</font><br/><font style="color:rgb(0, 0, 0);">选中单个文件的复选框，可选中该文件中所有未被勾选项</font> |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件形式</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">发放份数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | |
| <font style="color:rgb(0, 0, 0);">回收份数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | |
| <font style="color:rgb(0, 0, 0);">处置份数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | |
| <font style="color:rgb(0, 0, 0);">交还人</font> | <font style="color:rgb(0, 0, 0);">复选框+姓名</font> | <font style="color:rgb(0, 0, 0);">显示该次发放申请已交还纸质文件的人员姓名，共三种状态</font><br/><font style="color:rgb(0, 0, 0);">未勾选：该人员未处置，可选中数据</font><br/><font style="color:rgb(0, 0, 0);">未勾选置灰：该人员未处置，但已发起处置流程，不可用</font><br/><font style="color:rgb(0, 0, 0);">已勾选置灰：该人员已处置</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">处置记录：点击出现弹窗，详见</font>8.10处置记录 |


## 8.9回收记录
弹窗显示该次发放文件的回收记录，详情如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">回收记录</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">表单记录每一次回收详情，表单抬头为：序号、回收发起人、回收原因、交还人-内发：电子文件-查询、交还人-内发：电子文件-查询/下载、交还人-内发：纸质文件-一次下载、审批人、批准人、回收日期（审批通过时间）</font> |


## 8.10处置记录
弹窗显示该次发放文件的处置记录，详情如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">回收记录</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">表单记录每一次处置详情，表单抬头为：序号、交还人、交还日期、回收人（发起回收的人员姓名，若为作废，显示发起作废的人员姓名）、回收日期（发起回收的日期）、处置人（发起处置申请的人员姓名）、处置日期（发起处置的日期）、处置方式</font> |


# 9.借阅和交还
## 9.1权限说明
拥有此页面权限的用户，文件管理员可查看当前子公司所有的申请，普通用户只能看见自己的申请。在此页面直接进行借阅申请和回收操作。

文件管理员：新增、导出、借阅详情、撤销、编辑、回收、删除

普通用户：新增、导出、借阅详情、撤销、编辑、删除

## 9.2筛选
列表顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">内部文件、外部文件</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">待提交、待审批、已驳回、已审批</font> |
| <font style="color:rgb(0, 0, 0);">输入申请人</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>9.4新增/编辑 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


## 9.3表单
表单根据申请日期倒序进行排序。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">借阅文件数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | <font style="color:rgb(0, 0, 0);">显示借阅总数</font> |
| <font style="color:rgb(0, 0, 0);">交还文件数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | <font style="color:rgb(0, 0, 0);">显示已通过回收审批的文件总数</font> |
| <font style="color:rgb(0, 0, 0);">借阅期限</font> | <font style="color:rgb(0, 0, 0);">日期区间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD至YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">回收人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">待提交：新增后暂存的状态，该状态仅申请人自己可见，此状态下，申请人可删除数据</font><br/><font style="color:rgb(0, 0, 0);">待审批：审批中的状态，文件管理员可见所有，申请人仅能见自己的数据</font><br/><font style="color:rgb(0, 0, 0);">已驳回：审批被驳回后的状态，该状态文件管理员可见全部，申请人仅可见自己的数据</font><br/><font style="color:rgb(0, 0, 0);">已审批：审批通过的状态，该状态文件管理员可见全部，申请人仅可见自己的数据</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">借阅详情：所有状态可用，管理员和发起人可用，详见</font>9.5借阅详情<br/><font style="color:rgb(0, 0, 0);">编辑：已驳回和待提交状态可用，仅发起人自己可用，可重新修改数据再次审批</font><br/><font style="color:rgb(0, 0, 0);">撤销：待审批状态可用，仅发起人自己可用，点击后提示是否确认，确认后撤销当前流程，数据状态变为待提交</font><br/><font style="color:rgb(0, 0, 0);">回收：已审批状态可用，仅管理员可用，详见</font>9.6回收<br/><font style="color:rgb(0, 0, 0);">删除：待提交状态可用，仅发起人自己可用，点击后提示是否确认，确认后删除数据</font> |




## 9.4新增/编辑
拥有新增/编辑权限的用户可以点击新增增加发起发放申请，或点击编辑修改数据，新增和编辑均需要审批。点击按钮出现弹窗，至少需要一条数据才可以提交，信息如下：



| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | --- |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">借阅日期</font> | <font style="color:rgb(0, 0, 0);">日期区间选择</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">选择开始日期-结束日期</font> |
| <font style="color:rgb(0, 0, 0);">借阅原因</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | 项目参考/研究、问题调查/分析、审计/检查准备、培训/学习需要、其他 (选择其他需要额外输入文本)。 |
| <font style="color:rgb(0, 0, 0);">借阅清单</font> | <font style="color:rgb(0, 0, 0);">表单填写</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">见</font>9.4.1借阅清单 |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">暂存</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后保存数据为待提交状态</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将发起审批流程”，点击确认后发起流程</font> |


### 9.4.1借阅清单
借阅清单中可搜索内外部文件库及作废文件库中的文件，所有选项均有联动，根据前面的选项确定后面选择框内数据。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | | | | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件有效性</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择有效性</font> | <font style="color:rgb(0, 0, 0);">即将作废：文件库中搜索</font><br/><font style="color:rgb(0, 0, 0);">即将实施：文件库中搜索</font><br/><font style="color:rgb(0, 0, 0);">有效：文件库中搜索</font><br/><font style="color:rgb(0, 0, 0);">拟修订：文件库中搜索</font><br/><font style="color:rgb(0, 0, 0);">作废：作废文件库中搜索</font> |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件类型</font> | <font style="color:rgb(0, 0, 0);">内部文件、外部文件</font> |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">根据选择的文件类型显示内容，选择内部文件则为内部文件库字典，外部文件则为外部文件库字典。</font> |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">输入搜索选择框</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件名称</font> | <font style="color:rgb(0, 0, 0);">可输入查询选项后选择，与文件编号联动，选择文件名称后自动选择文件编号</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">输入搜索选择框</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件编号</font> | <font style="color:rgb(0, 0, 0);">可输入查询选项后选择，与文件名称联动，选择文件编号后自动选择文件名称</font> |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">自动生成/下拉单选</font> | | | | <font style="color:rgb(0, 0, 0);">若文件有效性选择的有效，根据所选文件自动生成</font><br/><font style="color:rgb(0, 0, 0);">若文件有效性选择的作废，需要选择该文件的版本/版次</font> |
| <font style="color:rgb(0, 0, 0);">删除</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">删除整个文件的选项数据，文件至少保留一条</font> |
| <font style="color:rgb(0, 0, 0);">增加文件</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后增加一条数据</font> |


## 9.5借阅详情
点击借阅详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">状态框</font> | <font style="color:rgb(0, 0, 0);">显示在弹窗标题右侧，三种颜色</font><br/><font style="color:rgb(0, 0, 0);">全部回收：绿色</font><br/><font style="color:rgb(0, 0, 0);">部分回收：黄色</font><br/><font style="color:rgb(0, 0, 0);">均未回收：红色</font> |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">借阅日期</font> | <font style="color:rgb(0, 0, 0);">日期区间</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD至YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">借阅原因</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">借阅清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录发放时填写的表单数据，不可操作编辑，表单抬头为：序号、文件有效性、文件类型、文件类别、文件名称、文件编号、版本/版次、状态（已回收、借阅中、回收中）</font> |
| <font style="color:rgb(0, 0, 0);">审批记录</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">按顺序正序显示审批人和审批时间，最后一个审批人叫批准人</font> |


## 9.6回收
拥有回收权限的用户可以点击回收，对单条借阅申请中发放的数据进行回收，回收需要审批。点击按钮出现弹窗，信息如下：



| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">回收人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">回收日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">借阅原因</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">借阅清单</font> | <font style="color:rgb(0, 0, 0);">表单填写</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">记录发放时填写的表单数据，表单抬头为：序号、文件有效性、文件类型、文件类别、文件名称、文件编号、版本/版次、状态（已回收、借阅中、回收中）、操作（状态为借阅中时可点击回收按钮进行回收，回收需二次确认发起审批，发起审批后状态变为回收中）</font> |


# 10.作废申请
## 10.1权限说明
此页面仅文件管理员可用。

## 10.2筛选
列表顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">内部文件、外部文件</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">待提交、待审批、已驳回、已审批</font> |
| <font style="color:rgb(0, 0, 0);">输入申请人</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>10.4新增/编辑 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


## 10.3表单
表单根据申请日期倒序进行排序。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">作废文件数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | <font style="color:rgb(0, 0, 0);">显示已通过作废审批的文件总数</font> |
| <font style="color:rgb(0, 0, 0);">拟定作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">审核人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD，如有多条分行显示</font> |
| <font style="color:rgb(0, 0, 0);">批准人</font> | <font style="color:rgb(0, 0, 0);">姓名+日期</font> | <font style="color:rgb(0, 0, 0);">显示姓名+日期，日期格式YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">状态</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">待提交：新增后暂存的状态，此状态下，申请人可删除数据</font><br/><font style="color:rgb(0, 0, 0);">待审批：审批中的状态</font><br/><font style="color:rgb(0, 0, 0);">已驳回：审批被驳回后的状态</font><br/><font style="color:rgb(0, 0, 0);">已审批：审批通过的状态</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">详情：所有状态可用，详见</font>10.5详情<br/><font style="color:rgb(0, 0, 0);">编辑：已驳回和待提交状态可用，仅发起人自己可用，可重新修改数据再次审批</font><br/><font style="color:rgb(0, 0, 0);">撤销：待审批状态可用，仅发起人自己可用，点击后提示是否确认，确认后撤销当前流程，数据状态变为待提交</font><br/><font style="color:rgb(0, 0, 0);">删除：待提交状态可用，仅发起人自己可用，点击后提示是否确认，确认后删除数据</font> |




## 10.4新增/编辑
拥有新增/编辑权限的用户可以点击新增增加发起发放申请，或点击编辑修改数据，新增和编辑均需要审批。点击按钮出现弹窗，至少需要一条数据才可以提交，信息如下：



| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | --- |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">拟定作废日期</font> | <font style="color:rgb(0, 0, 0);">日期选择</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择日期</font> | <font style="color:rgb(0, 0, 0);">选择拟定作废日期且审批通过后，在作废日期到达时，原文件会作废，若通过审批时已过拟定作废日期，那么文件直接作废</font> |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">内部文件、外部文件</font> |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | <font style="color:rgb(0, 0, 0);">根据选择的文件类型显示内容，选择内部文件则为内部文件库字典，外部文件则为外部文件库字典。</font> |
| <font style="color:rgb(0, 0, 0);">作废原因</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">请选择</font> | 版本更新替代、内容错误/失效、业务/流程不适用、相关标准/法规废止、业务/流程终止、组织架构变更、技术/设备淘汰、合并/整合、其他 (选择其他需要额外输入文本)。 |
| <font style="color:rgb(0, 0, 0);">作废清单</font> | <font style="color:rgb(0, 0, 0);">表单填写</font> | | <font style="color:rgb(0, 0, 0);">√</font> | | <font style="color:rgb(0, 0, 0);">见</font>10.4.1作废清单 |
| <font style="color:rgb(0, 0, 0);">取消</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后关闭弹窗且不保存弹窗内编辑数据</font> |
| <font style="color:rgb(0, 0, 0);">暂存</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后保存数据为待提交状态</font> |
| <font style="color:rgb(0, 0, 0);">提交</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后提示“确认后将发起审批流程”，点击确认后发起流程</font> |


### 10.4.1作废清单
作废清单中可搜索内外部文件库中的文件，所有选项均有联动，根据前面的选项确定后面选择框内数据。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | | | | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">输入搜索选择框</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件名称</font> | <font style="color:rgb(0, 0, 0);">可输入查询选项后选择，与文件编号联动，选择文件名称后自动选择文件编号</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">输入搜索选择框</font> | | <font style="color:rgb(0, 0, 0);">√</font> | <font style="color:rgb(0, 0, 0);">选择文件编号</font> | <font style="color:rgb(0, 0, 0);">可输入查询选项后选择，与文件名称联动，选择文件编号后自动选择文件名称</font> |
| <font style="color:rgb(0, 0, 0);">版本/版次</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> | | | | <font style="color:rgb(0, 0, 0);">根据所选文件自动生成</font> |
| <font style="color:rgb(0, 0, 0);">删除</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">删除整个文件的选项数据，文件至少保留一条</font> |
| <font style="color:rgb(0, 0, 0);">增加文件</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后增加一条数据</font> |


## 10.5详情
点击详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">申请人</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">申请日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">拟定作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD</font> |
| <font style="color:rgb(0, 0, 0);">文件类型</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">作废原因</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">作废清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录发放时填写的表单数据，不可操作编辑，表单抬头为：序号、文件名称、文件编号、版本/版次、状态</font> |
| <font style="color:rgb(0, 0, 0);">审批记录</font> | <font style="color:rgb(0, 0, 0);">文本</font> | <font style="color:rgb(0, 0, 0);">按顺序正序显示审批人和审批时间，最后一个审批人叫批准人</font> |


# 11.内部作废文件库
## 11.1权限说明
内部作废文件库记录各子公司作废文件，数据各自独立，各用户可见自己所属机构的数据。

## 11.2筛选
顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择编制部门</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择状态</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">即将作废、即将实施、有效、拟修订</font> |
| <font style="color:rgb(0, 0, 0);">是否有附件</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">是、否</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">新增</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现新增页面，详见</font>5.4新增/修订 |
| <font style="color:rgb(0, 0, 0);">导入</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮出现导入页面，详见</font>5.5导入 |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


## 11.3表单
表单根据集团文件编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">首次发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的发布日期</font> |
| <font style="color:rgb(0, 0, 0);">首次实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">最后作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最晚的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">作废版本数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | <font style="color:rgb(0, 0, 0);">统计作废的数量</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">借阅：子公司文件管理员、子公司普通用户可见</font><br/><font style="color:rgb(0, 0, 0);">作废详情：子公司文件管理员可见</font> |


## 11.4借阅
点击借阅后，弹窗显示该作废文件中的所有版本/版次号

判断用户是否有版本/版次的借阅权限，有借阅权限时点击可预览文件。

无借阅权限时弹窗提示“无借阅权限，是否发起借阅申请”。确认后需要填写借阅日期（区间），借阅原因，填写后发起审批。发起流程后在9.借阅和交还中会同步生成记录。

若当前选择的版本/版次已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

## 11.5作废详情
点击作废详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">首次发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的发布日期</font> |
| <font style="color:rgb(0, 0, 0);">首次实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">最后作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最晚的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">作废清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录作废文件的每个版本.版次详情，不可操作编辑，表单抬头为：序号、文件编号、版本/版次、原文件编号、原版本/版次、文件名称、文件类别、编制部门、编制人、审核人、批准人、发布日期、实施日期、操作（审批记录，点击查看该数据的作废审批表单，详见10.作废申请中的</font>10.5详情<font style="color:rgb(0, 0, 0);">）</font> |




# 12.外部作废文件库
## 12.1权限说明
外部作废文件库分为集团库和公司库，集团库仅集团账号权限和各子公司管理员可见，公司库仅各子公司账号可见，普通用户根据账号所在组织架构决定显示的数据，仅显示当前账号所在的子公司数据。

集团文件管理员：只有集团库

集团普通用户：只有集团库

子公司文件管理员：可查看集团库和公司库

子公司普通用户：只有公司库

## 12.2集团库
### 12.2.1筛选
顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入集团文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择所属领域</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">输入原文件号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文部门</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择认证方式</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">是否有附件</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">是、否</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


### 12.2.2表单
表单根据集团文件编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">集团文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">首次发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的发布日期</font> |
| <font style="color:rgb(0, 0, 0);">首次实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">最后作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最晚的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">作废版本数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | <font style="color:rgb(0, 0, 0);">统计作废的数量</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">借阅：集团文件管理员、集团普通用户、子公司文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">作废详情：集团文件管理员可见</font> |


### 12.2.3借阅
点击借阅后，弹窗显示该作废文件中的所有版本/版次号

判断用户是否有版本/版次的借阅权限，有借阅权限时点击可预览文件。

无借阅权限时弹窗提示“无借阅权限，是否发起借阅申请”。确认后需要填写借阅日期（区间），借阅原因，填写后发起审批。发起流程后在9.借阅和交还中会同步生成记录。

若当前选择的版本/版次已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

### 12.2.4作废详情
点击作废详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">首次发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的发布日期</font> |
| <font style="color:rgb(0, 0, 0);">首次实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">最后作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最晚的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">作废清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录作废文件的每个版本.版次详情，不可操作编辑，表单抬头为：序号、集团文件编号、版本/版次、原文件编号、原版本/版次、文件名称、文件类别、所属领域、原文件号、发文号、发文部门、审核人、批准人、发布日期、实施日期、作废日期、认证方式、操作（审批记录，点击查看该数据的作废审批表单，详见10.作废申请中的</font>10.5详情<font style="color:rgb(0, 0, 0);">）</font> |




## 12.3公司库
### 12.3.1筛选
顶部为筛选栏和操作按钮。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">长度</font>** | **<font style="color:rgb(0, 0, 0);">必填</font>** | **<font style="color:rgb(0, 0, 0);">默认值</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">输入文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | <font style="color:rgb(0, 0, 0);">　</font> | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入文件名称</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入原文件编号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择文件类别</font> | <font style="color:rgb(0, 0, 0);">树状-多选</font> | | | | <font style="color:rgb(0, 0, 0);">选择上级默认全选该项子级，可多选，可点击取消选择</font> |
| <font style="color:rgb(0, 0, 0);">选择所属领域</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">输入原文件号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文号</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">输入发文部门</font> | <font style="color:rgb(0, 0, 0);">文本输入</font> | | | | <font style="color:rgb(0, 0, 0);">模糊搜索</font> |
| <font style="color:rgb(0, 0, 0);">选择认证方式</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | |
| <font style="color:rgb(0, 0, 0);">是否有附件</font> | <font style="color:rgb(0, 0, 0);">下拉-单选</font> | | | | <font style="color:rgb(0, 0, 0);">是、否</font> |
| <font style="color:rgb(0, 0, 0);">查询</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击后根据所选条件进行查询</font> |
| <font style="color:rgb(0, 0, 0);">重置</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">重置查询条件并恢复为默认值</font> |
| <font style="color:rgb(0, 0, 0);">导出</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | | | | <font style="color:rgb(0, 0, 0);">点击按钮导出当前筛选出的台账数据，导出表单字段除“操作”列之外的所有数据</font> |


### 12.3.2表单
表单根据集团文件编号进行排序，排序规则为英文正序+数字正序排列。英文>数字。

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">序号</font> | <font style="color:rgb(0, 0, 0);">自动生成</font> |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">原文件号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">首次发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的发布日期</font> |
| <font style="color:rgb(0, 0, 0);">首次实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">最后作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最晚的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">作废版本数</font> | <font style="color:rgb(0, 0, 0);">数字</font> | <font style="color:rgb(0, 0, 0);">统计作废的数量</font> |
| <font style="color:rgb(0, 0, 0);">操作</font> | <font style="color:rgb(0, 0, 0);">按钮</font> | <font style="color:rgb(0, 0, 0);">借阅：子公司普通用户、子公司文件管理员可见</font><br/><font style="color:rgb(0, 0, 0);">作废详情：子公司文件管理员可见</font> |


### 12.3.3借阅
点击借阅后，弹窗显示该作废文件中的所有版本/版次号

判断用户是否有版本/版次的借阅权限，有借阅权限时点击可预览文件。

无借阅权限时弹窗提示“无借阅权限，是否发起借阅申请”。确认后需要填写借阅日期（区间），借阅原因，填写后发起审批。发起流程后在9.借阅和交还中会同步生成记录。

若当前选择的版本/版次已有审批流程，则弹窗提示“当前已有申请流程，请在流程完成后重试”。

### 12.3.4作废详情
点击作废详情按钮出现弹窗，信息如下：

| **<font style="color:rgb(0, 0, 0);">名称</font>** | **<font style="color:rgb(0, 0, 0);">类型</font>** | **<font style="color:rgb(0, 0, 0);">规则</font>** |
| :--- | :--- | :--- |
| <font style="color:rgb(0, 0, 0);">文件编号</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件名称</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">文件类别</font> | <font style="color:rgb(0, 0, 0);">文本</font> | |
| <font style="color:rgb(0, 0, 0);">首次发布日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的发布日期</font> |
| <font style="color:rgb(0, 0, 0);">首次实施日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最早的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">最后作废日期</font> | <font style="color:rgb(0, 0, 0);">日期</font> | <font style="color:rgb(0, 0, 0);">YYYY-MM-DD所有版本/版次中最晚的实施日期</font> |
| <font style="color:rgb(0, 0, 0);">作废清单</font> | <font style="color:rgb(0, 0, 0);">表单</font> | <font style="color:rgb(0, 0, 0);">记录作废文件的每个版本.版次详情，不可操作编辑，表单抬头为：序号、集团文件编号、版本/版次、原文件编号、原版本/版次、文件名称、文件类别、所属领域、原文件号、发文号、发文部门、审核人、批准人、发布日期、实施日期、作废日期、认证方式、操作（审批记录，点击查看该数据的作废审批表单，详见10.作废申请中的</font>10.5详情<font style="color:rgb(0, 0, 0);">）</font> |








