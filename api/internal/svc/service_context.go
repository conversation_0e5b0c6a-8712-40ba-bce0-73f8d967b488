package svc

import (
	"docvault/api/internal/config"
	"docvault/api/internal/middleware"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type ServiceContext struct {
	ConfigManager     gzconfigcenter.ConfigManager[config.Config]
	TraceMiddleware   rest.Middleware
	SessionMiddleware rest.Middleware
	DocvaultRpcConn   *grpc.ClientConn
}

func NewServiceContext(c gzconfigcenter.ConfigManager[config.Config]) *ServiceContext {
	return &ServiceContext{
		ConfigManager:     c,
		TraceMiddleware:   middleware.NewTraceMiddleware().Handle,
		SessionMiddleware: middleware.NewSessionMiddleware().Handle,
		DocvaultRpcConn:   zrpc.MustNewClient(c.GetConfig().DocvaultRPC).Conn(),
	}
}
