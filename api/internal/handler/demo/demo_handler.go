package demo

import (
	"net/http"

	"docvault/api/internal/logic/demo"
	"docvault/api/internal/respx"
	"docvault/api/internal/svc"
	"docvault/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func DemoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DemoReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := demo.NewDemoLogic(r.Context(), svcCtx)
		resp, err := l.Demo(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
