// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	demo "docvault/api/internal/handler/demo"
	"docvault/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.SessionMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/demoDemo",
					Handler: demo.DemoHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/docvault/api/v1"),
	)
}
