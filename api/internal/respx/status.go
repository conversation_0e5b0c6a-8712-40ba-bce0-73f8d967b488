package respx

import (
	"net/http"
)

var (
	OK                       = New(0, http.StatusOK, "操作成功")
	ServerError              = New(500, http.StatusOK, "服务器内部错误")
	ParamError               = New(400, http.StatusBadRequest, "请检查填写信息是否正确且完整")
	ServiceDiscoverFailError = New(400, http.StatusOK, "服务发现失败")
	HttpParseFailErr         = New(400, http.StatusOK, "http请求解析失败")
	TokenError               = New(200, http.StatusUnauthorized, "Token验证失败")
	UserNotExist             = New(55555, http.StatusOK, "用户不存在")
)
