package middleware

import (
	"context"
	"docvault/utils"
	"net/http"
	"strings"
)

const (
	SessionPrefix = "X-Session-"
)

type SessionMiddleware struct {
}

func NewSessionMiddleware() *SessionMiddleware {
	return &SessionMiddleware{}
}

func (m *SessionMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 遍历header
		ctx := r.Context()
		for key, values := range r.Header {
			if strings.HasPrefix(key, SessionPrefix) && len(values) > 0 {
				sessionKey := strings.TrimPrefix(key, SessionPrefix)
				ctx = context.WithValue(ctx, utils.KebabTo<PERSON>amel(sessionKey), values[0])
			}
		}
		r = r.WithContext(ctx)
		next(w, r)
	}
}
