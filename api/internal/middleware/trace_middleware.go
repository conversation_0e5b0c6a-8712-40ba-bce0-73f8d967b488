package middleware

import (
	"context"
	"net/http"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

type TraceMiddleware struct {
}

func NewTraceMiddleware() *TraceMiddleware {
	return &TraceMiddleware{}
}

func (m *TraceMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 创建traceId
		reqCtx := r.Context()

		// traceId存入ctx和日志
		traceId := uuid.New().String()
		ctx := logx.ContextWithFields(reqCtx, logx.Field("traceId", traceId))

		newReq := r.WithContext(context.WithValue(ctx, "traceId", traceId))
		next(w, newReq)
		// traceId写入响应头
		w.Header().Set("Trace-Id", traceId)
	}
}
