package main

import (
	"docvault/api/internal/config"
	"docvault/api/internal/handler"
	"docvault/api/internal/respx"
	"docvault/api/internal/svc"
	"flag"
	"fmt"

	"gitee.com/damengde/gzconfigcenter"

	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

var configFile = flag.String("f", "etc/docvault.yaml", "the config file")
var etcdEndpoints = flag.String("etcd", "", "etcd endpoints")
var etcdUsername = flag.String("etcd-user", "", "etcd username")
var etcdPassword = flag.String("etcd-pass", "", "etcd password")
var configKey = flag.String("config-key", "/services/config/docvaultapi/default", "config key")

func main() {
	flag.Parse()
	// 创建配置管理器
	configManager, err := gzconfigcenter.NewConfigManager[config.Config](gzconfigcenter.EtcdConfig{
		Endpoints: gzconfigcenter.SplitStringIgnoreEmpty(*etcdEndpoints, ","),
		Username:  *etcdUsername,
		Password:  *etcdPassword,
		ConfigKey: *configKey,
	}, *configFile)
	if err != nil {
		panic(fmt.Sprintf("create config manager error: %v", err))
	}

	svcCtx := svc.NewServiceContext(configManager)
	server := rest.MustNewServer(configManager.GetConfig().RestConf)
	defer server.Stop()

	handler.RegisterHandlers(server, svcCtx)

	httpx.SetErrorHandlerCtx(respx.ErrHandler)

	fmt.Printf("Starting server at %s:%d...\n", configManager.GetConfig().Host, configManager.GetConfig().Port)
	server.Start()
}
