// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package documentlibrary

import (
	"context"

	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	ApprovalInfo                    = docvault.ApprovalInfo
	ApprovalInfoItem                = docvault.ApprovalInfoItem
	BookInfo                        = docvault.BookInfo
	CreateBookReq                   = docvault.CreateBookReq
	CreateBookResp                  = docvault.CreateBookResp
	DeleteBookReq                   = docvault.DeleteBookReq
	DeleteBookResp                  = docvault.DeleteBookResp
	DisposalApprovalInfo            = docvault.DisposalApprovalInfo
	DisposalList                    = docvault.DisposalList
	DistributeApprovalReq           = docvault.DistributeApprovalReq
	DistributeInventory             = docvault.DistributeInventory
	DistributeList                  = docvault.DistributeList
	DistributeUser                  = docvault.DistributeUser
	DocumentDistributeReq           = docvault.DocumentDistributeReq
	EmptyResp                       = docvault.EmptyResp
	ExternalDocumentChangeReq       = docvault.ExternalDocumentChangeReq
	ExternalDocumentCreateInfo      = docvault.ExternalDocumentCreateInfo
	ExternalDocumentCreateReq       = docvault.ExternalDocumentCreateReq
	ExternalDocumentCreateResp      = docvault.ExternalDocumentCreateResp
	ExternalDocumentCreateRespList  = docvault.ExternalDocumentCreateRespList
	ExternalDocumentGetReq          = docvault.ExternalDocumentGetReq
	ExternalDocumentGetResp         = docvault.ExternalDocumentGetResp
	ExternalDocumentPageInfo        = docvault.ExternalDocumentPageInfo
	ExternalDocumentPageReq         = docvault.ExternalDocumentPageReq
	ExternalDocumentPageResp        = docvault.ExternalDocumentPageResp
	FilePermission                  = docvault.FilePermission
	GetBookListReq                  = docvault.GetBookListReq
	GetBookListResp                 = docvault.GetBookListResp
	GetDistributeApplicationReq     = docvault.GetDistributeApplicationReq
	GetDistributeApplicationResp    = docvault.GetDistributeApplicationResp
	GetDistributeListInfo           = docvault.GetDistributeListInfo
	GetDistributeListReq            = docvault.GetDistributeListReq
	GetDistributeListResp           = docvault.GetDistributeListResp
	GetDocPermissionUsersReq        = docvault.GetDocPermissionUsersReq
	GetDocPermissionUsersResp       = docvault.GetDocPermissionUsersResp
	ImportBookInfo                  = docvault.ImportBookInfo
	ImportBookReq                   = docvault.ImportBookReq
	ImportBookResp                  = docvault.ImportBookResp
	ImportGroupDocsToCompanyInfo    = docvault.ImportGroupDocsToCompanyInfo
	ImportGroupDocsToCompanyReq     = docvault.ImportGroupDocsToCompanyReq
	ImportGroupDocsToCompanyResp    = docvault.ImportGroupDocsToCompanyResp
	ImportRespInfo                  = docvault.ImportRespInfo
	InternalDocumentBatchCreateReq  = docvault.InternalDocumentBatchCreateReq
	InternalDocumentBatchCreateResp = docvault.InternalDocumentBatchCreateResp
	InternalDocumentChangeReq       = docvault.InternalDocumentChangeReq
	InternalDocumentCreateReq       = docvault.InternalDocumentCreateReq
	InternalDocumentCreateResp      = docvault.InternalDocumentCreateResp
	InternalDocumentGetReq          = docvault.InternalDocumentGetReq
	InternalDocumentGetResp         = docvault.InternalDocumentGetResp
	InternalDocumentPageItem        = docvault.InternalDocumentPageItem
	InternalDocumentPageReq         = docvault.InternalDocumentPageReq
	InternalDocumentPageResp        = docvault.InternalDocumentPageResp
	PageInfo                        = docvault.PageInfo
	Permission                      = docvault.Permission
	PermissionResp                  = docvault.PermissionResp
	PlagiarismCheckReq              = docvault.PlagiarismCheckReq
	ReceivedBy                      = docvault.ReceivedBy
	Recipient                       = docvault.Recipient
	RecycleApprovalInfo             = docvault.RecycleApprovalInfo
	RecycleList                     = docvault.RecycleList
	UpdateBookResp                  = docvault.UpdateBookResp
	UpdateDistributeStatusReq       = docvault.UpdateDistributeStatusReq

	DocumentLibrary interface {
		// 保存发放记录
		PreSaveDistributeRecord(ctx context.Context, in *DocumentDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 获取文档权限用户
		GetDocPermissionUsers(ctx context.Context, in *GetDocPermissionUsersReq, opts ...grpc.CallOption) (*GetDocPermissionUsersResp, error)
		// 更新发放状态
		UpdateDistributeStatusByWorkflowId(ctx context.Context, in *UpdateDistributeStatusReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 保存发放审批信息
		SaveDistributeApproval(ctx context.Context, in *DistributeApprovalReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 保存回收审批信息
		SaveRecycleApprovalInfo(ctx context.Context, in *RecycleApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error)
		// 保存处置审批信息
		SaveDisposalApprovalInfo(ctx context.Context, in *DisposalApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error)
		// 获取发放信息列表
		GetDistributeInfoList(ctx context.Context, in *GetDistributeListReq, opts ...grpc.CallOption) (*GetDistributeListResp, error)
		// 根据发放列表id获取发放清单信息
		GetDistributeApplicationById(ctx context.Context, in *GetDistributeApplicationReq, opts ...grpc.CallOption) (*GetDistributeApplicationResp, error)
	}

	defaultDocumentLibrary struct {
		cli zrpc.Client
	}
)

func NewDocumentLibrary(cli zrpc.Client) DocumentLibrary {
	return &defaultDocumentLibrary{
		cli: cli,
	}
}

// 保存发放记录
func (m *defaultDocumentLibrary) PreSaveDistributeRecord(ctx context.Context, in *DocumentDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.PreSaveDistributeRecord(ctx, in, opts...)
}

// 获取文档权限用户
func (m *defaultDocumentLibrary) GetDocPermissionUsers(ctx context.Context, in *GetDocPermissionUsersReq, opts ...grpc.CallOption) (*GetDocPermissionUsersResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDocPermissionUsers(ctx, in, opts...)
}

// 更新发放状态
func (m *defaultDocumentLibrary) UpdateDistributeStatusByWorkflowId(ctx context.Context, in *UpdateDistributeStatusReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.UpdateDistributeStatusByWorkflowId(ctx, in, opts...)
}

// 保存发放审批信息
func (m *defaultDocumentLibrary) SaveDistributeApproval(ctx context.Context, in *DistributeApprovalReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.SaveDistributeApproval(ctx, in, opts...)
}

// 保存回收审批信息
func (m *defaultDocumentLibrary) SaveRecycleApprovalInfo(ctx context.Context, in *RecycleApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.SaveRecycleApprovalInfo(ctx, in, opts...)
}

// 保存处置审批信息
func (m *defaultDocumentLibrary) SaveDisposalApprovalInfo(ctx context.Context, in *DisposalApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.SaveDisposalApprovalInfo(ctx, in, opts...)
}

// 获取发放信息列表
func (m *defaultDocumentLibrary) GetDistributeInfoList(ctx context.Context, in *GetDistributeListReq, opts ...grpc.CallOption) (*GetDistributeListResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDistributeInfoList(ctx, in, opts...)
}

// 根据发放列表id获取发放清单信息
func (m *defaultDocumentLibrary) GetDistributeApplicationById(ctx context.Context, in *GetDistributeApplicationReq, opts ...grpc.CallOption) (*GetDistributeApplicationResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDistributeApplicationById(ctx, in, opts...)
}
