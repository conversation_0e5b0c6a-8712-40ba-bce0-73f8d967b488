// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package externaldocumentlibrary

import (
	"context"

	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	ApprovalInfo                    = docvault.ApprovalInfo
	ApprovalInfoItem                = docvault.ApprovalInfoItem
	BookInfo                        = docvault.BookInfo
	CreateBookReq                   = docvault.CreateBookReq
	CreateBookResp                  = docvault.CreateBookResp
	DeleteBookReq                   = docvault.DeleteBookReq
	DeleteBookResp                  = docvault.DeleteBookResp
	DisposalApprovalInfo            = docvault.DisposalApprovalInfo
	DisposalList                    = docvault.DisposalList
	DistributeApprovalReq           = docvault.DistributeApprovalReq
	DistributeInventory             = docvault.DistributeInventory
	DistributeList                  = docvault.DistributeList
	DistributeUser                  = docvault.DistributeUser
	DocumentDistributeReq           = docvault.DocumentDistributeReq
	EmptyResp                       = docvault.EmptyResp
	ExternalDocumentChangeReq       = docvault.ExternalDocumentChangeReq
	ExternalDocumentCreateInfo      = docvault.ExternalDocumentCreateInfo
	ExternalDocumentCreateReq       = docvault.ExternalDocumentCreateReq
	ExternalDocumentCreateResp      = docvault.ExternalDocumentCreateResp
	ExternalDocumentCreateRespList  = docvault.ExternalDocumentCreateRespList
	ExternalDocumentGetReq          = docvault.ExternalDocumentGetReq
	ExternalDocumentGetResp         = docvault.ExternalDocumentGetResp
	ExternalDocumentPageInfo        = docvault.ExternalDocumentPageInfo
	ExternalDocumentPageReq         = docvault.ExternalDocumentPageReq
	ExternalDocumentPageResp        = docvault.ExternalDocumentPageResp
	FilePermission                  = docvault.FilePermission
	GetBookListReq                  = docvault.GetBookListReq
	GetBookListResp                 = docvault.GetBookListResp
	GetDistributeApplicationReq     = docvault.GetDistributeApplicationReq
	GetDistributeApplicationResp    = docvault.GetDistributeApplicationResp
	GetDistributeListInfo           = docvault.GetDistributeListInfo
	GetDistributeListReq            = docvault.GetDistributeListReq
	GetDistributeListResp           = docvault.GetDistributeListResp
	GetDocPermissionUsersReq        = docvault.GetDocPermissionUsersReq
	GetDocPermissionUsersResp       = docvault.GetDocPermissionUsersResp
	ImportBookInfo                  = docvault.ImportBookInfo
	ImportBookReq                   = docvault.ImportBookReq
	ImportBookResp                  = docvault.ImportBookResp
	ImportGroupDocsToCompanyInfo    = docvault.ImportGroupDocsToCompanyInfo
	ImportGroupDocsToCompanyReq     = docvault.ImportGroupDocsToCompanyReq
	ImportGroupDocsToCompanyResp    = docvault.ImportGroupDocsToCompanyResp
	ImportRespInfo                  = docvault.ImportRespInfo
	InternalDocumentBatchCreateReq  = docvault.InternalDocumentBatchCreateReq
	InternalDocumentBatchCreateResp = docvault.InternalDocumentBatchCreateResp
	InternalDocumentChangeReq       = docvault.InternalDocumentChangeReq
	InternalDocumentCreateReq       = docvault.InternalDocumentCreateReq
	InternalDocumentCreateResp      = docvault.InternalDocumentCreateResp
	InternalDocumentGetReq          = docvault.InternalDocumentGetReq
	InternalDocumentGetResp         = docvault.InternalDocumentGetResp
	InternalDocumentPageItem        = docvault.InternalDocumentPageItem
	InternalDocumentPageReq         = docvault.InternalDocumentPageReq
	InternalDocumentPageResp        = docvault.InternalDocumentPageResp
	PageInfo                        = docvault.PageInfo
	Permission                      = docvault.Permission
	PermissionResp                  = docvault.PermissionResp
	PlagiarismCheckReq              = docvault.PlagiarismCheckReq
	ReceivedBy                      = docvault.ReceivedBy
	Recipient                       = docvault.Recipient
	RecycleApprovalInfo             = docvault.RecycleApprovalInfo
	RecycleList                     = docvault.RecycleList
	UpdateBookResp                  = docvault.UpdateBookResp
	UpdateDistributeStatusReq       = docvault.UpdateDistributeStatusReq

	ExternalDocumentLibrary interface {
		// 批量创建文档
		Create(ctx context.Context, in *ExternalDocumentCreateReq, opts ...grpc.CallOption) (*ExternalDocumentCreateResp, error)
		// 获取文档
		Get(ctx context.Context, in *ExternalDocumentGetReq, opts ...grpc.CallOption) (*ExternalDocumentGetResp, error)
		// 分页查询文档
		Page(ctx context.Context, in *ExternalDocumentPageReq, opts ...grpc.CallOption) (*ExternalDocumentPageResp, error)
		// 修订文档
		Change(ctx context.Context, in *ExternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 导入集团文档到公司验重
		ImportCompanyPlagiarismCheck(ctx context.Context, in *PlagiarismCheckReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 导入集团文档到公司
		ImportGroupDocsToCompany(ctx context.Context, in *ImportGroupDocsToCompanyReq, opts ...grpc.CallOption) (*ImportGroupDocsToCompanyResp, error)
	}

	defaultExternalDocumentLibrary struct {
		cli zrpc.Client
	}
)

func NewExternalDocumentLibrary(cli zrpc.Client) ExternalDocumentLibrary {
	return &defaultExternalDocumentLibrary{
		cli: cli,
	}
}

// 批量创建文档
func (m *defaultExternalDocumentLibrary) Create(ctx context.Context, in *ExternalDocumentCreateReq, opts ...grpc.CallOption) (*ExternalDocumentCreateResp, error) {
	client := docvault.NewExternalDocumentLibraryClient(m.cli.Conn())
	return client.Create(ctx, in, opts...)
}

// 获取文档
func (m *defaultExternalDocumentLibrary) Get(ctx context.Context, in *ExternalDocumentGetReq, opts ...grpc.CallOption) (*ExternalDocumentGetResp, error) {
	client := docvault.NewExternalDocumentLibraryClient(m.cli.Conn())
	return client.Get(ctx, in, opts...)
}

// 分页查询文档
func (m *defaultExternalDocumentLibrary) Page(ctx context.Context, in *ExternalDocumentPageReq, opts ...grpc.CallOption) (*ExternalDocumentPageResp, error) {
	client := docvault.NewExternalDocumentLibraryClient(m.cli.Conn())
	return client.Page(ctx, in, opts...)
}

// 修订文档
func (m *defaultExternalDocumentLibrary) Change(ctx context.Context, in *ExternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewExternalDocumentLibraryClient(m.cli.Conn())
	return client.Change(ctx, in, opts...)
}

// 导入集团文档到公司验重
func (m *defaultExternalDocumentLibrary) ImportCompanyPlagiarismCheck(ctx context.Context, in *PlagiarismCheckReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewExternalDocumentLibraryClient(m.cli.Conn())
	return client.ImportCompanyPlagiarismCheck(ctx, in, opts...)
}

// 导入集团文档到公司
func (m *defaultExternalDocumentLibrary) ImportGroupDocsToCompany(ctx context.Context, in *ImportGroupDocsToCompanyReq, opts ...grpc.CallOption) (*ImportGroupDocsToCompanyResp, error) {
	client := docvault.NewExternalDocumentLibraryClient(m.cli.Conn())
	return client.ImportGroupDocsToCompany(ctx, in, opts...)
}
