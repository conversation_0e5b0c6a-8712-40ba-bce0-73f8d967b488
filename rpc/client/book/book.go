// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package book

import (
	"context"

	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	ApprovalInfo                    = docvault.ApprovalInfo
	ApprovalInfoItem                = docvault.ApprovalInfoItem
	BookInfo                        = docvault.BookInfo
	CreateBookReq                   = docvault.CreateBookReq
	CreateBookResp                  = docvault.CreateBookResp
	DeleteBookReq                   = docvault.DeleteBookReq
	DeleteBookResp                  = docvault.DeleteBookResp
	DisposalApprovalInfo            = docvault.DisposalApprovalInfo
	DisposalList                    = docvault.DisposalList
	DistributeApprovalReq           = docvault.DistributeApprovalReq
	DistributeInventory             = docvault.DistributeInventory
	DistributeList                  = docvault.DistributeList
	DistributeUser                  = docvault.DistributeUser
	DocumentDistributeReq           = docvault.DocumentDistributeReq
	EmptyResp                       = docvault.EmptyResp
	ExternalDocumentChangeReq       = docvault.ExternalDocumentChangeReq
	ExternalDocumentCreateInfo      = docvault.ExternalDocumentCreateInfo
	ExternalDocumentCreateReq       = docvault.ExternalDocumentCreateReq
	ExternalDocumentCreateResp      = docvault.ExternalDocumentCreateResp
	ExternalDocumentCreateRespList  = docvault.ExternalDocumentCreateRespList
	ExternalDocumentGetReq          = docvault.ExternalDocumentGetReq
	ExternalDocumentGetResp         = docvault.ExternalDocumentGetResp
	ExternalDocumentPageInfo        = docvault.ExternalDocumentPageInfo
	ExternalDocumentPageReq         = docvault.ExternalDocumentPageReq
	ExternalDocumentPageResp        = docvault.ExternalDocumentPageResp
	FilePermission                  = docvault.FilePermission
	GetBookListReq                  = docvault.GetBookListReq
	GetBookListResp                 = docvault.GetBookListResp
	GetDistributeApplicationReq     = docvault.GetDistributeApplicationReq
	GetDistributeApplicationResp    = docvault.GetDistributeApplicationResp
	GetDistributeListInfo           = docvault.GetDistributeListInfo
	GetDistributeListReq            = docvault.GetDistributeListReq
	GetDistributeListResp           = docvault.GetDistributeListResp
	GetDocPermissionUsersReq        = docvault.GetDocPermissionUsersReq
	GetDocPermissionUsersResp       = docvault.GetDocPermissionUsersResp
	ImportBookInfo                  = docvault.ImportBookInfo
	ImportBookReq                   = docvault.ImportBookReq
	ImportBookResp                  = docvault.ImportBookResp
	ImportGroupDocsToCompanyInfo    = docvault.ImportGroupDocsToCompanyInfo
	ImportGroupDocsToCompanyReq     = docvault.ImportGroupDocsToCompanyReq
	ImportGroupDocsToCompanyResp    = docvault.ImportGroupDocsToCompanyResp
	ImportRespInfo                  = docvault.ImportRespInfo
	InternalDocumentBatchCreateReq  = docvault.InternalDocumentBatchCreateReq
	InternalDocumentBatchCreateResp = docvault.InternalDocumentBatchCreateResp
	InternalDocumentChangeReq       = docvault.InternalDocumentChangeReq
	InternalDocumentCreateReq       = docvault.InternalDocumentCreateReq
	InternalDocumentCreateResp      = docvault.InternalDocumentCreateResp
	InternalDocumentGetReq          = docvault.InternalDocumentGetReq
	InternalDocumentGetResp         = docvault.InternalDocumentGetResp
	InternalDocumentPageItem        = docvault.InternalDocumentPageItem
	InternalDocumentPageReq         = docvault.InternalDocumentPageReq
	InternalDocumentPageResp        = docvault.InternalDocumentPageResp
	PageInfo                        = docvault.PageInfo
	Permission                      = docvault.Permission
	PermissionResp                  = docvault.PermissionResp
	PlagiarismCheckReq              = docvault.PlagiarismCheckReq
	ReceivedBy                      = docvault.ReceivedBy
	Recipient                       = docvault.Recipient
	RecycleApprovalInfo             = docvault.RecycleApprovalInfo
	RecycleList                     = docvault.RecycleList
	UpdateBookResp                  = docvault.UpdateBookResp
	UpdateDistributeStatusReq       = docvault.UpdateDistributeStatusReq

	Book interface {
		// 新增书籍信息
		CreateBook(ctx context.Context, in *CreateBookReq, opts ...grpc.CallOption) (*CreateBookResp, error)
		// 查询书籍列表
		GetBookList(ctx context.Context, in *GetBookListReq, opts ...grpc.CallOption) (*GetBookListResp, error)
		// 修改书籍信息
		UpdateBook(ctx context.Context, in *BookInfo, opts ...grpc.CallOption) (*UpdateBookResp, error)
		// 删除书籍信息
		DeleteBook(ctx context.Context, in *DeleteBookReq, opts ...grpc.CallOption) (*DeleteBookResp, error)
		// 批量导入书籍信息
		ImportBook(ctx context.Context, in *ImportBookReq, opts ...grpc.CallOption) (*ImportBookResp, error)
	}

	defaultBook struct {
		cli zrpc.Client
	}
)

func NewBook(cli zrpc.Client) Book {
	return &defaultBook{
		cli: cli,
	}
}

// 新增书籍信息
func (m *defaultBook) CreateBook(ctx context.Context, in *CreateBookReq, opts ...grpc.CallOption) (*CreateBookResp, error) {
	client := docvault.NewBookClient(m.cli.Conn())
	return client.CreateBook(ctx, in, opts...)
}

// 查询书籍列表
func (m *defaultBook) GetBookList(ctx context.Context, in *GetBookListReq, opts ...grpc.CallOption) (*GetBookListResp, error) {
	client := docvault.NewBookClient(m.cli.Conn())
	return client.GetBookList(ctx, in, opts...)
}

// 修改书籍信息
func (m *defaultBook) UpdateBook(ctx context.Context, in *BookInfo, opts ...grpc.CallOption) (*UpdateBookResp, error) {
	client := docvault.NewBookClient(m.cli.Conn())
	return client.UpdateBook(ctx, in, opts...)
}

// 删除书籍信息
func (m *defaultBook) DeleteBook(ctx context.Context, in *DeleteBookReq, opts ...grpc.CallOption) (*DeleteBookResp, error) {
	client := docvault.NewBookClient(m.cli.Conn())
	return client.DeleteBook(ctx, in, opts...)
}

// 批量导入书籍信息
func (m *defaultBook) ImportBook(ctx context.Context, in *ImportBookReq, opts ...grpc.CallOption) (*ImportBookResp, error) {
	client := docvault.NewBookClient(m.cli.Conn())
	return client.ImportBook(ctx, in, opts...)
}
