Name: docvault.rpc
ListenOn: 0.0.0.0:11013
Etcd:
  Hosts:
  - **************:23791
  Key: docvault.rpc.dev

Mode: dev

MicroServices:
  phoenix:
    Url: http://***************:10011
    Desc: saas微服务
  macrohard:
    Url: http://***************:8090
    Desc: 文档微服务

DocvaultDB:
  type: mysql
  host: **************
  port: "33061"
  user: developer
  password: "Zj123456."
  schema: docvault
  maxIdleConns: 5
  connMaxLifetime: 60
  maxOpenConns: 20

# redis 配置
Redis:
  Key: "docvault.rpc.sit"
  Host: "***************:6379"
  Pass: ""
  Type: "node"


Log:
  ServiceName: docvault
  Mode: console
  Path: ./logs
  Encoding: json
  Level: debug
  Compress: false
  KeepDays: 7


Snowflake:
  Node: 1
  Epoch: 1288834974657
  NodeBits: 1
  StepBits: 5

Kafka:
  Brokers:
    - **************:9092
  MinBytes: 1
  MaxBytes: 10e6
  # Consumers:
    # - Topic: sit_workflow_passed_SIGNATURE_APPROVAL
    #   GroupID: docvault
    #   Key: signature_approval


ScheduledTasks:
  - Name: update_internal_document_status_task
    CronExpr: "0 */5 * * * *"
    Enabled: false