package main

import (
	"context"
	"docvault/rpc/internal/interceptor"
	"docvault/rpc/internal/scheduledtask"
	bookServer "docvault/rpc/internal/server/book"
	documentLibraryServer "docvault/rpc/internal/server/documentlibrary"
	externalDocumentLibraryServer "docvault/rpc/internal/server/externaldocumentlibrary"
	internalDocumentLibraryServer "docvault/rpc/internal/server/internaldocumentlibrary"
	"flag"
	"fmt"

	"docvault/rpc/internal/config"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/docvault.yaml", "the config file")
var etcdEndpoints = flag.String("etcd", "", "etcd endpoints")
var etcdUsername = flag.String("etcd-user", "", "etcd username")
var etcdPassword = flag.String("etcd-pass", "", "etcd password")
var configKey = flag.String("config-key", "/services/config/docvaultrpc/default", "config key")

func main() {
	flag.Parse()
	configManager, err := gzconfigcenter.NewConfigManager[config.Config](gzconfigcenter.EtcdConfig{
		Endpoints: gzconfigcenter.SplitStringIgnoreEmpty(*etcdEndpoints, ","),
		Username:  *etcdUsername,
		Password:  *etcdPassword,
		ConfigKey: *configKey,
	}, *configFile)
	if err != nil {
		panic(fmt.Sprintf("create config manager error: %v", err))
	}

	svcCtx := svc.NewServiceContext(configManager)
	c := configManager.GetConfig()

	ctx := context.Background()
	// 注册定时任务
	scheduledtask.InitScheduledTask(ctx, svcCtx)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		docvault.RegisterInternalDocumentLibraryServer(grpcServer, internalDocumentLibraryServer.NewInternalDocumentLibraryServer(svcCtx))
		docvault.RegisterBookServer(grpcServer, bookServer.NewBookServer(svcCtx))
		docvault.RegisterExternalDocumentLibraryServer(grpcServer, externalDocumentLibraryServer.NewExternalDocumentLibraryServer(svcCtx))
		docvault.RegisterDocumentLibraryServer(grpcServer, documentLibraryServer.NewDocumentLibraryServer(svcCtx))
		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})

	s.AddUnaryInterceptors(interceptor.ErrorUnaryInterceptor, interceptor.SessionUnaryInterceptor)
	s.AddStreamInterceptors(interceptor.ErrorStreamInterceptor, interceptor.SessionStreamInterceptor)
	defer s.Stop()

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
