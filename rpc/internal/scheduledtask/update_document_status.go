package scheduledtask

import (
	context "context"
	"errors"
	"time"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logc"
)

// UpdateDocumentStatusTask 实现 ScheduledTask
// 定期处理内部文档状态
type UpdateDocumentStatusTask struct {
	db *mapper.DocvaultDB
}

func NewUpdateDocumentStatusTask(svcCtx *svc.ServiceContext) *UpdateDocumentStatusTask {
	return &UpdateDocumentStatusTask{
		db: svcCtx.DocvaultDB,
	}
}

func (t *UpdateDocumentStatusTask) Name() string {
	return "update_internal_document_status_task"
}

// Run 查询并批量更新文档状态 2- 未实施，3-有效
func (t *UpdateDocumentStatusTask) Run(ctx context.Context) {
	err := t.updateInternalDocStatusTask(ctx)
	if err != nil {
		return
	}
	err = t.updateExternalDocStatusTask(ctx)
	if err != nil {
		return
	}
}

func (t *UpdateDocumentStatusTask) updateInternalDocStatusTask(ctx context.Context) error {
	client := mapper.NewInternalDocumentLibraryClient(t.db)
	now := time.Now()
	docs, err := client.FindDocumentsByStatusAndEffectiveDate(ctx, 2, now)
	if err != nil {
		logc.Infof(ctx, "[定时任务] %s 查询需更新文档失败: %v", t.Name(), err)
		return err
	}
	if len(docs) == 0 {
		logc.Infof(ctx, "[定时任务] %s 无需更新的文档", t.Name())
		return errors.New("无需更新的文档")
	}
	ids := make([]string, 0, len(docs))
	for _, doc := range docs {
		ids = append(ids, doc.ID)
	}
	err = client.BatchUpdateDocumentStatus(ctx, ids, 3)
	if err != nil {
		logc.Infof(ctx, "[定时任务] %s 批量更新文档状态失败: %v", t.Name(), err)
		return err
	}
	logc.Infof(ctx, "[定时任务] %s 已更新 %d 条内部文档状态", t.Name(), len(ids))
	return nil
}

func (t *UpdateDocumentStatusTask) updateExternalDocStatusTask(ctx context.Context) error {
	client := mapper.NewExternalDocumentLibraryClient(t.db)
	now := time.Now()
	docs, err := client.FindDocumentsByStatusAndEffectiveDate(ctx, 2, now)
	if err != nil {
		logc.Infof(ctx, "[定时任务] %s 查询需更新文档失败: %v", t.Name(), err)
		return err
	}
	if len(docs) == 0 {
		logc.Infof(ctx, "[定时任务] %s 无需更新的文档", t.Name())
		return errors.New("无需更新的文档")
	}
	ids := make([]string, 0, len(docs))
	for _, doc := range docs {
		ids = append(ids, doc.ID)
	}
	err = client.BatchUpdateDocumentStatus(ctx, ids, 3)
	if err != nil {
		logc.Infof(ctx, "[定时任务] %s 批量更新文档状态失败: %v", t.Name(), err)
		return err
	}
	logc.Infof(ctx, "[定时任务] %s 已更新 %d 条外部文档状态", t.Name(), len(ids))
	return nil
}
