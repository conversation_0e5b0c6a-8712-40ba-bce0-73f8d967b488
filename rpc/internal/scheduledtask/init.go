package scheduledtask

import (
	"context"
	"docvault/rpc/internal/svc"
	"docvault/utils"
)

// 注册定时任务
func registerScheduledTasks(svcCtx *svc.ServiceContext) []ScheduledTask {
	return []ScheduledTask{
		// 内部文档状态更新
		NewUpdateDocumentStatusTask(svcCtx),
	}
}

func InitScheduledTask(ctx context.Context, svcCtx *svc.ServiceContext) {
	c := svcCtx.ConfigManager.GetConfig()
	if len(c.ScheduledTasks) == 0 {
		return
	}
	scheduledTasksMap := utils.ExtractSliceFieldToMap(registerScheduledTasks(svcCtx), func(scheduledTask ScheduledTask) (string, ScheduledTask) {
		return scheduledTask.Name(), scheduledTask
	})

	taskManager := GetTaskManager()
	for _, scheduledTask := range c.ScheduledTasks {
		task, ok := scheduledTasksMap[scheduledTask.Name]
		if !ok {
			continue
		}
		taskManager.Register(task, scheduledTask.CronExpr, scheduledTask.Enabled)
	}

	taskManager.StartAll(ctx)
}
