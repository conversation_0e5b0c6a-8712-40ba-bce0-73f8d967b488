package scheduledtask

import (
	"context"
	"sync"

	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logc"
)

// taskEntry 绑定任务和调度器
type taskEntry struct {
	task     ScheduledTask
	cron     *cron.Cron
	cronExpr string
	enabled  bool
}

// TaskManager 统一管理所有定时任务
// 支持注册、批量启动、批量停止、任务列表
type TaskManager struct {
	mu    sync.Mutex
	tasks []*taskEntry
}

var (
	taskManager     *TaskManager
	taskManagerOnce sync.Once
)

// GetTaskManager 获取全局唯一 TaskManager 实例
func GetTaskManager() *TaskManager {
	taskManagerOnce.Do(func() {
		taskManager = &TaskManager{}
	})
	return taskManager
}

// Register 注册一个定时任务
func (m *TaskManager) Register(task ScheduledTask, cronExpr string, enabled bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.tasks = append(m.tasks, &taskEntry{task: task, cronExpr: cronExpr, enabled: enabled})
}

// StartAll 启动所有已注册任务的 cron 调度（仅启用的任务）
func (m *TaskManager) StartAll(ctx context.Context) {
	m.mu.Lock()
	defer m.mu.Unlock()
	for _, entry := range m.tasks {
		if !entry.enabled {
			logc.Infof(ctx, "[定时任务] %s 未启用，跳过", entry.task.Name())
			continue
		}
		entry.cron = cron.New(cron.WithSeconds())
		name := entry.task.Name()
		err := m.addCron(ctx, entry)
		if err != nil {
			logc.Infof(ctx, "[定时任务] %s 启动失败: %v", name, err)
			panic("[定时任务] " + name + " 启动失败: " + err.Error())
		}
		entry.cron.Start()
		logc.Infof(ctx, "[定时任务] %s 启动，表达式: %s", name, entry.cronExpr)
	}
}

func (m *TaskManager) addCron(ctx context.Context, entry *taskEntry) error {
	_, err := entry.cron.AddFunc(entry.cronExpr, func() {
		entry.task.Run(ctx)
	})
	return err
}

// StopAll 停止所有已注册任务的 cron 调度
func (m *TaskManager) StopAll(ctx context.Context) {
	m.mu.Lock()
	defer m.mu.Unlock()
	for _, entry := range m.tasks {
		if entry.cron != nil {
			entry.cron.Stop()
			logc.Infof(ctx, "[定时任务] %s 停止", entry.task.Name())
		}
	}
}

// List 返回所有已注册任务
func (m *TaskManager) List() []ScheduledTask {
	m.mu.Lock()
	defer m.mu.Unlock()
	tasks := make([]ScheduledTask, 0, len(m.tasks))
	for _, entry := range m.tasks {
		tasks = append(tasks, entry.task)
	}
	return tasks
}
