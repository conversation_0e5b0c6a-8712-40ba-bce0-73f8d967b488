package svc

import (
	"docvault/rpc/internal/config"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/factory"
	"docvault/rpc/internal/infrastructure/adapter/addons"
	"docvault/rpc/internal/infrastructure/adapter/clientx"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/infrastructure/referenceimpl"

	"gitee.com/damengde/gzconfigcenter"
	"go.uber.org/fx"
)

// 依赖注入
func provide(c gzconfigcenter.ConfigManager[config.Config], svcCtx *ServiceContext) {

	fx.New(
		// 基础组件
		fx.Options(
			fx.Supply(c.GetConfig()),
			fx.Provide(fx.Annotate(addons.NewRedisAddonsImpl, fx.As(new(addons.RedisAddons)))),
			fx.Provide(fx.Annotate(addons.NewIdGeneratorAddonsImpl, fx.As(new(addons.IDGeneratorAddons)))),
			fx.Provide(fx.Annotate(addons.NewQuickNameTranslatorImpl, fx.As(new(addons.QuickNameTranslator)))),
			fx.Provide(mapper.NewDocvaultDB),
			fx.Provide(fx.Annotate(clientx.NewPhoenixClientImpl, fx.As(new(clientx.PhoenixClient)))),
			fx.Provide(fx.Annotate(clientx.NewMacrohardClientImpl, fx.As(new(clientx.MacrohardClient)))),
		),

		// 领域能力
		fx.Options(
			// 能力实现与其接口绑定
			fx.Provide(fx.Annotate(referenceimpl.NewBasicAbilityReferenceImpl, fx.As(new(aggregate.BasicAbilityReference)))),
			fx.Provide(fx.Annotate(referenceimpl.NewInternalDocumentLibraryReferenceImpl, fx.As(new(aggregate.InternalDocumentLibraryReference)))),
			fx.Provide(fx.Annotate(referenceimpl.NewExternalDocumentLibraryReferenceImpl, fx.As(new(aggregate.ExternalDocumentLibraryReference)))),
			fx.Provide(fx.Annotate(referenceimpl.NewDocumentLibraryReferenceImpl, fx.As(new(aggregate.DocumentLibraryReference)))),
		),

		// 领域工厂
		fx.Options(
			fx.Provide(fx.Annotate(factory.NewImpl, fx.As(new(factory.Factory)))),
		),

		// 全文注入
		fx.Options(
			fx.Populate(&svcCtx.Factory),
			fx.Populate(&svcCtx.QuickNameTranslator),
			fx.Populate(&svcCtx.DocvaultDB),
			fx.Populate(&svcCtx.IDGenerator),
			fx.Populate(&svcCtx.Redis),
			fx.Populate(&svcCtx.QuickNameTranslator),
		),
	)

}
