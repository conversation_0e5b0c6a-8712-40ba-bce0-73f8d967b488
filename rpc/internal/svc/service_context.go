package svc

import (
	"docvault/rpc/internal/config"
	"docvault/rpc/internal/domain/factory"
	"docvault/rpc/internal/infrastructure/adapter/addons"
	"docvault/rpc/internal/infrastructure/adapter/mapper"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	ConfigManager       gzconfigcenter.ConfigManager[config.Config]
	TraceMiddleware     rest.Middleware
	DocvaultDB          *mapper.DocvaultDB
	Redis               addons.RedisAddons
	IDGenerator         addons.IDGeneratorAddons
	QuickNameTranslator addons.QuickNameTranslator
	Factory             factory.Factory
}

func NewServiceContext(c gzconfigcenter.ConfigManager[config.Config]) *ServiceContext {
	svcCtx := &ServiceContext{
		ConfigManager: c,
	}
	provide(c, svcCtx)

	return svcCtx
}
