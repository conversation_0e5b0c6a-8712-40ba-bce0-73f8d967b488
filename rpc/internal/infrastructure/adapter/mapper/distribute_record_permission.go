package mapper

import (
	"context"
	"docvault/rpc/internal/domain/value"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameDistributeRecordPermission = "distribute_record_permissions"
)

// DistributeRecordPermission 对应 distribute_record_permissions 表
type DistributeRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联DistributeRecordFile的ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
	Recipient      string `gorm:"type:varchar(255);comment:'接收方'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	UserName       string `gorm:"type:varchar(255);comment:'接收人姓名'"`
	SignForStatus  int32  `gorm:"comment:'签收状态,1未签收 | 2已签收'"`
	DisposeStatus  int32  `gorm:"comment:'处置状态,1未回收 | 2回收审批中 | 3已回收'"`
}

func (DistributeRecordPermission) TableName() string {
	return TableNameDistributeRecordPermission
}

// DistributeRecordPermissionClient 是 distribute_record_permissions 表的数据访问客户端
type DistributeRecordPermissionClient struct {
	db *gorm.DB
}

// NewDistributeRecordPermissionClient 创建一个新的 DistributeRecordPermissionClient 实例
func NewDistributeRecordPermissionClient(db *DocvaultDB) *DistributeRecordPermissionClient {
	return &DistributeRecordPermissionClient{
		db: db.GetDB(),
	}
}

// Create 创建一个新的 DistributeRecordPermission 记录
func (c *DistributeRecordPermissionClient) CreateWithTx(ctx context.Context, tx Transaction, permission *DistributeRecordPermission) error {
	if err := tx.GetTX().WithContext(ctx).Create(permission).Error; err != nil {
		logc.Error(ctx, "Failed to create distribute record permission", err)
		return err
	}
	return nil
}

// GetByFileRecordID 根据 FileRecordID 获取所有相关的 DistributeRecordPermission 记录
func (c *DistributeRecordPermissionClient) GetByFileRecordID(ctx context.Context, fileRecordID string) ([]*DistributeRecordPermission, error) {
	var permissions []*DistributeRecordPermission
	if err := c.db.WithContext(ctx).Where("file_record_id = ?", fileRecordID).Find(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to get distribute record permissions by file record DistributeID", err)
		return nil, err
	}
	return permissions, nil
}

// GetByFileIDAndFileFromAndFilePermission 根据文件ID、文件来源和文件权限获取用户ID列表
func (c *DistributeRecordPermissionClient) GetByFileIDAndFileFromAndFilePermission(ctx context.Context, fileID, orgId string, fileFrom int32, filePermission int32) ([]string, error) {
	var userIds []string

	// 查询具有指定权限的用户ID
	// 这里假设权限信息存储在 distribute_record_permissions 表中
	// 并且与 distribute_record_files 和 distribute_records 表关联
	err := c.db.WithContext(ctx).
		Table(TableNameDistributeRecordPermission+" as p").
		Select("p.user_id").
		Joins("left join "+TableNameDistributeRecordFile+" as f on p.file_record_id = f.id").
		Joins("left join "+TableNameDistributeRecord+" as r on f.record_id = r.id").
		Where("f.file_id = ? and r.file_type = ? and p.file_permission = ? and r.organization_id = ?", fileID, fileFrom, filePermission, orgId).
		Group("p.user_id").
		Find(&userIds).Error

	if err != nil {
		logc.Error(ctx, "Failed to get user IDs by file DistributeID, file from and file permission", err)
		return nil, err
	}

	return userIds, nil
}

// GetDistributeDocPermissionUsers 根据文件ID、文件来源和文件权限获取发放权限用户信息
func (c *DistributeRecordPermissionClient) GetDistributeDocPermissionUsers(ctx context.Context, fileId string, fileFrom, filePermission int32) ([]struct {
	UserID           string `gorm:"column:user_id"`
	DistributeStatus int    `gorm:"column:distribute_status"`
}, error) {
	var result []struct {
		UserID           string `gorm:"column:user_id"`
		DistributeStatus int    `gorm:"column:distribute_status"`
	}

	err := c.db.WithContext(ctx).
		Table("distribute_record_permissions as p").
		Select("p.user_id, r.status as distribute_status").
		Joins("LEFT JOIN distribute_record_files as f ON p.file_record_id = f.id").
		Joins("LEFT JOIN distribute_records as r ON f.record_id = r.id").
		Where("f.file_id = ? AND r.file_type = ? AND p.file_permission = ? AND r.status IN (2, 3, 4)",
			fileId, fileFrom, filePermission).
		Group("p.user_id, r.status").
		Find(&result).Error

	if err != nil {
		logc.Error(ctx, "Failed to get distribute doc permission users", err)
		return nil, err
	}

	return result, nil
}

// GetDetailedUserPermissions 获取详细的用户权限状态信息
// 参数:
//   - ctx: 上下文
//   - fileId: 文件ID
//   - fileFrom: 文件类型，1内部文件 | 2外部文件
//   - filePermission: 文件权限，1查阅 | 2查阅/下载 | 3一次下载
//
// 返回:
//   - []value.DetailedUserPermission: 详细的用户权限信息列表
//   - error: 错误信息，成功时返回nil
func (c *DistributeRecordPermissionClient) GetDetailedUserPermissions(ctx context.Context, fileId string, fileFrom, filePermission int32) ([]value.DetailedUserPermission, error) {
	var result []value.DetailedUserPermission

	// 查询详细的用户权限信息，包括签收状态和处置状态
	type UserPermissionDetail struct {
		UserID           string `gorm:"column:user_id"`
		DistributeStatus int    `gorm:"column:distribute_status"`
		SignForStatus    int32  `gorm:"column:sign_for_status"`
		DisposeStatus    int32  `gorm:"column:dispose_status"`
	}

	var permissions []UserPermissionDetail
	err := c.db.WithContext(ctx).
		Table("distribute_record_permissions as p").
		Select("p.user_id, r.status as distribute_status, p.sign_for_status, p.dispose_status").
		Joins("LEFT JOIN distribute_record_files as f ON p.file_record_id = f.id").
		Joins("LEFT JOIN distribute_records as r ON f.record_id = r.id").
		Where("f.file_id = ? AND r.file_type = ? AND p.file_permission = ? AND r.status IN (2, 3, 4)",
			fileId, fileFrom, filePermission).
		Group("p.user_id, r.status, p.sign_for_status, p.dispose_status").
		Find(&permissions).Error

	if err != nil {
		logc.Error(ctx, "Failed to get detailed user permissions", err)
		return nil, err
	}

	// 转换为返回格式
	for _, perm := range permissions {
		result = append(result, value.DetailedUserPermission{
			UserID:           perm.UserID,
			DistributeStatus: perm.DistributeStatus,
			SignForStatus:    perm.SignForStatus,
			DisposeStatus:    perm.DisposeStatus,
		})
	}

	return result, nil
}

// GetDistributeUsersByRecordID 根据分发记录ID获取用户状态信息
// 参数:
//   - ctx: 上下文对象，用于传递请求范围的值、取消信号和截止时间
//   - recordID: 分发记录ID
//
// 返回:
//   - received: 已签收用户列表
//   - notReceived: 未签收用户列表
//   - recycle: 已回收用户列表
//   - error: 查询失败时返回错误，成功时返回nil
func (c *DistributeRecordPermissionClient) GetDistributeUsersByRecordID(ctx context.Context, recordID string) (received, notReceived, recycle []DistributeUser, err error) {
	type UserStatus struct {
		UserID        string `json:"user_id"`
		FileForm      int32  `json:"file_form"`
		SignForStatus int32  `json:"sign_for_status"`
		DisposeStatus int32  `json:"dispose_status"`
	}

	var userStatuses []UserStatus
	err = c.db.WithContext(ctx).
		Table("distribute_record_permissions as p").
		Select("p.user_id, p.file_form, p.sign_for_status, p.dispose_status").
		Joins("JOIN distribute_record_files as f ON p.file_record_id = f.id").
		Where("f.record_id = ?", recordID).
		Find(&userStatuses).Error

	if err != nil {
		logc.Error(ctx, "Failed to get distribute users by record ID", err)
		return nil, nil, nil, err
	}

	// 分类用户状态
	for _, user := range userStatuses {
		distributeUser := DistributeUser{
			UserID:   user.UserID,
			FileForm: int(user.FileForm),
		}

		// 根据签收状态和处置状态分类
		switch {
		case user.DisposeStatus == 4: // 已回收
			recycle = append(recycle, distributeUser)
		case user.SignForStatus == 4: // 已签收
			received = append(received, distributeUser)
		default: // 未签收
			notReceived = append(notReceived, distributeUser)
		}
	}

	return received, notReceived, recycle, nil
}

// DistributeUser 分发用户信息
type DistributeUser struct {
	UserID   string `json:"userId"`   // 用户id
	FileForm int    `json:"fileForm"` // 文件形式，1电子文件 | 2纸质文件
}

// UserStatusInfo 用户状态信息结构
type UserStatusInfo struct {
	RecordID      string `json:"record_id"`       // 记录ID
	UserID        string `json:"user_id"`         // 用户ID
	FileForm      int32  `json:"file_form"`       // 文件形式
	SignForStatus int32  `json:"sign_for_status"` // 签收状态
	DisposeStatus int32  `json:"dispose_status"`  // 处置状态
}

// GetUserStatusesByRecordIDs 批量根据分发记录ID列表查询用户状态信息（仅查询数据）
// 参数:
//   - ctx: 上下文对象，用于传递请求范围的值、取消信号和截止时间
//   - recordIDs: 分发记录ID列表
//
// 返回:
//   - []UserStatusInfo: 用户状态信息列表
//   - error: 查询失败时返回错误，成功时返回nil
func (c *DistributeRecordPermissionClient) GetUserStatusesByRecordIDs(ctx context.Context, recordIDs []string) ([]UserStatusInfo, error) {
	if len(recordIDs) == 0 {
		return []UserStatusInfo{}, nil
	}

	// 批量查询所有相关的权限记录
	var userStatuses []UserStatusInfo
	err := c.db.WithContext(ctx).
		Select("distribute_record_permissions.file_record_id as record_id, distribute_record_permissions.user_id, distribute_record_permissions.file_form, distribute_record_permissions.sign_for_status, distribute_record_permissions.dispose_status").
		Table("distribute_record_permissions").
		Where("distribute_record_permissions.file_record_id IN ?", recordIDs).
		Find(&userStatuses).Error

	if err != nil {
		logc.Error(ctx, "Failed to get user statuses by record IDs", err)
		return nil, err
	}

	return userStatuses, nil
}

// GetPermissionsByFileRecordIDs 根据文件记录ID列表获取权限信息
// 参数:
//   - ctx: 上下文
//   - fileRecordIDs: 文件记录ID列表
//
// 返回:
//   - []*DistributeRecordPermission: 权限信息列表
//   - error: 错误信息，成功时返回nil
func (c *DistributeRecordPermissionClient) GetPermissionsByFileRecordIDs(ctx context.Context, fileRecordIDs []string) ([]*DistributeRecordPermission, error) {
	if len(fileRecordIDs) == 0 {
		return []*DistributeRecordPermission{}, nil
	}

	var permissions []*DistributeRecordPermission
	err := c.db.WithContext(ctx).
		Where("file_record_id IN ?", fileRecordIDs).
		Find(&permissions).Error

	if err != nil {
		logc.Error(ctx, "Failed to get permissions by file record IDs", err)
		return nil, err
	}

	return permissions, nil
}

// DeleteByFileRecordIDsWithTx 根据文件记录ID列表删除权限记录（事务版本）
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - fileRecordIDs: 文件记录ID列表
//
// 返回:
//   - error: 删除失败时返回错误，成功时返回nil
func (c *DistributeRecordPermissionClient) DeleteByFileRecordIDsWithTx(ctx context.Context, tx Transaction, fileRecordIDs []string) error {
	if len(fileRecordIDs) == 0 {
		return nil
	}

	if err := tx.GetTX().WithContext(ctx).Where("file_record_id IN ?", fileRecordIDs).Delete(&DistributeRecordPermission{}).Error; err != nil {
		logc.Error(ctx, "Failed to delete distribute record permissions by file record IDs", err)
		return err
	}
	return nil
}
