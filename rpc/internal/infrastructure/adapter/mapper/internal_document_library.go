package mapper

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	TableNameInternalDocumentLibrary = "internal_document_library"
)

type InternalDocumentLibraryClient struct {
	db *gorm.DB
}

func NewInternalDocumentLibraryClient(db *DocvaultDB) *InternalDocumentLibraryClient {
	return &InternalDocumentLibraryClient{db: db.GetDB()}
}

func (c *InternalDocumentLibraryClient) CreateWithTX(ctx context.Context, tx Transaction, internalDocument InternalDocumentLibrary) error {
	return tx.GetTX().Create(internalDocument).Error
}

func (c *InternalDocumentLibraryClient) Create(ctx context.Context, internalDocument InternalDocumentLibrary) error {
	return c.db.WithContext(ctx).Create(&internalDocument).Error
}

func (c *InternalDocumentLibraryClient) GetByID(ctx context.Context, id string) (InternalDocumentLibrary, error) {
	internalDocument := InternalDocumentLibrary{}
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&internalDocument).Error; err != nil {
		return InternalDocumentLibrary{}, err
	}
	return internalDocument, nil
}

// 获取前缀最大值并加锁
func (c *InternalDocumentLibraryClient) GetPrefixMaxAndLock(ctx context.Context, tx Transaction, noPrefix string, organizationID string) (int, error) {
	maxNo := 0
	if err := tx.GetTX().Table(TableNameInternalDocumentLibrary).Select("ifnull(max(serial_no), 0) as max_no").Where("no_prefix = ? and organization_id = ?", noPrefix, organizationID).
		Clauses(clause.Locking{Strength: "UPDATE"}).Find(&maxNo).Error; err != nil {
		return 0, err
	}
	return maxNo, nil
}

type InternalDocumentLibraryPageReq struct {
	Page           int
	PageSize       int
	NoPage         bool
	Ids            []string
	DocCategoryIds []string
	DepartmentIds  []string
	Status         int32
	OrganizationID string
	HasAttachment  int32
	Name           string
	No             string
	OriginalNo     string
}

func (c *InternalDocumentLibraryClient) Page(ctx context.Context, pageReq InternalDocumentLibraryPageReq) (docs []InternalDocumentLibrary, total int64, err error) {
	query := c.db.WithContext(ctx).Table(TableNameInternalDocumentLibrary).Where("organization_id = ?", pageReq.OrganizationID)
	if len(pageReq.Ids) > 0 {
		query = query.Where("id in ?", pageReq.Ids)
	}
	if pageReq.Status != 0 {
		query = query.Where("status = ?", pageReq.Status)
	}
	if len(pageReq.DocCategoryIds) > 0 {
		query = query.Where("doc_category_id in ?", pageReq.DocCategoryIds)
	}
	if len(pageReq.DepartmentIds) > 0 {
		query = query.Where("department_id in ?", pageReq.DepartmentIds)
	}
	if pageReq.Name != "" {
		query = query.Where("name like ?", "%"+pageReq.Name+"%")
	}
	if pageReq.No != "" {
		query = query.Where("no like ?", "%"+pageReq.No+"%")
	}
	if pageReq.OriginalNo != "" {
		query = query.Where("original_no like ?", "%"+pageReq.OriginalNo+"%")
	}

	if pageReq.HasAttachment == 1 {
		query = query.Where("file_id is not null and file_id != ''")
	} else if pageReq.HasAttachment == 2 {
		query = query.Where("file_id is null or file_id = ''")
	}

	query = query.Count(&total).Order("no_prefix, no desc")

	if !pageReq.NoPage {
		query = query.Offset(int((pageReq.Page - 1) * pageReq.PageSize)).Limit(int(pageReq.PageSize))
	}

	if err := query.Find(&docs).Error; err != nil {
		return nil, 0, err
	}
	return docs, total, nil
}

// InternalDocumentLibrary 是 internal_document_library 表的GORM模型
type InternalDocumentLibrary struct {
	ID                string         `gorm:"type:varchar(64);primary_key"`
	OrganizationID    string         `gorm:"type:varchar(64);column:organization_id"`      // 子公司ID
	NoPrefix          string         `gorm:"type:varchar(128);column:no_prefix"`           // 编号前缀
	No                string         `gorm:"type:varchar(128);column:no"`                  // 编号
	SerialNo          int            `gorm:"type:int(11);column:serial_no"`                // 编号序列号
	Name              string         `gorm:"type:varchar(255);column:name"`                // 文件名称
	FileID            string         `gorm:"type:varchar(255);column:file_id"`             // 文件id
	DocCategoryID     string         `gorm:"type:varchar(64);column:doc_category_id"`      // 类别ID
	DepartmentID      string         `gorm:"type:varchar(64);column:department_id"`        // 编制部门ID
	AuthorID          string         `gorm:"type:varchar(64);column:author_id"`            // 编制人ID
	Status            int8           `gorm:"type:tinyint;column:status"`                   // 状态
	ApprovalInfo      datatypes.JSON `gorm:"type:json;column:approval_info"`               // 审批信息
	VersionNo         int            `gorm:"type:int(11);column:version_no"`               // 版本
	PublishDate       time.Time      `gorm:"type:date;column:publish_date"`                // 发布日期
	EffectiveDate     time.Time      `gorm:"type:date;column:effective_date"`              // 实施日期
	OriginalNo        string         `gorm:"type:varchar(128);column:original_no"`         // 原文件编号
	OriginalVersionNo string         `gorm:"type:varchar(128);column:original_version_no"` // 原文件版本
	CreatedAt         time.Time      `gorm:"column:created_at"`                            // 创建时间
	UpdatedAt         time.Time      `gorm:"column:updated_at"`                            // 更新时间
	CreatedBy         string         `gorm:"type:varchar(64);column:created_by"`           // 创建人
	UpdatedBy         string         `gorm:"type:varchar(64);column:updated_by"`           // 更新人
}

// TableName 指定GORM模型对应的表名
func (InternalDocumentLibrary) TableName() string {
	return TableNameInternalDocumentLibrary
}

type ApprovalInfo struct {
	Auditors  []ApprovalItem `json:"auditors"`  // 审核人
	Approvers []ApprovalItem `json:"approvers"` // 批准人
}

type ApprovalItem struct {
	UserID     string `json:"userId"`
	PassedDate int64  `json:"passedDate"`
}

func (i InternalDocumentLibrary) GetApprovalInfo() ApprovalInfo {
	approvalInfo, err := i.ApprovalInfo.MarshalJSON()
	if err != nil {
		return ApprovalInfo{}
	}
	approvalInfoValue := ApprovalInfo{}
	if err := json.Unmarshal(approvalInfo, &approvalInfoValue); err != nil {
		return ApprovalInfo{}
	}
	return approvalInfoValue
}

func (i *InternalDocumentLibrary) SetApprovalInfo(approvalInfo ApprovalInfo) {
	approvalInfoBytes, err := json.Marshal(approvalInfo)
	if err != nil {
		return
	}
	i.ApprovalInfo = datatypes.JSON(approvalInfoBytes)
}

// FindDocumentsByStatusAndEffectiveDate 查询指定状态且 effective_date<=指定日期的文档
func (c *InternalDocumentLibraryClient) FindDocumentsByStatusAndEffectiveDate(ctx context.Context, status int8, effectiveDate time.Time) ([]InternalDocumentLibrary, error) {
	docs := []InternalDocumentLibrary{}
	err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Where("status = ? AND effective_date <= ?", status, effectiveDate.Format("2006-01-02")).
		Find(&docs).Error
	if err != nil {
		return nil, err
	}
	return docs, nil
}

// BatchUpdateDocumentStatus 批量根据 id 列表更新 status
func (c *InternalDocumentLibraryClient) BatchUpdateDocumentStatus(ctx context.Context, ids []string, newStatus int8) error {
	if len(ids) == 0 {
		return nil
	}
	return c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Where("id IN ?", ids).
		Update("status", newStatus).Error
}

func (c *InternalDocumentLibraryClient) GetCountByNames(ctx context.Context, names []string, organizationID string) (int64, error) {
	count := int64(0)
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Where("name IN ? AND organization_id = ?", names, organizationID).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
