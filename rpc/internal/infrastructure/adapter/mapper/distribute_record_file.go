package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameDistributeRecordFile = "distribute_record_files"
)

// DistributeRecordFile 对应 distribute_record_files 表
type DistributeRecordFile struct {
	ID       string `gorm:"type:varchar(64);primary_key"`
	RecordID string `gorm:"type:varchar(64);index;comment:'关联DistributeRecord的ID'"`
	FileID   string `gorm:"type:varchar(64);comment:'文件id'"`
	FileName string `gorm:"type:varchar(255);comment:'文件名'"`
	Number   string `gorm:"type:varchar(255);comment:'文件编号'"`
	Version  string `gorm:"type:varchar(255);comment:'版本'"`
}

func (DistributeRecordFile) TableName() string {
	return TableNameDistributeRecordFile
}

// DistributeRecordFileClient 是 distribute_record_files 表的数据访问客户端
type DistributeRecordFileClient struct {
	db *gorm.DB
}

// NewDistributeRecordFileClient 创建一个新的 DistributeRecordFileClient 实例
func NewDistributeRecordFileClient(db *DocvaultDB) *DistributeRecordFileClient {
	return &DistributeRecordFileClient{
		db: db.GetDB(),
	}
}

// Create 创建一个新的 DistributeRecordFile 记录
func (c *DistributeRecordFileClient) CreateWithTx(ctx context.Context, tx Transaction, file *DistributeRecordFile) error {
	if err := tx.GetTX().WithContext(ctx).Create(file).Error; err != nil {
		logc.Error(ctx, "Failed to create distribute record file", err)
		return err
	}
	return nil
}

// GetByRecordID 根据 RecordID 获取所有相关的 DistributeRecordFile 记录
func (c *DistributeRecordFileClient) GetByRecordID(ctx context.Context, recordID string) ([]*DistributeRecordFile, error) {
	var files []*DistributeRecordFile
	if err := c.db.WithContext(ctx).Where("record_id = ?", recordID).Find(&files).Error; err != nil {
		logc.Error(ctx, "Failed to get distribute record files by record DistributeID", err)
		return nil, err
	}
	return files, nil
}

// GetByID 根据 DistributeID 获取 DistributeRecordFile 记录
func (c *DistributeRecordFileClient) GetByID(ctx context.Context, id string) (*DistributeRecordFile, error) {
	var file DistributeRecordFile
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get distribute record file by DistributeID", err)
		return nil, err
	}
	return &file, nil
}

// GetInventoryByRecordID 根据发放记录ID获取发放清单信息
// 参数:
//   - ctx: 上下文
//   - recordID: 发放记录ID
//
// 返回:
//   - []*DistributeRecordFile: 发放清单文件列表
//   - error: 错误信息，成功时返回nil
func (c *DistributeRecordFileClient) GetInventoryByRecordID(ctx context.Context, recordID string) ([]*DistributeRecordFile, error) {
	var files []*DistributeRecordFile
	if err := c.db.WithContext(ctx).Where("record_id = ?", recordID).Find(&files).Error; err != nil {
		logc.Error(ctx, "Failed to get distribute inventory by record ID", err)
		return nil, err
	}
	return files, nil
}
