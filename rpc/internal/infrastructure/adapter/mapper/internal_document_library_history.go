package mapper

import (
	"context"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameInternalDocumentLibraryHistory = "internal_document_library_history"
)

type InternalDocumentLibraryHistoryClient struct {
	db *gorm.DB
}

func NewInternalDocumentLibraryHistoryClient(db *DocvaultDB) *InternalDocumentLibraryHistoryClient {
	return &InternalDocumentLibraryHistoryClient{db: db.GetDB()}
}

func (c *InternalDocumentLibraryHistoryClient) CreateWithTX(ctx context.Context, tx Transaction, history InternalDocumentLibraryHistory) error {
	return tx.GetTX().Create(&history).Error
}

type InternalDocumentLibraryHistory struct {
	ID             string         `gorm:"type:varchar(64);primary_key"`
	MainID         string         `gorm:"type:varchar(64);column:main_id"`         // 关联主表ID
	OrganizationID string         `gorm:"type:varchar(64);column:organization_id"` // 子公司ID
	NoPrefix       string         `gorm:"type:varchar(128);column:no_prefix"`      // 编号前缀
	No             string         `gorm:"type:varchar(128);column:no"`             // 编号
	SerialNo       int            `gorm:"type:int(11);column:serial_no"`           // 编号序列号
	Name           string         `gorm:"type:varchar(255);column:name"`           // 文件名称
	FileID         string         `gorm:"type:varchar(255);column:file_id"`        // 文件id
	DocCategoryID  string         `gorm:"type:varchar(64);column:doc_category_id"` // 类别ID
	DepartmentID   string         `gorm:"type:varchar(64);column:department_id"`   // 编制部门ID
	AuthorID       string         `gorm:"type:varchar(64);column:author_id"`       // 编制人ID
	VersionNo      int            `gorm:"type:int(11);column:version_no"`          // 版本
	PublishDate    time.Time      `gorm:"type:date;column:publish_date"`           // 发布日期
	EffectiveDate  time.Time      `gorm:"type:date;column:effective_date"`         // 实施日期
	ApprovalInfo   datatypes.JSON `gorm:"type:json;column:approval_info"`          // 审批信息
	OperatedType   int8           `gorm:"type:tinyint;column:operated_type"`       // 操作类型 1- 新增,2- 修订,3-作废
	CreatedAt      time.Time      `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at"`                       // 更新时间
	CreatedBy      string         `gorm:"type:varchar(64);column:created_by"`      // 创建人
	UpdatedBy      string         `gorm:"type:varchar(64);column:updated_by"`      // 更新人
}

// TableName 指定GORM模型对应的表名
func (InternalDocumentLibraryHistory) TableName() string {
	return TableNameInternalDocumentLibraryHistory
}
