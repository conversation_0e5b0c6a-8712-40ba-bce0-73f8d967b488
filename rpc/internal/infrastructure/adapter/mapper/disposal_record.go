package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameDisposalRecord = "disposal_records"
)

// DisposalRecord 对应 disposal_records 表
type DisposalRecord struct {
	ID                 string         `gorm:"type:varchar(64);primary_key"`
	DistributeRecordID string         `gorm:"type:varchar(64);index;comment:'关联的发放记录ID'"`
	DisposalBy         string         `gorm:"type:varchar(64);comment:'处置人,用户id'"`
	DisposalDate       int64          `gorm:"comment:'处置日期'"`
	Reason             string         `gorm:"type:text;comment:'处置方式'"`
	WorkflowID         string         `gorm:"type:varchar(64);comment:'流程ID'"`
	ApprovalInfo       datatypes.JSON `gorm:"type:json;column:approval_info;comment:'审批信息'"`
	CreatedAt          time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt          time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy          string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy          string         `gorm:"type:varchar(64);column:updated_by"`
}

func (DisposalRecord) TableName() string {
	return TableNameDisposalRecord
}

// DisposalRecordClient 是 disposal_records 表的数据访问客户端
type DisposalRecordClient struct {
	db *gorm.DB
}

// NewDisposalRecordClient 创建一个新的 DisposalRecordClient 实例
func NewDisposalRecordClient(db *DocvaultDB) *DisposalRecordClient {
	return &DisposalRecordClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 DisposalRecord 记录
func (c *DisposalRecordClient) CreateWithTx(ctx context.Context, tx Transaction, record *DisposalRecord) error {
	if err := tx.GetTX().WithContext(ctx).Create(record).Error; err != nil {
		logc.Error(ctx, "Failed to create disposal record", err)
		return err
	}
	return nil
}

// GetByID 根据 ID 获取 DisposalRecord 记录
func (c *DisposalRecordClient) GetByID(ctx context.Context, id string) (*DisposalRecord, error) {
	var record DisposalRecord
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get disposal record by ID", err)
		return nil, err
	}
	return &record, nil
}

// Update 更新 DisposalRecord 记录
func (c *DisposalRecordClient) Update(ctx context.Context, record *DisposalRecord) error {
	if err := c.db.WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update disposal record", err)
		return err
	}
	return nil
}

// GetByDistributeRecordID 根据发放记录ID获取处置记录
func (c *DisposalRecordClient) GetByDistributeRecordID(ctx context.Context, distributeRecordID string) (*DisposalRecord, error) {
	var record DisposalRecord
	if err := c.db.WithContext(ctx).Where("distribute_record_id = ?", distributeRecordID).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get disposal record by distribute record ID", err)
		return nil, err
	}
	return &record, nil
}
