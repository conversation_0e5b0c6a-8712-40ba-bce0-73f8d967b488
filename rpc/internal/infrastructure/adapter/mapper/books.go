package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const BooksTableName = "books"

type BooksClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewBooksClient(db *DocvaultDB) *BooksClient {
	return &BooksClient{
		db: db.db,
	}
}

// 根据id查询书籍信息
func (b *BooksClient) GetBookInfoById(ctx context.Context, id string) (Books, error) {
	var bookInfo Books
	err := b.db.WithContext(ctx).Where("id = ?", id).Find(&bookInfo).Error
	return bookInfo, err
}

// 根据id删除书籍信息
func (b *BooksClient) Delete(ctx context.Context, id string) error {
	err := b.db.WithContext(ctx).Table(BooksTableName).Where("id = ?", id).Update("is_delete", 1).Error
	return err
}

// 通过类型查询最大书籍编号
func (b *BooksClient) GetMaxNumberByDictNodeId(ctx context.Context, orgID, dictionaryNodeID string) (bookInfo Books, err error) {
	err = b.db.WithContext(ctx).
		Where("organization_id = ? and dictionary_node_id = ? and is_delete = ?", orgID, dictionaryNodeID, 0).
		Order("sort desc").
		Limit(1).
		Find(&bookInfo).Error
	if err != nil {
		return Books{}, err
	}
	return bookInfo, nil
}

func (b *BooksClient) Create(ctx context.Context, book Books) error {
	err := b.db.WithContext(ctx).Create(&book).Error
	return err
}

// 通过组织id获取书籍信息
func (b *BooksClient) GetBooksByOrgId(ctx context.Context, orgID string) ([]Books, error) {
	var books []Books
	err := b.db.WithContext(ctx).Where("organization_id = ? and is_delete = ?", orgID, 0).Find(&books).Error
	return books, err
}

// 通过组织id和书籍名称获取书籍信息
func (b *BooksClient) GetBookByOrgIdAndName(ctx context.Context, name, orgID string) (Books, error) {
	var book Books
	err := b.db.WithContext(ctx).Where("name = ? and organization_id = ? and is_delete = ?", name, orgID, 0).Find(&book).Error
	return book, err
}

// 批量添加
func (b *BooksClient) BatchAddition(ctx context.Context, books []Books) error {
	return b.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&books).Error; err != nil {
			return err
		}
		return nil
	})
}

func (b *BooksClient) GetBookList(ctx context.Context, noPage bool, page, pageSize int, number, name, author, publisher, onBorrow string, dictNodeIds []string) (bookList []Books, total int64, err error) {
	query := b.db.Table(BooksTableName)
	if number != "" {
		query = query.Where("number LIKE ?", "%"+number+"%")
	}
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if author != "" {
		query = query.Where("author LIKE ?", "%"+author+"%")
	}
	if publisher != "" {
		query = query.Where("publisher LIKE ?", "%"+publisher+"%")
	}
	if len(dictNodeIds) != 0 {
		query = query.Where("dictionary_node_id in ?", dictNodeIds)
	}
	if onBorrow == "true" {
		query = query.Where("on_borrow = ?", true)
	}
	if onBorrow == "false" {
		query = query.Where("on_borrow = ?", false)
	}
	query = query.Where("is_delete = ?", 0).Order("number asc")
	query.Count(&total)

	if !noPage {
		query = query.Offset((page - 1) * pageSize).Limit(pageSize)
	}
	err = query.Find(&bookList).Error
	return bookList, total, err
}

func (b *BooksClient) Update(ctx context.Context, bookInfo Books) error {
	err := b.db.WithContext(ctx).Updates(&bookInfo).Error
	return err
}

// 根据id修改在册总数
func (b *BooksClient) UpdateRegisterCountById(ctx context.Context, id, userId string, regCount, surCount int) error {
	err := b.db.WithContext(ctx).
		Table(BooksTableName).
		Where("id = ?", id).
		Update("register_count", regCount).
		Update("surplus_count", surCount).
		Update("updated_by", userId).Error
	return err
}

// Books 书籍表
type Books struct {
	Id               string    `gorm:"column:id;primary_key;NOT NULL;comment:'主键id'"`
	Status           int8      `gorm:"column:status;NOT NULL;comment:'状态（0未启用  1启用）'"`
	IsDelete         int8      `gorm:"column:is_delete;NOT NULL;comment:'是否删除（0未删除  1已删除）'"`
	Sort             int       `gorm:"column:sort;NOT NULL;comment:'序号'"`
	Number           string    `gorm:"column:number;NOT NULL;comment:'书籍编号'"`
	Name             string    `gorm:"column:name;NOT NULL;comment:'书籍名称'"`
	Author           string    `gorm:"column:author;NOT NULL;comment:'作者/编者'"`
	Publisher        string    `gorm:"column:publisher;NOT NULL;comment:'出版社'"`
	BookType         string    `gorm:"column:book_type;NOT NULL;comment:'书籍类型'"`
	RegisterCount    int32     `gorm:"column:register_count;NOT NULL;comment:'总在册数'"`
	ReceiveCount     int32     `gorm:"column:receive_count;NOT NULL;comment:'领用数'"`
	BorrowCount      int32     `gorm:"column:borrow_count;NOT NULL;comment:'借用数'"`
	OnBorrow         bool      `gorm:"column:on_borrow;NOT NULL;comment:'借用状态(false未借用  true借用中)'"`
	SurplusCount     int32     `gorm:"column:surplus_count;NOT NULL;comment:'剩余数'"`
	CreatedTime      time.Time `gorm:"column:created_time;default:CURRENT_TIMESTAMP;NOT NULL;comment:'创建时间'"`
	UpdatedTime      time.Time `gorm:"column:updated_time;default:CURRENT_TIMESTAMP;NOT NULL;comment:'更新时间'"`
	CreatedBy        string    `gorm:"column:created_by;NOT NULL;comment:'创建人'"`
	UpdatedBy        string    `gorm:"column:updated_by;NOT NULL;comment:'修改人'"`
	DictionaryNodeID string    `gorm:"column:dictionary_node_id;NOT NULL;comment:'类型id'"`
	FileID           string    `gorm:"column:file_id;NOT NULL;comment:'书籍文件id'"`
	OrganizationId   string    `gorm:"column:organization_id;NOT NULL;comment:'所属组织'"`
}

// TableName 表名
func (b *Books) TableName() string {
	return BooksTableName
}
