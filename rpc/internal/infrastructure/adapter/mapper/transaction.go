package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

//go:generate mockgen -source=transaction.go -destination=transaction_mock.go -package=mapper
type Transaction interface {
	Begin(ctx context.Context) (Transaction, error)
	GetTX() *gorm.DB
	Commit() error
	Rollback() error
	AutoCommit(err *error)
}

type transactionImpl struct {
	db *gorm.DB
}

func NewTransaction(db *gorm.DB) Transaction {
	return &transactionImpl{
		db: db,
	}
}

func (p *transactionImpl) Begin(ctx context.Context) (Transaction, error) {
	tx := p.db.Begin().WithContext(ctx)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return NewTransaction(tx), nil
}

func (p *transactionImpl) GetTX() *gorm.DB {
	return p.db
}

func (p *transactionImpl) Commit() error {
	return p.db.Commit().Error
}

func (p *transactionImpl) Rollback() error {
	return p.db.Rollback().Error
}

func (p *transactionImpl) AutoCommit(err *error) {
	if r := recover(); r != nil {
		p.Rollback()
		return
	}
	if *err != nil {
		err2 := p.Rollback()
		if err2 != nil {
			logc.Error(p.db.Statement.Context, err2)
		}
		return
	}

	err2 := p.Commit()
	if err2 != nil {
		logc.Error(p.db.Statement.Context, err2)
	}
}
