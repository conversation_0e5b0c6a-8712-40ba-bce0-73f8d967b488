package mapper

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameExternalDocumentLibrary = "external_document_library"
)

type ExternalDocumentLibraryClient struct {
	db *gorm.DB
}

func NewExternalDocumentLibraryClient(db *DocvaultDB) *ExternalDocumentLibraryClient {
	return &ExternalDocumentLibraryClient{db: db.GetDB()}
}

func (c *ExternalDocumentLibraryClient) CreateInBatches(ctx context.Context, externalDocuments []ExternalDocumentLibrary) error {
	return c.db.WithContext(ctx).CreateInBatches(externalDocuments, 100).Error
}

func (c *ExternalDocumentLibraryClient) GetByID(ctx context.Context, id string) (ExternalDocumentLibrary, error) {
	externalDocument := ExternalDocumentLibrary{}
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&externalDocument).Error; err != nil {
		return ExternalDocumentLibrary{}, err
	}
	return externalDocument, nil
}

func (c *ExternalDocumentLibraryClient) Update(ctx context.Context, externalDocument ExternalDocumentLibrary) error {
	return c.db.WithContext(ctx).Model(&externalDocument).Where("id=?", externalDocument.ID).Updates(externalDocument).Error
}

func (c *ExternalDocumentLibraryClient) BatchGetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds []string) ([]DictNodeIdAndNumberPostfix, error) {
	var result []DictNodeIdAndNumberPostfix
	err := c.db.WithContext(ctx).Model(&ExternalDocumentLibrary{}).Select("type_dictionary_node_id as dict_node_id, max(number_postfix) as number_postfix").Where("organization_id = ? AND type_dictionary_node_id IN ?", orgID, TypeDictionaryNodeIds).Group("type_dictionary_node_id").Find(&result).Error
	return result, err
}

func (c *ExternalDocumentLibraryClient) GetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds string) (int, error) {
	maxNo := 0
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Select("ifnull(max(number_postfix), 0) as max_no").Where("organization_id = ? and type_dictionary_node_id = ?", orgID, TypeDictionaryNodeIds).Find(&maxNo).Error; err != nil {
		return 0, err
	}
	return maxNo, nil
}

func (c *ExternalDocumentLibraryClient) GetExternalDocList(ctx context.Context, req ExternalDocumentPage) ([]ExternalDocumentLibrary, int64, error) {
	var docs []ExternalDocumentLibrary
	total := int64(0)

	query := c.db.WithContext(ctx).Model(&ExternalDocumentLibrary{}).Where("organization_id = ?", req.OrgID)

	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	if req.Number != "" {
		query = query.Where("number LIKE ?", "%"+req.Number+"%")
	}
	if req.OriginalNumber != "" {
		query = query.Where("original_number LIKE ?", "%"+req.OriginalNumber+"%")
	}
	if req.OriginalDocNumber != "" {
		query = query.Where("original_doc_number LIKE ?", "%"+req.OriginalDocNumber+"%")
	}
	if req.PublishDocNumber != "" {
		query = query.Where("publish_doc_number LIKE ?", "%"+req.PublishDocNumber+"%")
	}
	if req.PublishDepartment != "" {
		query = query.Where("publish_department LIKE ?", "%"+req.PublishDepartment+"%")
	}

	if len(req.TypeDictionaryNodeIds) > 0 {
		query = query.Where("type_dictionary_node_id IN ?", req.TypeDictionaryNodeIds)
	}
	if req.DomainDictionaryNodeId != "" {
		query = query.Where("domain_dictionary_node_id = ?", req.DomainDictionaryNodeId)
	}
	if req.AuthenticationDictionaryNodeId != "" {
		query = query.Where("authentication_dictionary_node_ids LIKE ?", "%"+req.AuthenticationDictionaryNodeId+"%")
	}

	if req.BeAttachedFile == "1" {
		query = query.Where("file_id IS NOT NULL AND file_id != ''")
	} else if req.BeAttachedFile == "2" {
		query = query.Where("file_id IS NULL OR file_id = ''")
	}

	if req.Status != 0 {
		query = query.Where("status = ?", req.Status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if !req.NoPage {
		query = query.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize)
	}

	if err := query.Find(&docs).Error; err != nil {
		logc.Error(ctx, "GetExternalDocList", err)
		return nil, 0, err
	}

	return docs, total, nil
}

// ExternalDocumentLibrary 是 external_document_library 表的GORM模型
type ExternalDocumentLibrary struct {
	ID                              string    `gorm:"type:varchar(64);primary_key"`
	Number                          string    `gorm:"type:varchar(255);column:number"`
	Version                         string    `gorm:"type:varchar(255);column:version"`
	NumberPostfix                   int       `gorm:"type:int(11);column:number_postfix"`
	OriginalNumber                  string    `gorm:"type:varchar(255);column:original_number"`
	OriginalVersion                 string    `gorm:"type:varchar(255);column:original_version"`
	Name                            string    `gorm:"type:varchar(255);column:name"`
	DocType                         string    `gorm:"type:varchar(255);column:doc_type"`
	Domain                          string    `gorm:"type:varchar(255);column:domain"`
	OriginalDocNumber               string    `gorm:"type:varchar(255);column:original_doc_number"`
	PublishDocNumber                string    `gorm:"type:varchar(255);column:publish_doc_number"`
	PublishDepartment               string    `gorm:"type:varchar(255);column:publish_department"`
	PublishDate                     time.Time `gorm:"type:datetime;column:publish_date"`
	EffectiveDate                   time.Time `gorm:"type:datetime;column:effective_date"`
	Authentications                 string    `gorm:"type:varchar(500);column:authentications"`
	Status                          int8      `gorm:"type:tinyint;column:status"`
	TypeDictionaryNodeId            string    `gorm:"type:varchar(64);column:type_dictionary_node_id"`
	DomainDictionaryNodeId          string    `gorm:"type:varchar(64);column:domain_dictionary_node_id"`
	AuthenticationDictionaryNodeIds string    `gorm:"type:varchar(500);column:authentication_dictionary_node_ids"`
	FileID                          string    `gorm:"type:varchar(64);column:file_id"`
	OrganizationID                  string    `gorm:"type:varchar(64);column:organization_id"`
	CreatedBy                       string    `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy                       string    `gorm:"type:varchar(64);column:updated_by"`
	CreatedAt                       time.Time `gorm:"column:created_at"`
	UpdatedAt                       time.Time `gorm:"column:updated_at"`
	TenantID                        string    `gorm:"type:varchar(64);column:tenant_id"`
}

// TableName 指定GORM模型对应的表名
func (ExternalDocumentLibrary) TableName() string {
	return TableNameExternalDocumentLibrary
}

type DictNodeIdAndNumberPostfix struct {
	DictNodeId    string
	NumberPostfix int
}

type ExternalDocumentPage struct {
	Page                           int
	PageSize                       int
	NoPage                         bool
	Number                         string
	Name                           string
	OriginalNumber                 string
	OriginalDocNumber              string
	PublishDocNumber               string
	PublishDepartment              string
	TypeDictionaryNodeIds          []string
	DomainDictionaryNodeId         string
	AuthenticationDictionaryNodeId string
	BeAttachedFile                 string
	Status                         int
	OrgID                          string
	TenantID                       string
}

// FindDocumentsByStatusAndEffectiveDate 查询指定状态且 effective_date<=指定日期的文档
func (c *ExternalDocumentLibraryClient) FindDocumentsByStatusAndEffectiveDate(ctx context.Context, status int8, effectiveDate time.Time) ([]ExternalDocumentLibrary, error) {
	docs := []ExternalDocumentLibrary{}
	err := c.db.WithContext(ctx).
		Table(TableNameExternalDocumentLibrary).
		Where("status = ? AND effective_date <= ?", status, effectiveDate.Format("2006-01-02")).
		Find(&docs).Error
	if err != nil {
		return nil, err
	}
	return docs, nil
}

// BatchUpdateDocumentStatus 批量根据 id 列表更新 status
func (c *ExternalDocumentLibraryClient) BatchUpdateDocumentStatus(ctx context.Context, ids []string, newStatus int8) error {
	if len(ids) == 0 {
		return nil
	}
	return c.db.WithContext(ctx).
		Table(TableNameExternalDocumentLibrary).
		Where("id IN ?", ids).
		Update("status", newStatus).Error
}

func (c *ExternalDocumentLibraryClient) GetExternalDocNamesByOrgID(ctx context.Context, orgID string) ([]string, error) {
	var names []string
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Select("name").Where("organization_id = ?", orgID).Find(&names).Error; err != nil {
		return nil, err
	}
	return names, nil
}

func (c *ExternalDocumentLibraryClient) GetExternalDocByOrgIDAndName(ctx context.Context, orgID string, name string) (*ExternalDocumentLibrary, error) {
	var doc ExternalDocumentLibrary
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Where("organization_id = ? AND name = ?", orgID, name).First(&doc).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &doc, nil
}

func (c *ExternalDocumentLibraryClient) GetByIDs(ctx context.Context, ids []string) ([]ExternalDocumentLibrary, error) {
	var docs []ExternalDocumentLibrary
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Where("id IN ?", ids).Find(&docs).Error; err != nil {
		return nil, err
	}
	return docs, nil
}

func (c *ExternalDocumentLibraryClient) GetByOrgIDAndNames(ctx context.Context, orgID string, names []string) ([]ExternalDocumentLibrary, error) {
	var docs []ExternalDocumentLibrary
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Where("organization_id = ? AND name IN ?", orgID, names).Find(&docs).Error; err != nil {
		return nil, err
	}
	return docs, nil
}
