package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordFile = "recycle_record_files"
)

// RecycleRecordFile 对应 recycle_record_files 表
type RecycleRecordFile struct {
	ID       string `gorm:"type:varchar(64);primary_key"`
	RecordID string `gorm:"type:varchar(64);index;comment:'关联RecycleRecord的ID'"`
	FileID   string `gorm:"type:varchar(64);comment:'文件id'"`
	FileForm int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
}

func (RecycleRecordFile) TableName() string {
	return TableNameRecycleRecordFile
}

// RecycleRecordFileClient 是 recycle_record_files 表的数据访问客户端
type RecycleRecordFileClient struct {
	db *gorm.DB
}

// NewRecycleRecordFileClient 创建一个新的 RecycleRecordFileClient 实例
func NewRecycleRecordFileClient(db *DocvaultDB) *RecycleRecordFileClient {
	return &RecycleRecordFileClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) CreateWithTx(ctx context.Context, tx Transaction, file *RecycleRecordFile) error {
	if err := tx.GetTX().WithContext(ctx).Create(file).Error; err != nil {
		logc.Error(ctx, "Failed to create recycle record file", err)
		return err
	}
	return nil
}

// GetByRecordID 根据 RecordID 获取所有相关的 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) GetByRecordID(ctx context.Context, recordID string) ([]*RecycleRecordFile, error) {
	var files []*RecycleRecordFile
	if err := c.db.WithContext(ctx).Where("record_id = ?", recordID).Find(&files).Error; err != nil {
		logc.Error(ctx, "Failed to get recycle record files by record ID", err)
		return nil, err
	}
	return files, nil
}

// GetByID 根据 ID 获取 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) GetByID(ctx context.Context, id string) (*RecycleRecordFile, error) {
	var file RecycleRecordFile
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get recycle record file by ID", err)
		return nil, err
	}
	return &file, nil
}
