package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameDisposalRecordFile = "disposal_record_files"
)

// DisposalRecordFile 对应 disposal_record_files 表
type DisposalRecordFile struct {
	ID       string `gorm:"type:varchar(64);primary_key"`
	RecordID string `gorm:"type:varchar(64);index;comment:'关联DisposalRecord的ID'"`
	FileID   string `gorm:"type:varchar(64);comment:'文件id'"`
	FileForm int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
}

func (DisposalRecordFile) TableName() string {
	return TableNameDisposalRecordFile
}

// DisposalRecordFileClient 是 disposal_record_files 表的数据访问客户端
type DisposalRecordFileClient struct {
	db *gorm.DB
}

// NewDisposalRecordFileClient 创建一个新的 DisposalRecordFileClient 实例
func NewDisposalRecordFileClient(db *DocvaultDB) *DisposalRecordFileClient {
	return &DisposalRecordFileClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 DisposalRecordFile 记录
func (c *DisposalRecordFileClient) CreateWithTx(ctx context.Context, tx Transaction, file *DisposalRecordFile) error {
	if err := tx.GetTX().WithContext(ctx).Create(file).Error; err != nil {
		logc.Error(ctx, "Failed to create disposal record file", err)
		return err
	}
	return nil
}

// GetByRecordID 根据 RecordID 获取所有相关的 DisposalRecordFile 记录
func (c *DisposalRecordFileClient) GetByRecordID(ctx context.Context, recordID string) ([]*DisposalRecordFile, error) {
	var files []*DisposalRecordFile
	if err := c.db.WithContext(ctx).Where("record_id = ?", recordID).Find(&files).Error; err != nil {
		logc.Error(ctx, "Failed to get disposal record files by record ID", err)
		return nil, err
	}
	return files, nil
}

// GetByID 根据 ID 获取 DisposalRecordFile 记录
func (c *DisposalRecordFileClient) GetByID(ctx context.Context, id string) (*DisposalRecordFile, error) {
	var file DisposalRecordFile
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get disposal record file by ID", err)
		return nil, err
	}
	return &file, nil
}
