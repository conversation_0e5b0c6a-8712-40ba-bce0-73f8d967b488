package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordPermission = "recycle_record_permissions"
)

// RecycleRecordPermission 对应 recycle_record_permissions 表
type RecycleRecordPermission struct {
	ID           string `gorm:"type:varchar(64);primary_key"`
	FileRecordID string `gorm:"type:varchar(64);index;comment:'关联RecycleRecordFile的ID'"`
	UserID       string `gorm:"type:varchar(64);comment:'接收人ID'"`
}

func (RecycleRecordPermission) TableName() string {
	return TableNameRecycleRecordPermission
}

// RecycleRecordPermissionClient 是 recycle_record_permissions 表的数据访问客户端
type RecycleRecordPermissionClient struct {
	db *gorm.DB
}

// NewRecycleRecordPermissionClient 创建一个新的 RecycleRecordPermissionClient 实例
func NewRecycleRecordPermissionClient(db *DocvaultDB) *RecycleRecordPermissionClient {
	return &RecycleRecordPermissionClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 RecycleRecordPermission 记录
func (c *RecycleRecordPermissionClient) CreateWithTx(ctx context.Context, tx Transaction, permission *RecycleRecordPermission) error {
	if err := tx.GetTX().WithContext(ctx).Create(permission).Error; err != nil {
		logc.Error(ctx, "Failed to create recycle record permission", err)
		return err
	}
	return nil
}

// GetByFileRecordID 根据 FileRecordID 获取所有相关的 RecycleRecordPermission 记录
func (c *RecycleRecordPermissionClient) GetByFileRecordID(ctx context.Context, fileRecordID string) ([]*RecycleRecordPermission, error) {
	var permissions []*RecycleRecordPermission
	if err := c.db.WithContext(ctx).Where("file_record_id = ?", fileRecordID).Find(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to get recycle record permissions by file record ID", err)
		return nil, err
	}
	return permissions, nil
}

// BatchCreateWithTx 批量创建 RecycleRecordPermission 记录
func (c *RecycleRecordPermissionClient) BatchCreateWithTx(ctx context.Context, tx Transaction, permissions []*RecycleRecordPermission) error {
	if len(permissions) == 0 {
		return nil
	}
	if err := tx.GetTX().WithContext(ctx).Create(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to batch create recycle record permissions", err)
		return err
	}
	return nil
}

// GetRecycleDocPermissionUsers 根据文件ID、文件来源和文件权限获取回收权限用户信息
func (c *RecycleRecordPermissionClient) GetRecycleDocPermissionUsers(ctx context.Context, fileId string, fileFrom, filePermission int32) ([]struct {
	UserID        string `gorm:"column:user_id"`
	RecycleStatus int    `gorm:"column:recycle_status"`
}, error) {
	var result []struct {
		UserID        string `gorm:"column:user_id"`
		RecycleStatus int    `gorm:"column:recycle_status"`
	}

	err := c.db.WithContext(ctx).
		Table("recycle_record_permissions as rp").
		Select("rp.user_id, 2 as recycle_status"). // 回收状态固定为2待审批
		Joins("LEFT JOIN recycle_record_files as rf ON rp.file_record_id = rf.id").
		Joins("LEFT JOIN recycle_records as rr ON rf.record_id = rr.id").
		Joins("LEFT JOIN distribute_records as dr ON rr.distribute_record_id = dr.id").
		Where("rf.file_id = ? AND dr.file_type = ? AND rf.file_form = ?",
			fileId, fileFrom, filePermission).
		Group("rp.user_id").
		Find(&result).Error

	if err != nil {
		logc.Error(ctx, "Failed to get recycle doc permission users", err)
		return nil, err
	}

	return result, nil
}
