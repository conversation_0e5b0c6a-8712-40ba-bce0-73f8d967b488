// Code generated by MockGen. DO NOT EDIT.
// Source: phoenix.go

// Package clientx is a generated GoMock package.
package clientx

import (
	context "context"
	entity "docvault/rpc/internal/infrastructure/adapter/clientx/entity"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockPhoenixClient is a mock of PhoenixClient interface.
type MockPhoenixClient struct {
	ctrl     *gomock.Controller
	recorder *MockPhoenixClientMockRecorder
}

// MockPhoenixClientMockRecorder is the mock recorder for MockPhoenixClient.
type MockPhoenixClientMockRecorder struct {
	mock *MockPhoenixClient
}

// NewMockPhoenixClient creates a new mock instance.
func NewMockPhoenixClient(ctrl *gomock.Controller) *MockPhoenixClient {
	mock := &MockPhoenixClient{ctrl: ctrl}
	mock.recorder = &MockPhoenixClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPhoenixClient) EXPECT() *MockPhoenixClientMockRecorder {
	return m.recorder
}

// GetFileInfo mocks base method.
func (m *MockPhoenixClient) GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileInfo", ctx, fileId)
	ret0, _ := ret[0].(entity.FileInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileInfo indicates an expected call of GetFileInfo.
func (mr *MockPhoenixClientMockRecorder) GetFileInfo(ctx, fileId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileInfo", reflect.TypeOf((*MockPhoenixClient)(nil).GetFileInfo), ctx, fileId)
}

// GetUserNicknames mocks base method.
func (m *MockPhoenixClient) GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNicknames", ctx, uids)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNicknames indicates an expected call of GetUserNicknames.
func (mr *MockPhoenixClientMockRecorder) GetUserNicknames(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNicknames", reflect.TypeOf((*MockPhoenixClient)(nil).GetUserNicknames), ctx, uids)
}

// GetWorkflow mocks base method.
func (m *MockPhoenixClient) GetWorkflow(ctx context.Context, workflowId string) (entity.GetWorkflowRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", ctx, workflowId)
	ret0, _ := ret[0].(entity.GetWorkflowRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockPhoenixClientMockRecorder) GetWorkflow(ctx, workflowId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockPhoenixClient)(nil).GetWorkflow), ctx, workflowId)
}
