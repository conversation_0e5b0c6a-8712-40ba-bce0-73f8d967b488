package clientx

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/clientx/entity"
)

//go:generate mockgen -source=macrohard.go -destination=macrohard_mock.go -package=clientx

// MacrohardClient 定义了与 Macrohard 服务交互的接口
type MacrohardClient interface {
	// 内容填充
	DocxContentFilling(ctx context.Context, req *entity.DocxContentFillingRequest) (fileId string, err error)

	// 转换为 PDF
	DocxConvertToPdf(ctx context.Context, docxFileID string) (fileId string, err error)
}
