package clientx

import (
	"context"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"time"
)

type FileService interface {
	UploadBase64Image(ctx context.Context, base64Str string) (fileId string, err error)
}

type LocalFileService struct {
	BaseDir string
}

func NewLocalFileService(baseDir string) *LocalFileService {
	return &LocalFileService{BaseDir: baseDir}
}

// UploadBase64Image 将Base64图片存本地，返回唯一fileId（可用时间戳+随机数模拟）
func (l *LocalFileService) UploadBase64Image(ctx context.Context, base64Str string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return "", err
	}
	fileId := fmt.Sprintf("sig_%d.png", time.Now().UnixNano())
	filePath := l.BaseDir + "/" + fileId
	if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
		return "", err
	}
	return fileId, nil
}
