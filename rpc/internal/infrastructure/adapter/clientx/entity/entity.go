package entity

type GetUserNicknamesResp struct {
	CommonResp
	Data map[string]string `json:"data"`
}

type CommonResp struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

type FileInfo struct {
	ID         string `json:"id"`
	UUID       string `json:"UUID"`
	OriginName string `json:"originName"`
	Name       string `json:"name"`
	FileType   uint8  `json:"fileType"`
	OpenStatus uint8  `json:"openStatus"`
	Size       uint64 `json:"size"`
	Path       string `json:"path"`
	Hash       string `json:"hash"`
	Url        string `json:"url"`
}

type GetFileInfoResp struct {
	CommonResp
	Data FileInfo `json:"data"`
}

type DocxContentFillingRequest struct {
	// 文件ID
	FileID string `json:"fileId"`
	// 新文件名
	NewFileName string `json:"newFileName"`
	// 填充参数
	Params []DocxContentFillingFieldParam `json:"params"`
}

// 填充参数
type DocxContentFillingFieldParam struct {
	// 字段类型: text, image
	FieldType string `json:"fieldType"`
	// 字段key
	FieldKey string `json:"fieldKey"`
	// 填充类型: text, base64(fieldType=image格式)
	FieldFillingType string `json:"fieldFillingType"`
	// 填充值
	FieldValue interface{} `json:"fieldValue"`
	// 图片宽度
	ImgWidth int `json:"imgWidth"`
	// 图片高度
	ImgHeight int `json:"imgHeight"`
}

type DocxContentFillingResp struct {
	CommonResp
	Data DocxContentFillingRespData `json:"data"`
}

type DocxContentFillingRespData struct {
	FileID string `json:"fileId"`
}

type GetWorkflowResp struct {
	CommonResp
	Data GetWorkflowRespData `json:"data"`
}
type GetWorkflowRespData struct {
	FlowName                string                    `json:"flowName"`
	FlowID                  string                    `json:"flowId"`
	FlowStatus              string                    `json:"flowStatus"`
	FlowCreatedUserNickname string                    `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64                     `json:"flowCreatedTime"`
	FormContent             string                    `json:"formContent"`
	Nodes                   []GetWorkflowRespDataNode `json:"nodes"`
}

type GetWorkflowRespDataNode struct {
	NodeName    string                            `json:"nodeName"`
	NodeID      string                            `json:"nodeId"`
	Status      string                            `json:"status"`
	SigningKind string                            `json:"signingKind"`
	Approvers   []GetWorkflowRespDataNodeApprover `json:"approvers"`
}

type GetWorkflowRespDataNodeApprover struct {
	TaskID           string `json:"taskId"`
	ApproverID       string `json:"approverId"`
	ApproverNickname string `json:"approverNickname"`
	Status           string `json:"status"`
	UpdatedAt        int64  `json:"updatedAt"`
	Comment          string `json:"comment"`
}
