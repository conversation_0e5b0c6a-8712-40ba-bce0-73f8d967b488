package clientx

import (
	"context"
	"docvault/rpc/internal/config"
	"docvault/rpc/internal/infrastructure/adapter/clientx/entity"
	"errors"
	"time"

	"gitee.com/damengde/teemo"
)

//go:generate mockgen -source=macrohard.go -destination=macrohard_mock.go -package=clientx

// MacrohardClient 定义了与 Macrohard 服务交互的接口
type MacrohardClientImpl struct {
	cl *teemo.Client
}

func NewMacrohardClientImpl(conf config.Config) MacrohardClient {
	service := conf.MicroServices.GetService("macrohard")
	if service.Url == "" {
		return nil
	}
	cl := teemo.New(teemo.WithBaseURL(service.Url), teemo.WithTimeout(time.Second*20))
	return &MacrohardClientImpl{
		cl: cl,
	}
}

func (c *MacrohardClientImpl) DocxContentFilling(ctx context.Context, req *entity.DocxContentFillingRequest) (fileId string, err error) {
	respBody, err := teemo.NewRequest[entity.DocxContentFillingResp](c.cl).
		Post(ctx, "/macrohard/api/v1/word/contentFilling", req)
	if err != nil {
		return "", err
	}
	if respBody.Code != 0 {
		return "", errors.New(respBody.Msg)
	}
	return respBody.Data.FileID, nil
}

func (c *MacrohardClientImpl) DocxConvertToPdf(ctx context.Context, docxFileID string) (fileId string, err error) {
	respBody, err := teemo.NewRequest[entity.CommonResp](c.cl).
		Post(ctx, "/macrohard/api/v1/common/docxAndConvertPdf", map[string]interface{}{
			"fileId": docxFileID,
		})
	if err != nil {
		return "", err
	}
	if respBody.Code != 0 {
		return "", errors.New(respBody.Msg)
	}
	return respBody.Data.(string), nil
}
