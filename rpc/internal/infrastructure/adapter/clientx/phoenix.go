package clientx

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/clientx/entity"
)

//go:generate mockgen -source=phoenix.go -destination=phoenix_mock.go -package=clientx

// PhoenixClient 定义了与 Phoenix 服务交互的接口
type PhoenixClient interface {
	// 查询用户昵称
	GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error)

	// 查询文件信息
	GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error)

	// 获取审批流信息
	GetWorkflow(ctx context.Context, workflowId string) (progress entity.GetWorkflowRespData, err error)
}
