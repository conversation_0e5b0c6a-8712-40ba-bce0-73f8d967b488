package clientx

import (
	"context"
	"docvault/rpc/internal/config"
	"docvault/rpc/internal/infrastructure/adapter/clientx/entity"
	"docvault/utils"
	"errors"
	"time"

	"gitee.com/damengde/teemo"
	"github.com/zeromicro/go-zero/core/logc"
)

type PhoenixClientImpl struct {
	cl   *teemo.Client
	conf config.Config
}

func NewPhoenixClientImpl(conf config.Config) PhoenixClient {
	service := conf.MicroServices.GetService("phoenix")
	if service.Url == "" {
		return nil
	}
	cl := teemo.New(teemo.WithBaseURL(service.Url), teemo.WithTimeout(time.Second*20))
	return &PhoenixClientImpl{
		cl:   cl,
		conf: conf,
	}
}

func (p *PhoenixClientImpl) GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error) {
	respBody, err := teemo.NewRequest[entity.GetUserNicknamesResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Post(ctx, "/saas/api/v1/user/nicknames/byids", map[string]interface{}{
			"ids": uids,
		})
	if err != nil {
		return nil, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return nil, err
	}

	return respBody.Data, nil
}

func (p *PhoenixClientImpl) GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error) {
	respBody, err := teemo.NewRequest[entity.GetFileInfoResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/file/get/"+fileId, nil)
	if err != nil {
		return entity.FileInfo{}, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return entity.FileInfo{}, err
	}
	return respBody.Data, nil
}

func (p *PhoenixClientImpl) GetWorkflow(ctx context.Context, workflowId string) (entity.GetWorkflowRespData, error) {
	respBody, err := teemo.NewRequest[entity.GetWorkflowResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/workflow", map[string]interface{}{
			"flowId": workflowId,
		})
	if err != nil {
		return entity.GetWorkflowRespData{}, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return entity.GetWorkflowRespData{}, err
	}

	return respBody.Data, nil
}
func (p *PhoenixClientImpl) checkResult(ctx context.Context, resp entity.CommonResp) error {
	if resp.Code != 0 {
		logc.Errorf(ctx, "phoenix request fail, response code:%d msg:%s", resp.Code, resp.Msg)
		return errors.New(resp.Msg)
	}
	return nil
}

func (p *PhoenixClientImpl) getHeaders(ctx context.Context) map[string]string {
	return map[string]string{
		"X-Session-User-Id":         utils.GetContextUserID(ctx),
		"X-Session-Tenant-Id":       utils.GetContextTenantID(ctx),
		"X-Session-Organization-Id": utils.GetContextOrganizationID(ctx),
	}
}
