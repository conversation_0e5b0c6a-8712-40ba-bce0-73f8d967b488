// Code generated by MockGen. DO NOT EDIT.
// Source: id_generate_addons.go

// Package addons is a generated GoMock package.
package addons

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIDGeneratorAddons is a mock of IDGeneratorAddons interface.
type MockIDGeneratorAddons struct {
	ctrl     *gomock.Controller
	recorder *MockIDGeneratorAddonsMockRecorder
}

// MockIDGeneratorAddonsMockRecorder is the mock recorder for MockIDGeneratorAddons.
type MockIDGeneratorAddonsMockRecorder struct {
	mock *MockIDGeneratorAddons
}

// NewMockIDGeneratorAddons creates a new mock instance.
func NewMockIDGeneratorAddons(ctrl *gomock.Controller) *MockIDGeneratorAddons {
	mock := &MockIDGeneratorAddons{ctrl: ctrl}
	mock.recorder = &MockIDGeneratorAddonsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDGeneratorAddons) EXPECT() *MockIDGeneratorAddonsMockRecorder {
	return m.recorder
}

// GenerateID mocks base method.
func (m *MockIDGeneratorAddons) GenerateID() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateID")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GenerateID indicates an expected call of GenerateID.
func (mr *MockIDGeneratorAddonsMockRecorder) GenerateID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateID", reflect.TypeOf((*MockIDGeneratorAddons)(nil).GenerateID))
}

// GenerateIDString mocks base method.
func (m *MockIDGeneratorAddons) GenerateIDString() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateIDString")
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateIDString indicates an expected call of GenerateIDString.
func (mr *MockIDGeneratorAddonsMockRecorder) GenerateIDString() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateIDString", reflect.TypeOf((*MockIDGeneratorAddons)(nil).GenerateIDString))
}
