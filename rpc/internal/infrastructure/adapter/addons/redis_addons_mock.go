// Code generated by MockGen. DO NOT EDIT.
// Source: redis_addons.go

// Package addons is a generated GoMock package.
package addons

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	redis "github.com/zeromicro/go-zero/core/stores/redis"
)

// MockRedisAddons is a mock of RedisAddons interface.
type MockRedisAddons struct {
	ctrl     *gomock.Controller
	recorder *MockRedisAddonsMockRecorder
}

// MockRedisAddonsMockRecorder is the mock recorder for MockRedisAddons.
type MockRedisAddonsMockRecorder struct {
	mock *MockRedisAddons
}

// NewMockRedisAddons creates a new mock instance.
func NewMockRedisAddons(ctrl *gomock.Controller) *MockRedisAddons {
	mock := &MockRedisAddons{ctrl: ctrl}
	mock.recorder = &MockRedisAddonsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedisAddons) EXPECT() *MockRedisAddonsMockRecorder {
	return m.recorder
}

// Del mocks base method.
func (m *MockRedisAddons) Del(ctx context.Context, key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Del", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// Del indicates an expected call of Del.
func (mr *MockRedisAddonsMockRecorder) Del(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Del", reflect.TypeOf((*MockRedisAddons)(nil).Del), ctx, key)
}

// Exists mocks base method.
func (m *MockRedisAddons) Exists(ctx context.Context, key string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exists", ctx, key)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exists indicates an expected call of Exists.
func (mr *MockRedisAddonsMockRecorder) Exists(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockRedisAddons)(nil).Exists), ctx, key)
}

// Get mocks base method.
func (m *MockRedisAddons) Get(ctx context.Context, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRedisAddonsMockRecorder) Get(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRedisAddons)(nil).Get), ctx, key)
}

// GetRawClient mocks base method.
func (m *MockRedisAddons) GetRawClient() *redis.Redis {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRawClient")
	ret0, _ := ret[0].(*redis.Redis)
	return ret0
}

// GetRawClient indicates an expected call of GetRawClient.
func (mr *MockRedisAddonsMockRecorder) GetRawClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRawClient", reflect.TypeOf((*MockRedisAddons)(nil).GetRawClient))
}

// Set mocks base method.
func (m *MockRedisAddons) Set(ctx context.Context, key, value string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", ctx, key, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockRedisAddonsMockRecorder) Set(ctx, key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockRedisAddons)(nil).Set), ctx, key, value)
}

// SetEx mocks base method.
func (m *MockRedisAddons) SetEx(ctx context.Context, key, value string, seconds int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEx", ctx, key, value, seconds)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEx indicates an expected call of SetEx.
func (mr *MockRedisAddonsMockRecorder) SetEx(ctx, key, value, seconds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEx", reflect.TypeOf((*MockRedisAddons)(nil).SetEx), ctx, key, value, seconds)
}
