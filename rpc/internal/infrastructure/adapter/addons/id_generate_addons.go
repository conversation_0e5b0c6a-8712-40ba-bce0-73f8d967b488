package addons

import (
	"docvault/rpc/internal/config"
	"strconv"

	"github.com/bwmarrin/snowflake"
)

//go:generate mockgen -destination=id_generate_addons_mock.go -package=addons -source=id_generate_addons.go
type IDGeneratorAddons interface {
	GenerateID() int64
	GenerateIDString() string
}

type IDGeneratorImpl struct {
	node *snowflake.Node
}

func (i *IDGeneratorImpl) GenerateID() int64 {
	return int64(i.node.Generate())
}

func (i *IDGeneratorImpl) GenerateIDString() string {
	return strconv.FormatInt(int64(i.node.Generate()), 10)
}

func NewIdGeneratorAddonsImpl(s config.Config) IDGeneratorAddons {
	generator := newSnowflakeGenerator(s)
	return &IDGeneratorImpl{
		node: generator,
	}
}

// NewSnowflakeGenerator 初始化雪花算法生成器
func newSnowflakeGenerator(s config.Config) *snowflake.Node {
	// 元年
	snowflake.Epoch = s.Snowflake.Epoch
	// 节点bit位
	snowflake.NodeBits = s.Snowflake.NodeBits
	// 步骤bit位
	snowflake.StepBits = s.Snowflake.StepBits

	node, err := snowflake.NewNode(s.Snowflake.Node)
	if err != nil {
		panic(err)
	}
	return node
}
