package addons

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	QuickNameTranslatorUserKey = "quick_name_translator:user:%s"
	QuickNameTranslatorOrgKey  = "quick_name_translator:org:%s"
)

type QuickNameTranslator interface {
	TranslateUserNickname(ctx context.Context, userID string) string
	TranslateOrganizationName(ctx context.Context, organizationID string) string
}

type QuickNameTranslatorImpl struct {
	redis RedisAddons
}

func NewQuickNameTranslatorImpl(redis RedisAddons) QuickNameTranslator {
	return &QuickNameTranslatorImpl{
		redis: redis,
	}
}

func (q *QuickNameTranslatorImpl) TranslateUserNickname(ctx context.Context, userID string) string {
	nickname, err := q.redis.Get(ctx, fmt.Sprintf(QuickNameTranslatorUserKey, userID))
	if err != nil {
		logc.Errorw(ctx, "get user nickname from redis error", logx.LogField{Key: "error", Value: err})
		return ""
	}
	return nickname
}

func (q *QuickNameTranslatorImpl) TranslateOrganizationName(ctx context.Context, organizationID string) string {
	organizationName, err := q.redis.Get(ctx, fmt.Sprintf(QuickNameTranslatorOrgKey, organizationID))
	if err != nil {
		logc.Errorw(ctx, "get organization name from redis error", logx.LogField{Key: "error", Value: err})
		return ""
	}
	return organizationName
}
