package interceptor

import (
	"context"
	"docvault/utils"
	"strconv"
)

type SessionAuth struct {
}

func NewSessionAuth() *SessionAuth {
	return &SessionAuth{}
}

func (s *SessionAuth) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
	userInfo := utils.GetCurrentLoginUser(ctx)
	md := make(map[string]string)
	md["x-session-user-id"] = userInfo.UserId
	md["x-session-tenant-id"] = userInfo.TenantId
	md["x-session-device-kind"] = strconv.Itoa(userInfo.DeviceKind)
	md["x-session-is-virtual-user"] = strconv.FormatBool(userInfo.IsVirtualUser)
	md["x-trace-id"] = utils.GetContextTraceId(ctx)
	return md, nil
}

func (s *SessionAuth) RequireTransportSecurity() bool {
	return false
}
