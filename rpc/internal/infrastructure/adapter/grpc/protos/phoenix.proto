// 声明 proto 语法版本，固定值
syntax = "proto3";

// proto 包名
package phoenix;

// 生成 golang 代码后的包名
option go_package = "./phoenix";

message GetUserInfoReq {
    // 用户ID
    string id = 1;
}

// 用户信息
message UserInfo {
    // 用户ID
    string id = 1;
    // 用户名
    string username = 2;
    // 手机号
    string mobile = 3;
    // 邮箱
    string email = 4;
    // 昵称
    string nickname = 5;
    // 头像
    string avatar = 6;
    // 职位
    string post = 7;
    // 状态（true=启用，false=禁用）
    bool status = 8;
    // 默认租户ID
    string default_tenant_id = 9;
}

message GetUserInfoResp {
    // 用户信息列表
     UserInfo user = 1;
}


message GetUserInfoListReq {
    // 用户ID列表
    repeated string ids = 1;
}

message GetUserInfoListResp {
    // 用户信息列表
    repeated UserInfo users = 1;
}

// 保存操作日志
message SaveSystemOperationLogReq {
    // 操作日志
    SystemOperationLog system_operation_log = 1;
}

message SaveSystemOperationLogResp { 
    
}

message SystemOperationLog {
    // 方法
    string method = 1;
    // API
    string api = 2;
    // 请求体
    string request_body = 3;
    // 响应体
    string response_body = 4;
    // IP
    string ip = 5;
    // 状态码
    int32 http_status = 6;
    // 用户ID
    string user_id = 7;
    // 租户ID
    string tenant_id = 8;
    // 创建时间
    sint64 created_at = 9;
}



message SaveSystemLoginLogReq {
    // 登录日志
    SystemLoginLog system_login_log = 1;
}

message SaveSystemLoginLogResp {
    
}


message SystemLoginLog {
    // 登录日志
    string user_id = 1;
    // 登录用户名
    string login_username = 2;
    // IP
    string ip = 3;
    // 设备类型  web，app，h5
    string device_kind = 4;

    // 租户ID
    string tenant_id = 5;

    // 创建时间
    sint64 created_at = 9;
}

//  用户
service User {
  // 获取用户信息
  rpc GetUserInfo(GetUserInfoReq) returns (GetUserInfoResp);

  // 获取用户信息列表
  rpc GetUserInfoList(GetUserInfoListReq) returns (GetUserInfoListResp);
}

// 系统日志
service SystemLog {
  // 保存操作日志
  rpc SaveSystemOperationLog(stream SaveSystemOperationLogReq) returns (SaveSystemOperationLogResp);

    // 保存登录日志
  rpc SaveSystemLoginLog(SaveSystemLoginLogReq) returns (SaveSystemLoginLogResp);
}
