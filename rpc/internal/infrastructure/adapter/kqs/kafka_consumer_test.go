package kqs

import (
	"context"
	"log"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
)

// mockHandler 用于记录被消费的消息
type mockHandler struct {
	msgs [][]byte
	mu   sync.Mutex
	wg   *sync.WaitGroup
	fail bool // 是否模拟处理失败
}

func (h *mockHandler) Handle(ctx context.Context, message []byte) error {
	h.mu.Lock()
	h.msgs = append(h.msgs, message)
	h.mu.Unlock()
	if h.wg != nil {
		h.wg.Done()
	}
	if h.fail {
		return os.ErrInvalid
	}
	return nil
}

func TestKafkaConsumer_Integration(t *testing.T) {
	topic := "sit_workflow_passed_SIGNATURE_APPROVAL"
	brokers := []string{"192.168.110.74:9092"}
	groupID := "test-group"
	msgValue := []byte("hello-kafka")

	// 1. 生产者写入消息
	writer := kafka.NewWriter(kafka.WriterConfig{
		Brokers: brokers,
		Topic:   topic,
	})
	defer writer.Close()

	if err := writer.WriteMessages(context.Background(), kafka.Message{Value: msgValue, Headers: []kafka.Header{}}); err != nil {
		t.Fatalf("写入Kafka消息失败: %v", err)
	}

	// 2. 启动Consumer
	wg := &sync.WaitGroup{}
	wg.Add(1)
	handler := &mockHandler{wg: wg}
	readerConfig := kafka.ReaderConfig{
		Brokers:  brokers,
		Topic:    topic,
		GroupID:  groupID,
		MinBytes: 1,
		MaxBytes: 10e6,
	}
	consumer := NewKafkaConsumer(readerConfig, handler)

	ctx, cancel := context.WithTimeout(context.WithoutCancel(context.Background()), 10*time.Second)
	defer cancel()

	consumer.Start(ctx)

	// 3. 等待消息被消费
	wg.Wait()

	// 4. 断言
	handler.mu.Lock()
	defer handler.mu.Unlock()
	if len(handler.msgs) == 0 {
		t.Fatalf("未消费到任何消息")
	}
	found := false
	for _, m := range handler.msgs {
		if string(m) == string(msgValue) {
			found = true
			break
		}
	}
	if !found {
		t.Fatalf("未消费到预期消息: %s", msgValue)
		return
	}
	log.Printf("Kafka集成测试通过，消费到消息: %s", msgValue)
}
