package referenceimpl

import (
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"encoding/json"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/datatypes"
)

// TestApprovalInfoSerialization 测试审批信息的序列化和数据库字段映射
func TestApprovalInfoSerialization(t *testing.T) {
	Convey("测试审批信息序列化", t, func() {

		Convey("当创建审批信息时", func() {
			// 准备测试数据
			approvalInfo := entity.ApprovalInfo{
				Auditors: []entity.ApprovalItem{
					{
						UserID:     "auditor1",
						PassedDate: 1640995200, // 2022-01-01 00:00:00
					},
					{
						UserID:     "auditor2",
						PassedDate: 1641081600, // 2022-01-02 00:00:00
					},
				},
				Approvers: []entity.ApprovalItem{
					{
						UserID:     "approver1",
						PassedDate: 1641168000, // 2022-01-03 00:00:00
					},
				},
			}

			<PERSON><PERSON>("应该能够正确序列化为JSON", func() {
				jsonData, err := json.Marshal(approvalInfo)
				So(err, ShouldBeNil)
				So(string(jsonData), ShouldContainSubstring, "auditor1")
				So(string(jsonData), ShouldContainSubstring, "auditor2")
				So(string(jsonData), ShouldContainSubstring, "approver1")
				So(string(jsonData), ShouldContainSubstring, "1640995200")
			})

			Convey("应该能够反序列化回原始结构", func() {
				// 序列化
				jsonData, _ := json.Marshal(approvalInfo)

				// 反序列化
				var deserializedInfo entity.ApprovalInfo
				err := json.Unmarshal(jsonData, &deserializedInfo)

				So(err, ShouldBeNil)
				So(len(deserializedInfo.Auditors), ShouldEqual, 2)
				So(len(deserializedInfo.Approvers), ShouldEqual, 1)
				So(deserializedInfo.Auditors[0].UserID, ShouldEqual, "auditor1")
				So(deserializedInfo.Approvers[0].UserID, ShouldEqual, "approver1")
			})
		})
	})
}

// TestRecycleRecordWithApprovalInfo 测试回收记录的审批信息字段
func TestRecycleRecordWithApprovalInfo(t *testing.T) {
	Convey("测试回收记录的审批信息字段", t, func() {

		Convey("当创建包含审批信息的回收记录时", func() {
			// 准备审批信息
			approvalInfo := entity.ApprovalInfo{
				Auditors: []entity.ApprovalItem{
					{UserID: "auditor1", PassedDate: 1640995200},
				},
				Approvers: []entity.ApprovalItem{
					{UserID: "approver1", PassedDate: 1641081600},
				},
			}

			// 序列化审批信息
			approvalInfoJSON, err := json.Marshal(approvalInfo)
			So(err, ShouldBeNil)

			// 创建回收记录
			record := &mapper.RecycleRecord{
				ID:                 "test_recycle_001",
				DistributeRecordID: "distribute_001",
				RecycleBy:          "user_001",
				RecycleDate:        1641168000,
				RecycleReason:      "文件过期",
				OtherReason:        "其他原因说明",
				WorkflowID:         "workflow_001",                   // 流程ID
				ApprovalInfo:       datatypes.JSON(approvalInfoJSON), // 使用 datatypes.JSON
			}

			Convey("审批信息字段应该正确设置", func() {
				So(record.ApprovalInfo, ShouldNotBeEmpty)
				So(string(record.ApprovalInfo), ShouldContainSubstring, "auditor1")
				So(string(record.ApprovalInfo), ShouldContainSubstring, "approver1")
			})

			Convey("应该能够从审批信息字段反序列化", func() {
				var deserializedInfo entity.ApprovalInfo
				err := json.Unmarshal(record.ApprovalInfo, &deserializedInfo)

				So(err, ShouldBeNil)
				So(len(deserializedInfo.Auditors), ShouldEqual, 1)
				So(len(deserializedInfo.Approvers), ShouldEqual, 1)
				So(deserializedInfo.Auditors[0].UserID, ShouldEqual, "auditor1")
				So(deserializedInfo.Approvers[0].UserID, ShouldEqual, "approver1")
			})
		})
	})
}

// TestDisposalRecordWithApprovalInfo 测试处置记录的审批信息字段
func TestDisposalRecordWithApprovalInfo(t *testing.T) {
	Convey("测试处置记录的审批信息字段", t, func() {

		Convey("当创建包含审批信息的处置记录时", func() {
			// 准备审批信息
			approvalInfo := entity.ApprovalInfo{
				Auditors: []entity.ApprovalItem{
					{UserID: "auditor1", PassedDate: 1640995200},
				},
				Approvers: []entity.ApprovalItem{
					{UserID: "approver1", PassedDate: 1641081600},
				},
			}

			// 序列化审批信息
			approvalInfoJSON, err := json.Marshal(approvalInfo)
			So(err, ShouldBeNil)

			// 创建处置记录
			record := &mapper.DisposalRecord{
				ID:                 "test_disposal_001",
				DistributeRecordID: "distribute_001",
				DisposalBy:         "user_001",
				DisposalDate:       1641168000,
				Reason:             "文件销毁",
				WorkflowID:         "workflow_002",                   // 流程ID
				ApprovalInfo:       datatypes.JSON(approvalInfoJSON), // 使用 datatypes.JSON
			}

			Convey("审批信息字段应该正确设置", func() {
				So(record.ApprovalInfo, ShouldNotBeEmpty)
				So(string(record.ApprovalInfo), ShouldContainSubstring, "auditor1")
				So(string(record.ApprovalInfo), ShouldContainSubstring, "approver1")
			})

			Convey("应该能够从审批信息字段反序列化", func() {
				var deserializedInfo entity.ApprovalInfo
				err := json.Unmarshal(record.ApprovalInfo, &deserializedInfo)

				So(err, ShouldBeNil)
				So(len(deserializedInfo.Auditors), ShouldEqual, 1)
				So(len(deserializedInfo.Approvers), ShouldEqual, 1)
				So(deserializedInfo.Auditors[0].UserID, ShouldEqual, "auditor1")
				So(deserializedInfo.Approvers[0].UserID, ShouldEqual, "approver1")
			})
		})
	})
}

// TestDatatypesJSONBehavior 测试 datatypes.JSON 类型的行为
func TestDatatypesJSONBehavior(t *testing.T) {
	Convey("测试 datatypes.JSON 类型行为", t, func() {

		Convey("当使用 datatypes.JSON 类型时", func() {
			testData := map[string]interface{}{
				"auditors": []map[string]interface{}{
					{"userId": "auditor1", "passedDate": 1640995200},
				},
				"approvers": []map[string]interface{}{
					{"userId": "approver1", "passedDate": 1641081600},
				},
			}

			jsonBytes, err := json.Marshal(testData)
			So(err, ShouldBeNil)

			// 创建 datatypes.JSON 类型
			jsonField := datatypes.JSON(jsonBytes)

			Convey("应该能够正确存储和读取", func() {
				So(jsonField, ShouldNotBeEmpty)
				So(string(jsonField), ShouldContainSubstring, "auditor1")
				So(string(jsonField), ShouldContainSubstring, "approver1")
			})

			Convey("应该能够反序列化为原始数据", func() {
				var result map[string]interface{}
				err := json.Unmarshal(jsonField, &result)

				So(err, ShouldBeNil)
				So(result["auditors"], ShouldNotBeNil)
				So(result["approvers"], ShouldNotBeNil)
			})
		})
	})
}
