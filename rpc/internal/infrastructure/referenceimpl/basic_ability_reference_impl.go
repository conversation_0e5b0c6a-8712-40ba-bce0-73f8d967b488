package referenceimpl

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/utils"

	"docvault/rpc/internal/infrastructure/adapter/addons"
	"docvault/rpc/internal/infrastructure/adapter/clientx"
	"docvault/rpc/internal/infrastructure/adapter/mapper"

	"github.com/zeromicro/go-zero/core/logc"
)

type BasicAbilityReferenceImpl struct {
	quickNameTranslator addons.QuickNameTranslator
	phoenixClient       clientx.PhoenixClient
	idGenerator         addons.IDGeneratorAddons
	macrohardClient     clientx.MacrohardClient
	db                  *mapper.DocvaultDB
}

func NewBasicAbilityReferenceImpl(quickNameTranslator addons.QuickNameTranslator, phoenixClient clientx.PhoenixClient, idGenerator addons.IDGeneratorAddons, macrohardClient clientx.MacrohardClient, db *mapper.DocvaultDB) aggregate.BasicAbilityReference {
	return &BasicAbilityReferenceImpl{
		quickNameTranslator: quickNameTranslator,
		phoenixClient:       phoenixClient,
		idGenerator:         idGenerator,
		macrohardClient:     macrohardClient,
		db:                  db,
	}
}

func (b *BasicAbilityReferenceImpl) GenerateID() string {
	return b.idGenerator.GenerateIDString()
}

func (b *BasicAbilityReferenceImpl) TranslateUserNickname(ctx context.Context, userId string) string {
	return b.quickNameTranslator.TranslateUserNickname(ctx, userId)
}

func (b *BasicAbilityReferenceImpl) TranslateOrganizationName(ctx context.Context, organizationId string) string {
	return b.quickNameTranslator.TranslateOrganizationName(ctx, organizationId)
}

func (b *BasicAbilityReferenceImpl) GetFileInfo(ctx context.Context, fileId string) (entity.File, error) {
	info, err := b.phoenixClient.GetFileInfo(ctx, fileId)
	if err != nil {
		return entity.File{}, err
	}
	return entity.File{
		ID:         info.ID,
		UUID:       info.UUID,
		OriginName: info.OriginName,
		Name:       info.Name,
		FileType:   info.FileType,
		OpenStatus: info.OpenStatus,
		Size:       info.Size,
		Path:       info.Path,
		Hash:       info.Hash,
		Url:        info.Url,
	}, nil
}

func (b *BasicAbilityReferenceImpl) GetWorkflowInfo(ctx context.Context, workflowId string) (workflowInfo value.WorkflowInfo, err error) {
	w, err := b.phoenixClient.GetWorkflow(ctx, workflowId)
	if err != nil {
		return value.WorkflowInfo{}, err
	}

	if err := utils.StructDeepCopy(ctx, &workflowInfo, w); err != nil {
		return value.WorkflowInfo{}, err
	}

	return workflowInfo, nil
}

func (b *BasicAbilityReferenceImpl) StartTX(ctx context.Context) mapper.Transaction {
	tx, err := mapper.NewTransaction(b.db.GetDB()).Begin(ctx)
	if err != nil {
		logc.Error(ctx, "BasicAbilityReferenceImpl.StartTX", "Begin", err)
		return nil
	}
	return tx
}
