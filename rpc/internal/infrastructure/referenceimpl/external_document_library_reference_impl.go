package referenceimpl

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/utils"
	"strings"
)

type ExternalDocumentLibraryReferenceImpl struct {
	db *mapper.DocvaultDB
}

func NewExternalDocumentLibraryReferenceImpl(docvaultDB *mapper.DocvaultDB) aggregate.ExternalDocumentLibraryReference {
	return &ExternalDocumentLibraryReferenceImpl{
		db: docvaultDB,
	}
}

func (e *ExternalDocumentLibraryReferenceImpl) GetExternalDocByOrgIDAndName(ctx context.Context, orgID string, name string) (*entity.ExternalDocument, error) {
	doc, err := mapper.NewExternalDocumentLibraryClient(e.db).GetExternalDocByOrgIDAndName(ctx, orgID, name)
	if err != nil {
		return nil, err
	}
	if doc == nil {
		return nil, nil
	}
	var entityDoc entity.ExternalDocument
	if err := utils.StructCopy(ctx, &entityDoc, doc); err != nil {
		return nil, err
	}
	e.setExternalDocumentAuthentication(doc, &entityDoc)

	return &entityDoc, nil
}

func (e *ExternalDocumentLibraryReferenceImpl) BatchGetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds []string) ([]value.DictNodeIdAndNumberPostfix, error) {
	client := mapper.NewExternalDocumentLibraryClient(e.db)
	maxNumberPostfixInfos, err := client.BatchGetMaxNumberPostfix(ctx, orgID, TypeDictionaryNodeIds)
	if err != nil {
		return nil, err
	}

	var result []value.DictNodeIdAndNumberPostfix
	for _, v := range maxNumberPostfixInfos {
		result = append(result, value.DictNodeIdAndNumberPostfix{
			DictNodeId:    v.DictNodeId,
			NumberPostfix: v.NumberPostfix,
		})
	}
	return result, nil
}

func (e *ExternalDocumentLibraryReferenceImpl) Create(ctx context.Context, externalDocuments []entity.ExternalDocument) error {
	client := mapper.NewExternalDocumentLibraryClient(e.db)

	var mappers []mapper.ExternalDocumentLibrary
	for _, v := range externalDocuments {
		var externalDocumentMapper mapper.ExternalDocumentLibrary
		if err := utils.StructCopy(ctx, &externalDocumentMapper, v); err != nil {
			return err
		}
		externalDocumentMapper.Authentications = strings.Join(v.Authentications, ",")
		externalDocumentMapper.AuthenticationDictionaryNodeIds = strings.Join(v.AuthenticationDictionaryNodeIds, ",")
		externalDocumentMapper.TenantID = utils.GetContextTenantID(ctx)
		mappers = append(mappers, externalDocumentMapper)
	}

	return client.CreateInBatches(ctx, mappers)
}

func (e *ExternalDocumentLibraryReferenceImpl) GetExternalDocList(ctx context.Context, req *value.ExternalDocumentPage) (externalDocumentGroupPageResp *value.ExternalDocumentPageInfo, err error) {
	client := mapper.NewExternalDocumentLibraryClient(e.db)

	var mapperReq mapper.ExternalDocumentPage
	if err := utils.StructCopy(ctx, &mapperReq, req); err != nil {
		return nil, err
	}

	list, total, err := client.GetExternalDocList(ctx, mapperReq)
	if err != nil {
		return nil, err
	}
	externalDocumentGroupPageResp = &value.ExternalDocumentPageInfo{
		Total: total,
		Data:  make([]value.ExternalDocumentList, len(list)),
	}

	for i, item := range list {
		var doc value.ExternalDocumentList
		if err := utils.StructCopy(ctx, &doc, item); err != nil {
			return nil, err
		}
		if len(item.Authentications) > 0 {
			doc.Authentications = strings.Split(item.Authentications, ",")
		}
		if len(item.AuthenticationDictionaryNodeIds) > 0 {
			doc.AuthenticationDictionaryNodeIds = strings.Split(item.AuthenticationDictionaryNodeIds, ",")
		}
		externalDocumentGroupPageResp.Data[i] = doc
	}
	return externalDocumentGroupPageResp, nil
}

func (e *ExternalDocumentLibraryReferenceImpl) ChangeExternalDocument(ctx context.Context, externalDocument entity.ExternalDocument) error {
	client := mapper.NewExternalDocumentLibraryClient(e.db)

	var mapperDoc mapper.ExternalDocumentLibrary
	if err := utils.StructCopy(ctx, &mapperDoc, externalDocument); err != nil {
		return err
	}
	mapperDoc.Authentications = strings.Join(externalDocument.Authentications, ",")
	mapperDoc.AuthenticationDictionaryNodeIds = strings.Join(externalDocument.AuthenticationDictionaryNodeIds, ",")

	return client.Update(ctx, mapperDoc)
}

func (e *ExternalDocumentLibraryReferenceImpl) GetExternalDocumentById(ctx context.Context, id string) (*entity.ExternalDocument, error) {
	client := mapper.NewExternalDocumentLibraryClient(e.db)

	mapperDoc, err := client.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	entityDoc, err := e.convertMapperToEntity(ctx, mapperDoc)
	if err != nil {
		return nil, err
	}

	return entityDoc, nil
}

func (e *ExternalDocumentLibraryReferenceImpl) GetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds string) (int, error) {
	client := mapper.NewExternalDocumentLibraryClient(e.db)
	return client.GetMaxNumberPostfix(ctx, orgID, TypeDictionaryNodeIds)
}

func (e *ExternalDocumentLibraryReferenceImpl) GetExternalDocNamesByOrgID(ctx context.Context, orgID string) ([]string, error) {
	return mapper.NewExternalDocumentLibraryClient(e.db).GetExternalDocNamesByOrgID(ctx, orgID)
}

func (e *ExternalDocumentLibraryReferenceImpl) GetExternalDocsByIds(ctx context.Context, ids []string) ([]entity.ExternalDocument, error) {
	client := mapper.NewExternalDocumentLibraryClient(e.db)
	mapperDocs, err := client.GetByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	var result []entity.ExternalDocument
	for _, v := range mapperDocs {
		entityDoc, err := e.convertMapperToEntity(ctx, v)
		if err != nil {
			return nil, err
		}
		result = append(result, *entityDoc)
	}

	return result, nil
}

func (e *ExternalDocumentLibraryReferenceImpl) GetExternalDocsByOrgIDAndNames(ctx context.Context, orgID string, names []string) ([]entity.ExternalDocument, error) {
	client := mapper.NewExternalDocumentLibraryClient(e.db)
	mapperDocs, err := client.GetByOrgIDAndNames(ctx, orgID, names)
	if err != nil {
		return nil, err
	}

	var result []entity.ExternalDocument
	for _, v := range mapperDocs {
		entityDoc, err := e.convertMapperToEntity(ctx, v)
		if err != nil {
			return nil, err
		}
		result = append(result, *entityDoc)
	}

	return result, nil
}

// 转换mapper.ExternalDocumentLibrary为entity.ExternalDocument
func (e *ExternalDocumentLibraryReferenceImpl) convertMapperToEntity(ctx context.Context, mapperDoc mapper.ExternalDocumentLibrary) (*entity.ExternalDocument, error) {
	var entityDoc entity.ExternalDocument
	if err := utils.StructCopy(ctx, &entityDoc, mapperDoc); err != nil {
		return nil, err
	}
	e.setExternalDocumentAuthentication(&mapperDoc, &entityDoc)
	return &entityDoc, nil
}

func (*ExternalDocumentLibraryReferenceImpl) setExternalDocumentAuthentication(doc *mapper.ExternalDocumentLibrary, entityDoc *entity.ExternalDocument) {
	if len(doc.Authentications) > 0 {
		entityDoc.Authentications = strings.Split(doc.Authentications, ",")
	} else {
		entityDoc.Authentications = []string{}
	}
	if len(doc.AuthenticationDictionaryNodeIds) > 0 {
		entityDoc.AuthenticationDictionaryNodeIds = strings.Split(doc.AuthenticationDictionaryNodeIds, ",")
	} else {
		entityDoc.AuthenticationDictionaryNodeIds = []string{}
	}
}
