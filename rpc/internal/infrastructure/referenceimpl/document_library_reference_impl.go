package referenceimpl

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/addons"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"encoding/json"
	"fmt"

	"gorm.io/datatypes"
)

type DocumentLibraryReferenceImpl struct {
	db          *mapper.DocvaultDB
	idGenerator addons.IDGeneratorAddons
}

func NewDocumentLibraryReferenceImpl(
	docvaultDB *mapper.DocvaultDB,
	idGenerator addons.IDGeneratorAddons,
) aggregate.DocumentLibraryReference {
	return &DocumentLibraryReferenceImpl{
		db:          docvaultDB,
		idGenerator: idGenerator,
	}
}

func (d *DocumentLibraryReferenceImpl) CreateDistributeRecord(ctx context.Context, distributeRecordInfo *entity.DistributeRecordInfo) error {
	// 创建 Clients
	recordClient := mapper.NewDistributeRecordClient(d.db)
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 开始事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return err
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 序列化 ApprovalInfo
	approvalInfoBytes, err := json.Marshal(distributeRecordInfo.ApprovalInfo)
	if err != nil {
		return err
	}

	// 创建 DistributeRecord
	record := &mapper.DistributeRecord{
		ID:                 distributeRecordInfo.ID,
		WorkflowID:         distributeRecordInfo.WorkflowID,
		Applicant:          distributeRecordInfo.Applicant,
		ApplyDate:          distributeRecordInfo.ApplyDate,
		DistributeType:     distributeRecordInfo.DistributeType,
		FileType:           distributeRecordInfo.FileType,
		FileCategory:       distributeRecordInfo.FileCategory,
		TypeDictNodeId:     distributeRecordInfo.TypeDictNodeId,
		Reason:             distributeRecordInfo.Reason,
		OtherReason:        distributeRecordInfo.OtherReason,
		WishDistributeDate: distributeRecordInfo.WishDistributeDate,
		DistributeCount:    distributeRecordInfo.DistributeCount,
		SignForCount:       distributeRecordInfo.SignForCount,
		DisposeCount:       distributeRecordInfo.DisposeCount,
		Status:             distributeRecordInfo.Status,
		ApprovalInfo:       datatypes.JSON(approvalInfoBytes),
	}

	// 插入 DistributeRecord
	if err = recordClient.CreateWithTx(ctx, tx, record); err != nil {
		return err
	}

	// 创建 DistributeRecordFile 和 DistributeRecordPermission
	for _, fileList := range distributeRecordInfo.DistributeList {
		fileRecord := &mapper.DistributeRecordFile{
			ID:       d.idGenerator.GenerateIDString(),
			RecordID: record.ID,
			FileID:   fileList.FileID,
			FileName: fileList.FileName,
			Number:   fileList.Number,
			Version:  fileList.Version,
		}

		// 插入 DistributeRecordFile
		if err = fileClient.CreateWithTx(ctx, tx, fileRecord); err != nil {
			return err
		}

		// 创建 DistributeRecordPermission
		for _, permission := range fileList.Permissions {
			for _, receivedBy := range permission.ReceivedBy {
				permissionRecord := &mapper.DistributeRecordPermission{
					ID:             d.idGenerator.GenerateIDString(),
					FileRecordID:   fileRecord.ID,
					FileForm:       permission.FileForm,
					FilePermission: permission.FilePermission,
					Recipient:      permission.Recipient,
					UserID:         receivedBy.UserId,
					UserName:       receivedBy.UserName,
					SignForStatus:  receivedBy.SignForStatus,
					DisposeStatus:  receivedBy.DisposeStatus,
				}

				// 插入 DistributeRecordPermission
				if err = permissionClient.CreateWithTx(ctx, tx, permissionRecord); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// GetUserIdsByFileIdAndFileFromAndFilePermission 根据文件id、文件类型（内外部文件）、文件权限类型（查询、一次下载），查询用户id
// 参数:
//   - ctx: 上下文
//   - fileId: 文件ID
//   - fileFrom: 文件类型，1内部文件 | 2外部文件
//   - filePermission: 文件权限，1查阅 | 2查阅/下载 | 3一次下载
//
// 返回:
//   - *value.DocPermissionUsers: 包含三类用户的权限信息
//   - error: 错误信息，成功时返回nil
func (d *DocumentLibraryReferenceImpl) GetUserIdsByFileIdAndFileFromAndFilePermission(ctx context.Context, fileId string, fileFrom, filePermission int32) (*value.DocPermissionUsers, error) {
	// 初始化结果结构
	result := &value.DocPermissionUsers{
		WaitForApprovalUsers: []string{},
		NotRecycledUsers:     []string{},
		RecycleFlowUsers:     []string{},
	}

	// 通过mapper层查询详细的用户权限状态信息
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)
	userPermissions, err := permissionClient.GetDetailedUserPermissions(ctx, fileId, fileFrom, filePermission)
	if err != nil {
		return nil, fmt.Errorf("查询用户权限详细信息失败: %v", err)
	}

	// 根据业务规则分类用户
	// 查询条件查询发放列表status为 2待审批 的用户id保存在 WaitForApprovalUsers
	// 查询条件查询发放列表status为 3已审批 的，查询用户DisposeStatus为 1未回收 的，保存在NotRecycledUsers
	// 查询条件查询发放列表status为 3已审批 的，查询用户DisposeStatus为 2回收审批中 的，保存在RecycleFlowUsers

	// 使用map去重，避免重复用户
	waitForApprovalMap := make(map[string]bool)
	notRecycledMap := make(map[string]bool)
	recycleFlowMap := make(map[string]bool)

	for _, perm := range userPermissions {
		switch perm.DistributeStatus {
		case 2: // 待审批
			waitForApprovalMap[perm.UserID] = true
		case 3: // 已审批 - 根据处置状态进一步分类
			switch perm.DisposeStatus {
			case 1: // 未回收
				notRecycledMap[perm.UserID] = true
			case 2: // 回收审批中
				recycleFlowMap[perm.UserID] = true
			default:
				// 其他状态默认归类为未回收
				notRecycledMap[perm.UserID] = true
			}
		}
	}

	// 查询回收记录中的用户权限信息
	recycleUsers, err := d.getRecycleDocPermissionUsers(ctx, fileId, fileFrom, filePermission)
	if err != nil {
		return nil, fmt.Errorf("查询回收记录用户权限失败: %v", err)
	}

	// 处理回收记录用户
	for _, user := range recycleUsers {
		switch user.RecycleStatus {
		case 2: // 回收审批中
			recycleFlowMap[user.UserID] = true
		}
	}

	// 转换map为slice
	for userID := range waitForApprovalMap {
		result.WaitForApprovalUsers = append(result.WaitForApprovalUsers, userID)
	}
	for userID := range notRecycledMap {
		result.NotRecycledUsers = append(result.NotRecycledUsers, userID)
	}
	for userID := range recycleFlowMap {
		result.RecycleFlowUsers = append(result.RecycleFlowUsers, userID)
	}

	return result, nil
}

// 根据发放记录ID修改状态
func (d *DocumentLibraryReferenceImpl) AlterDistributeStatus(ctx context.Context, id string, status int32) error {
	// 根据发放记录ID修改状态
	recordClient := mapper.NewDistributeRecordClient(d.db)
	return recordClient.UpdateStatusByWorkflowId(ctx, id, status)
}

// 根据workflowId修改status
func (d *DocumentLibraryReferenceImpl) AlterDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error {
	// 根据workflowId修改status
	recordClient := mapper.NewDistributeRecordClient(d.db)
	return recordClient.UpdateStatusByWorkflowId(ctx, workflowId, status)
}

// 保存回收信息
func (d *DocumentLibraryReferenceImpl) SaveRecycleInfo(ctx context.Context, recycleApprovalRequest *entity.RecycleApprovalInfo) error {
	// 创建 Clients
	recordClient := mapper.NewRecycleRecordClient(d.db)
	fileClient := mapper.NewRecycleRecordFileClient(d.db)
	permissionClient := mapper.NewRecycleRecordPermissionClient(d.db)

	// 开始事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return err
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 序列化审批信息为 datatypes.JSON
	approvalInfoJSON, err := json.Marshal(recycleApprovalRequest.ApprovalInfo)
	if err != nil {
		return err
	}

	// 创建主记录
	record := &mapper.RecycleRecord{
		ID:                 d.idGenerator.GenerateIDString(),
		DistributeRecordID: recycleApprovalRequest.DistributeID, // 关联的发放记录ID
		RecycleBy:          recycleApprovalRequest.RecycleBy,
		RecycleDate:        recycleApprovalRequest.RecycleDate,
		RecycleReason:      recycleApprovalRequest.RecycleReason,
		OtherReason:        recycleApprovalRequest.OtherReason,
		WorkflowID:         recycleApprovalRequest.WorkflowID, // 流程ID
		ApprovalInfo:       approvalInfoJSON,                  // 直接赋值 []byte 给 datatypes.JSON
	}

	// 插入 RecycleRecord
	if err = recordClient.CreateWithTx(ctx, tx, record); err != nil {
		return err
	}

	// 创建 RecycleRecordFile 和 RecycleRecordPermission
	for _, recycleItem := range recycleApprovalRequest.RecycleList {
		// 遍历每个文件的权限
		for _, permission := range recycleItem.Permissions {
			fileRecord := &mapper.RecycleRecordFile{
				ID:       d.idGenerator.GenerateIDString(),
				RecordID: record.ID,
				FileID:   recycleItem.FileID,
				FileForm: int32(permission.FileForm),
			}

			// 插入 RecycleRecordFile
			if err = fileClient.CreateWithTx(ctx, tx, fileRecord); err != nil {
				return err
			}

			// 创建权限记录
			var permissions []*mapper.RecycleRecordPermission
			for _, userID := range permission.ReceivedBy {
				permissionRecord := &mapper.RecycleRecordPermission{
					ID:           d.idGenerator.GenerateIDString(),
					FileRecordID: fileRecord.ID,
					UserID:       userID,
				}
				permissions = append(permissions, permissionRecord)
			}

			// 批量插入 RecycleRecordPermission
			if err = permissionClient.BatchCreateWithTx(ctx, tx, permissions); err != nil {
				return err
			}
		}
	}

	return nil
}

// 保存处置信息
func (d *DocumentLibraryReferenceImpl) SaveDisposalInfo(ctx context.Context, disposalApprovalRequest *entity.DisposalApprovalInfo) error {
	// 创建 Clients
	recordClient := mapper.NewDisposalRecordClient(d.db)
	fileClient := mapper.NewDisposalRecordFileClient(d.db)
	permissionClient := mapper.NewDisposalRecordPermissionClient(d.db)

	// 开始事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return err
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 序列化审批信息为 datatypes.JSON
	approvalInfoJSON, err := json.Marshal(disposalApprovalRequest.ApprovalInfo)
	if err != nil {
		return err
	}

	// 创建主记录
	record := &mapper.DisposalRecord{
		ID:                 d.idGenerator.GenerateIDString(),
		DistributeRecordID: disposalApprovalRequest.DistributeID, // 关联的发放记录ID
		DisposalBy:         disposalApprovalRequest.DisposalBy,
		DisposalDate:       disposalApprovalRequest.DisposalDate,
		Reason:             disposalApprovalRequest.DisposalReason,
		WorkflowID:         disposalApprovalRequest.WorkflowID, // 流程ID
		ApprovalInfo:       approvalInfoJSON,                   // 直接赋值 []byte 给 datatypes.JSON
	}

	// 插入 DisposalRecord
	if err = recordClient.CreateWithTx(ctx, tx, record); err != nil {
		return err
	}

	// 创建 DisposalRecordFile 和 DisposalRecordPermission
	for _, disposalItem := range disposalApprovalRequest.DisposalList {
		// 创建文件记录
		fileRecord := &mapper.DisposalRecordFile{
			ID:       d.idGenerator.GenerateIDString(),
			RecordID: record.ID,
			FileID:   disposalItem.FileID,
			FileForm: int32(disposalItem.FileForm),
		}

		// 插入 DisposalRecordFile
		if err = fileClient.CreateWithTx(ctx, tx, fileRecord); err != nil {
			return err
		}

		// 创建权限记录
		var permissions []*mapper.DisposalRecordPermission
		for _, userID := range disposalItem.ReceivedBy {
			permissionRecord := &mapper.DisposalRecordPermission{
				ID:           d.idGenerator.GenerateIDString(),
				FileRecordID: fileRecord.ID,
				UserID:       userID,
			}
			permissions = append(permissions, permissionRecord)
		}

		// 批量插入 DisposalRecordPermission
		if len(permissions) > 0 {
			if err = permissionClient.BatchCreateWithTx(ctx, tx, permissions); err != nil {
				return err
			}
		}
	}

	return nil
}

// SaveDistributeApprovalWorkflow 保存发放审批流程信息
// 根据 workflowID 更新发放记录的状态和审批信息
// 参数:
//   - ctx: 上下文
//   - req: 发放审批请求，包含工作流ID、状态和审批信息
//
// 返回:
//   - error: 错误信息，成功时返回nil
func (d *DocumentLibraryReferenceImpl) SaveDistributeApprovalWorkflow(ctx context.Context, req *value.DistributeApprovalRequest) error {
	// 创建分发记录客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)

	// 根据 workflowID 获取现有的分发记录
	record, err := recordClient.GetByWorkflowID(ctx, req.WorkflowID)
	if err != nil {
		return err
	}

	// 检查记录是否存在
	if record == nil {
		return fmt.Errorf("未找到工作流ID为 %s 的分发记录", req.WorkflowID)
	}

	// 序列化审批信息为JSON
	approvalInfoJSON, err := json.Marshal(req.ApprovalInfo)
	if err != nil {
		return fmt.Errorf("序列化审批信息失败: %v", err)
	}

	// 更新记录的状态和审批信息
	record.Status = req.Status
	record.ApprovalInfo = datatypes.JSON(approvalInfoJSON)

	// 保存更新后的记录
	if err := recordClient.Update(ctx, record); err != nil {
		return fmt.Errorf("更新分发记录失败: %v", err)
	}

	return nil
}

func (d *DocumentLibraryReferenceImpl) GetDistributeInfos(ctx context.Context, req *value.GetDistributeListRequest) (int, []*entity.GetDistributeListInfo, error) {
	// 创建分发记录客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 构建查询请求参数
	getDistributesReq := &mapper.GetDistributesRequest{
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       req.FileType,
		FileCategory:   req.FileCategory,
		DistributeType: req.DistributeType,
		Status:         req.Status,
		Applicant:      req.Applicant,
		Page:           int(req.Page),
		PageSize:       int(req.PageSize),
		NoPage:         req.NoPage,
	}

	// 查询分发记录列表和总数
	records, total, err := recordClient.GetDistributes(ctx, getDistributesReq)
	if err != nil {
		return 0, nil, err
	}

	if len(records) == 0 {
		return int(total), []*entity.GetDistributeListInfo{}, nil
	}

	// 批量获取用户状态信息，避免N+1查询问题
	var recordIDs []string
	for _, record := range records {
		recordIDs = append(recordIDs, record.ID)
	}

	// 批量查询所有记录的用户状态原始数据
	userStatuses, err := permissionClient.GetUserStatusesByRecordIDs(ctx, recordIDs)
	if err != nil {
		return 0, nil, err
	}

	// 在impl层进行用户状态分类
	receivedMap, notReceivedMap, recycleMap := d.classifyUsersByStatus(userStatuses)

	// 确保每个记录ID都有对应的映射
	d.ensureRecordMappings(recordIDs, receivedMap, notReceivedMap, recycleMap)

	// 转换为返回格式
	var result []*entity.GetDistributeListInfo
	for _, record := range records {
		// 反序列化审批信息
		var approvalInfo entity.ApprovalInfo
		if len(record.ApprovalInfo) > 0 {
			if err := json.Unmarshal(record.ApprovalInfo, &approvalInfo); err != nil {
				// 如果反序列化失败，使用空的审批信息
				approvalInfo = entity.ApprovalInfo{}
			}
		}

		// 从批量查询结果中获取用户状态信息
		received := receivedMap[record.ID]
		notReceived := notReceivedMap[record.ID]
		recycle := recycleMap[record.ID]

		// 构建分发信息
		distributeInfo := &entity.GetDistributeListInfo{
			ID:                 record.ID,
			Applicant:          record.Applicant,
			ApplyDate:          record.ApplyDate,
			DistributeType:     int(record.DistributeType),
			FileType:           int(record.FileType),
			FileCategory:       record.FileCategory,
			WishDistributeDate: record.WishDistributeDate,
			Reason:             record.Reason,
			OtherReason:        record.OtherReason,
			Status:             record.Status,
			WorkflowID:         record.WorkflowID,
			ApprovalInfo:       approvalInfo,
			Received:           d.convertDistributeUsers(received),
			NotReceived:        d.convertDistributeUsers(notReceived),
			Recycle:            d.convertDistributeUsers(recycle),
		}

		result = append(result, distributeInfo)
	}

	return int(total), result, nil
}

// getRecycleDocPermissionUsers 获取回收记录中的用户权限信息
func (d *DocumentLibraryReferenceImpl) getRecycleDocPermissionUsers(ctx context.Context, fileId string, fileFrom, filePermission int32) ([]value.RecycleDocPermissionUser, error) {
	var result []value.RecycleDocPermissionUser

	// 使用mapper查询回收权限用户信息
	permissionClient := mapper.NewRecycleRecordPermissionClient(d.db)
	permissions, err := permissionClient.GetRecycleDocPermissionUsers(ctx, fileId, fileFrom, filePermission)
	if err != nil {
		return nil, err
	}

	// 转换为返回格式
	for _, perm := range permissions {
		result = append(result, value.RecycleDocPermissionUser{
			UserID:        perm.UserID,
			RecycleStatus: perm.RecycleStatus,
		})
	}

	return result, nil
}

// classifyUsersByStatus 根据签收状态和处置状态分类用户
// 参数:
//   - userStatuses: 用户状态列表
//
// 返回:
//   - receivedMap: 按记录ID分组的已签收用户映射
//   - notReceivedMap: 按记录ID分组的未签收用户映射
//   - recycleMap: 按记录ID分组的已回收用户映射
func (d *DocumentLibraryReferenceImpl) classifyUsersByStatus(userStatuses []mapper.UserStatusInfo) (
	receivedMap, notReceivedMap, recycleMap map[string][]mapper.DistributeUser) {

	// 初始化映射
	receivedMap = make(map[string][]mapper.DistributeUser)
	notReceivedMap = make(map[string][]mapper.DistributeUser)
	recycleMap = make(map[string][]mapper.DistributeUser)

	// 按记录ID和状态分组用户
	for _, status := range userStatuses {
		user := mapper.DistributeUser{
			UserID:   status.UserID,
			FileForm: int(status.FileForm),
		}

		// 根据状态分类用户
		switch {
		case status.DisposeStatus == 4: // 已回收
			recycleMap[status.RecordID] = append(recycleMap[status.RecordID], user)
		case status.SignForStatus == 4: // 已签收
			receivedMap[status.RecordID] = append(receivedMap[status.RecordID], user)
		default: // 未签收（包括待审批、待签收等状态）
			notReceivedMap[status.RecordID] = append(notReceivedMap[status.RecordID], user)
		}
	}

	return receivedMap, notReceivedMap, recycleMap
}

// ensureRecordMappings 确保每个记录ID都有对应的映射（即使为空）
// 参数:
//   - recordIDs: 记录ID列表
//   - receivedMap: 已签收用户映射
//   - notReceivedMap: 未签收用户映射
//   - recycleMap: 已回收用户映射
func (d *DocumentLibraryReferenceImpl) ensureRecordMappings(recordIDs []string,
	receivedMap, notReceivedMap, recycleMap map[string][]mapper.DistributeUser) {

	for _, recordID := range recordIDs {
		if _, exists := receivedMap[recordID]; !exists {
			receivedMap[recordID] = []mapper.DistributeUser{}
		}
		if _, exists := notReceivedMap[recordID]; !exists {
			notReceivedMap[recordID] = []mapper.DistributeUser{}
		}
		if _, exists := recycleMap[recordID]; !exists {
			recycleMap[recordID] = []mapper.DistributeUser{}
		}
	}
}

// convertDistributeUsers 转换 mapper.DistributeUser 到 entity.DistributeUser
// 参数:
//   - mapperUsers: mapper层的用户列表
//
// 返回:
//   - []entity.DistributeUser: entity层的用户列表
func (d *DocumentLibraryReferenceImpl) convertDistributeUsers(mapperUsers []mapper.DistributeUser) []entity.DistributeUser {
	var entityUsers []entity.DistributeUser
	for _, mapperUser := range mapperUsers {
		entityUser := entity.DistributeUser{
			UserID:   mapperUser.UserID,
			FileForm: mapperUser.FileForm,
		}
		entityUsers = append(entityUsers, entityUser)
	}
	return entityUsers
}

// GetDistributeInventoryByID 根据发放列表id获取发放清单信息
// 参数:
//   - ctx: 上下文
//   - id: 发放列表ID（即发放记录ID）
//
// 返回:
//   - []*entity.DistributeInventory: 发放清单信息列表
//   - error: 错误信息，成功时返回nil
func (d *DocumentLibraryReferenceImpl) GetDistributeInventoryByID(ctx context.Context, id string) ([]*entity.DistributeInventory, error) {
	// 创建mapper客户端
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 1. 根据发放记录ID获取文件列表
	files, err := fileClient.GetInventoryByRecordID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取发放文件列表失败: %v", err)
	}

	if len(files) == 0 {
		return []*entity.DistributeInventory{}, nil
	}

	// 2. 收集所有文件记录ID
	var fileRecordIDs []string
	for _, file := range files {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	// 3. 批量获取权限信息
	permissions, err := permissionClient.GetPermissionsByFileRecordIDs(ctx, fileRecordIDs)
	if err != nil {
		return nil, fmt.Errorf("获取权限信息失败: %v", err)
	}

	// 4. 构建发放清单信息
	inventories := d.buildDistributeInventories(files, permissions)

	return inventories, nil
}

// buildDistributeInventories 构建发放清单信息
// 参数:
//   - files: 文件列表
//   - permissions: 权限信息列表
//
// 返回:
//   - []*entity.DistributeInventory: 构建好的发放清单信息列表
func (d *DocumentLibraryReferenceImpl) buildDistributeInventories(files []*mapper.DistributeRecordFile, permissions []*mapper.DistributeRecordPermission) []*entity.DistributeInventory {
	// 按文件记录ID分组权限信息
	permissionMap := d.groupPermissionsByFileRecordID(permissions)

	// 构建发放清单信息
	var inventories []*entity.DistributeInventory
	for _, file := range files {
		inventory := d.buildSingleInventory(file, permissionMap[file.ID])
		inventories = append(inventories, inventory)
	}

	return inventories
}

// groupPermissionsByFileRecordID 按文件记录ID分组权限信息
// 参数:
//   - permissions: 权限信息列表
//
// 返回:
//   - map[string][]*mapper.DistributeRecordPermission: 按文件记录ID分组的权限信息映射
func (d *DocumentLibraryReferenceImpl) groupPermissionsByFileRecordID(permissions []*mapper.DistributeRecordPermission) map[string][]*mapper.DistributeRecordPermission {
	permissionMap := make(map[string][]*mapper.DistributeRecordPermission)
	for _, permission := range permissions {
		permissionMap[permission.FileRecordID] = append(permissionMap[permission.FileRecordID], permission)
	}
	return permissionMap
}

// buildSingleInventory 构建单个发放清单项
// 参数:
//   - file: 文件信息
//   - filePermissions: 该文件的权限信息列表
//
// 返回:
//   - *entity.DistributeInventory: 构建好的发放清单项
func (d *DocumentLibraryReferenceImpl) buildSingleInventory(file *mapper.DistributeRecordFile, filePermissions []*mapper.DistributeRecordPermission) *entity.DistributeInventory {
	inventory := &entity.DistributeInventory{
		ID:       file.ID,
		FileId:   file.FileID,
		FileName: file.FileName,
		Number:   file.Number,
		Version:  file.Version,
	}

	// 如果有权限信息，则构建权限列表
	if len(filePermissions) > 0 {
		inventory.Permissions = d.buildPermissionResponses(filePermissions)
	}

	return inventory
}

// buildPermissionResponses 构建权限响应列表
// 参数:
//   - filePermissions: 文件权限信息列表
//
// 返回:
//   - []entity.PermissionResp: 权限响应列表
func (d *DocumentLibraryReferenceImpl) buildPermissionResponses(filePermissions []*mapper.DistributeRecordPermission) []entity.PermissionResp {
	// 按文件形式和权限类型分组
	permissionGroupMap := d.groupPermissionsByFormAndType(filePermissions)

	// 转换为权限列表
	var permissions []entity.PermissionResp
	for _, permResp := range permissionGroupMap {
		permissions = append(permissions, *permResp)
	}

	return permissions
}

// groupPermissionsByFormAndType 按文件形式和权限类型分组权限信息
// 参数:
//   - filePermissions: 文件权限信息列表
//
// 返回:
//   - map[string]*entity.PermissionResp: 按权限组合键分组的权限响应映射
func (d *DocumentLibraryReferenceImpl) groupPermissionsByFormAndType(filePermissions []*mapper.DistributeRecordPermission) map[string]*entity.PermissionResp {
	permissionGroupMap := make(map[string]*entity.PermissionResp)

	for _, perm := range filePermissions {
		// 创建权限组合键：文件形式_权限类型
		key := fmt.Sprintf("%d_%d", perm.FileForm, perm.FilePermission)

		if permissionGroupMap[key] == nil {
			permissionGroupMap[key] = &entity.PermissionResp{
				FileForm:       perm.FileForm,
				FilePermission: perm.FilePermission,
				ReceivedBy:     []entity.Received{},
			}
		}

		// 添加接收人信息
		received := entity.Received{
			UserID:   perm.UserID,
			Nickname: perm.UserName,
			Status:   int(perm.SignForStatus), // 使用签收状态作为状态
		}
		permissionGroupMap[key].ReceivedBy = append(permissionGroupMap[key].ReceivedBy, received)
	}

	return permissionGroupMap
}

// UpdateDistributeInventoryByID 根据发放列表ID更新发放清单信息
// 业务流程：
//  1. 根据ID获取现有发放记录，验证记录存在性
//  2. 在事务中删除旧的发放清单数据（文件记录和权限记录）
//  3. 更新发放记录的基本信息
//  4. 保存新的发放清单数据（文件记录和权限记录）
//
// 参数:
//   - ctx: 上下文，用于传递请求范围的值、取消信号和截止时间
//   - req: 发放记录信息，包含要更新的所有数据
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
//
// 注意事项:
//   - 使用数据库事务确保数据一致性
//   - 删除操作会级联删除相关的权限记录
//   - 更新过程中如果发生错误会自动回滚
func (d *DocumentLibraryReferenceImpl) UpdateDistributeInventoryByID(ctx context.Context, req *entity.DistributeRecordInfo) error {
	// 创建数据访问客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 1. 验证发放记录是否存在
	if err := d.validateDistributeRecordExists(ctx, recordClient, req.ID); err != nil {
		return err
	}

	// 2. 开始数据库事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 3. 删除旧的发放清单数据
	if err = d.deleteOldInventoryData(ctx, tx, fileClient, permissionClient, req.ID); err != nil {
		return err
	}

	// 4. 更新发放记录的基本信息
	if err = d.updateDistributeRecord(ctx, tx, recordClient, req); err != nil {
		return err
	}

	// 5. 保存新的发放清单数据
	if err = d.createNewInventoryData(ctx, tx, fileClient, permissionClient, req); err != nil {
		return err
	}

	return nil
}

// validateDistributeRecordExists 验证发放记录是否存在
// 参数:
//   - ctx: 上下文
//   - recordClient: 发放记录数据访问客户端
//   - recordID: 发放记录ID
//
// 返回:
//   - error: 记录不存在或查询失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) validateDistributeRecordExists(ctx context.Context, recordClient *mapper.DistributeRecordClient, recordID string) error {
	existingRecord, err := recordClient.GetByID(ctx, recordID)
	if err != nil {
		return fmt.Errorf("获取发放记录失败: %v", err)
	}
	if existingRecord == nil {
		return fmt.Errorf("未找到ID为 %s 的发放记录", recordID)
	}
	return nil
}

// deleteOldInventoryData 删除旧的发放清单数据
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - fileClient: 文件记录数据访问客户端
//   - permissionClient: 权限记录数据访问客户端
//   - recordID: 发放记录ID
//
// 返回:
//   - error: 删除失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) deleteOldInventoryData(ctx context.Context, tx mapper.Transaction, fileClient *mapper.DistributeRecordFileClient, permissionClient *mapper.DistributeRecordPermissionClient, recordID string) error {
	// 获取所有相关的文件记录
	oldFiles, err := fileClient.GetByRecordID(ctx, recordID)
	if err != nil {
		return fmt.Errorf("获取旧文件记录失败: %v", err)
	}

	// 提取文件记录ID列表
	fileRecordIDs := make([]string, len(oldFiles))
	for i, file := range oldFiles {
		fileRecordIDs[i] = file.ID
	}

	// 删除所有相关的权限记录
	if err = permissionClient.DeleteByFileRecordIDsWithTx(ctx, tx, fileRecordIDs); err != nil {
		return fmt.Errorf("删除旧权限记录失败: %v", err)
	}

	// 删除所有文件记录
	if err = fileClient.DeleteByRecordIDWithTx(ctx, tx, recordID); err != nil {
		return fmt.Errorf("删除旧文件记录失败: %v", err)
	}

	return nil
}

// updateDistributeRecord 更新发放记录的基本信息
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - recordClient: 发放记录数据访问客户端
//   - req: 发放记录信息
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) updateDistributeRecord(ctx context.Context, tx mapper.Transaction, recordClient *mapper.DistributeRecordClient, req *entity.DistributeRecordInfo) error {
	// 序列化审批信息
	approvalInfoBytes, err := json.Marshal(req.ApprovalInfo)
	if err != nil {
		return fmt.Errorf("序列化审批信息失败: %v", err)
	}

	// 构建更新的发放记录
	updatedRecord := &mapper.DistributeRecord{
		ID:                 req.ID,
		WorkflowID:         req.WorkflowID,
		Applicant:          req.Applicant,
		ApplyDate:          req.ApplyDate,
		DistributeType:     req.DistributeType,
		FileType:           req.FileType,
		FileCategory:       req.FileCategory,
		TypeDictNodeId:     req.TypeDictNodeId,
		Reason:             req.Reason,
		OtherReason:        req.OtherReason,
		WishDistributeDate: req.WishDistributeDate,
		DistributeCount:    req.DistributeCount,
		SignForCount:       req.SignForCount,
		DisposeCount:       req.DisposeCount,
		Status:             req.Status,
		ApprovalInfo:       datatypes.JSON(approvalInfoBytes),
	}

	// 使用事务更新发放记录
	if err = recordClient.UpdateWithTx(ctx, tx, updatedRecord); err != nil {
		return fmt.Errorf("更新发放记录失败: %v", err)
	}

	return nil
}

// createNewInventoryData 创建新的发放清单数据
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - fileClient: 文件记录数据访问客户端
//   - permissionClient: 权限记录数据访问客户端
//   - req: 发放记录信息
//
// 返回:
//   - error: 创建失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) createNewInventoryData(ctx context.Context, tx mapper.Transaction, fileClient *mapper.DistributeRecordFileClient, permissionClient *mapper.DistributeRecordPermissionClient, req *entity.DistributeRecordInfo) error {
	// 遍历发放清单，创建文件记录和权限记录
	for _, fileList := range req.DistributeList {
		// 创建文件记录
		fileRecord, err := d.buildFileRecord(req.ID, &fileList)
		if err != nil {
			return fmt.Errorf("构建文件记录失败: %v", err)
		}

		// 插入文件记录
		if err = fileClient.CreateWithTx(ctx, tx, fileRecord); err != nil {
			return fmt.Errorf("创建文件记录失败: %v", err)
		}

		// 创建权限记录
		if err = d.createPermissionRecords(ctx, tx, permissionClient, fileRecord.ID, fileList.Permissions); err != nil {
			return fmt.Errorf("创建权限记录失败: %v", err)
		}
	}

	return nil
}

// buildFileRecord 构建文件记录
// 参数:
//   - recordID: 发放记录ID
//   - fileList: 文件清单信息
//
// 返回:
//   - *mapper.DistributeRecordFile: 构建好的文件记录
//   - error: 构建失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) buildFileRecord(recordID string, fileList *entity.DistributeList) (*mapper.DistributeRecordFile, error) {
	return &mapper.DistributeRecordFile{
		ID:       d.idGenerator.GenerateIDString(),
		RecordID: recordID,
		FileID:   fileList.FileID,
		FileName: fileList.FileName,
		Number:   fileList.Number,
		Version:  fileList.Version,
	}, nil
}

// createPermissionRecords 创建权限记录
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - permissionClient: 权限记录数据访问客户端
//   - fileRecordID: 文件记录ID
//   - permissions: 权限信息列表
//
// 返回:
//   - error: 创建失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) createPermissionRecords(ctx context.Context, tx mapper.Transaction, permissionClient *mapper.DistributeRecordPermissionClient, fileRecordID string, permissions []entity.Permission) error {
	for _, permission := range permissions {
		for _, receivedBy := range permission.ReceivedBy {
			permissionRecord := &mapper.DistributeRecordPermission{
				ID:             d.idGenerator.GenerateIDString(),
				FileRecordID:   fileRecordID,
				FileForm:       permission.FileForm,
				FilePermission: permission.FilePermission,
				Recipient:      permission.Recipient,
				UserID:         receivedBy.UserId,
				UserName:       receivedBy.UserName,
				SignForStatus:  receivedBy.SignForStatus,
				DisposeStatus:  receivedBy.DisposeStatus,
			}

			// 插入权限记录
			if err := permissionClient.CreateWithTx(ctx, tx, permissionRecord); err != nil {
				return err
			}
		}
	}

	return nil
}
