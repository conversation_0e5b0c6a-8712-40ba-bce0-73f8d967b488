// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package server

import (
	"context"

	"docvault/rpc/internal/logic/documentlibrary"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
)

type DocumentLibraryServer struct {
	svcCtx *svc.ServiceContext
	docvault.UnimplementedDocumentLibraryServer
}

func NewDocumentLibraryServer(svcCtx *svc.ServiceContext) *DocumentLibraryServer {
	return &DocumentLibraryServer{
		svcCtx: svcCtx,
	}
}

// 保存发放记录
func (s *DocumentLibraryServer) PreSaveDistributeRecord(ctx context.Context, in *docvault.DocumentDistributeReq) (*docvault.EmptyResp, error) {
	l := documentlibrarylogic.NewPreSaveDistributeRecordLogic(ctx, s.svcCtx)
	return l.PreSaveDistributeRecord(in)
}

// 获取文档权限用户
func (s *DocumentLibraryServer) GetDocPermissionUsers(ctx context.Context, in *docvault.GetDocPermissionUsersReq) (*docvault.GetDocPermissionUsersResp, error) {
	l := documentlibrarylogic.NewGetDocPermissionUsersLogic(ctx, s.svcCtx)
	return l.GetDocPermissionUsers(in)
}

// 更新发放状态
func (s *DocumentLibraryServer) UpdateDistributeStatusByWorkflowId(ctx context.Context, in *docvault.UpdateDistributeStatusReq) (*docvault.EmptyResp, error) {
	l := documentlibrarylogic.NewUpdateDistributeStatusByWorkflowIdLogic(ctx, s.svcCtx)
	return l.UpdateDistributeStatusByWorkflowId(in)
}

// 保存发放审批信息
func (s *DocumentLibraryServer) SaveDistributeApproval(ctx context.Context, in *docvault.DistributeApprovalReq) (*docvault.EmptyResp, error) {
	l := documentlibrarylogic.NewSaveDistributeApprovalLogic(ctx, s.svcCtx)
	return l.SaveDistributeApproval(in)
}

// 保存回收审批信息
func (s *DocumentLibraryServer) SaveRecycleApprovalInfo(ctx context.Context, in *docvault.RecycleApprovalInfo) (*docvault.EmptyResp, error) {
	l := documentlibrarylogic.NewSaveRecycleApprovalInfoLogic(ctx, s.svcCtx)
	return l.SaveRecycleApprovalInfo(in)
}

// 保存处置审批信息
func (s *DocumentLibraryServer) SaveDisposalApprovalInfo(ctx context.Context, in *docvault.DisposalApprovalInfo) (*docvault.EmptyResp, error) {
	l := documentlibrarylogic.NewSaveDisposalApprovalInfoLogic(ctx, s.svcCtx)
	return l.SaveDisposalApprovalInfo(in)
}

// 获取发放信息列表
func (s *DocumentLibraryServer) GetDistributeInfoList(ctx context.Context, in *docvault.GetDistributeListReq) (*docvault.GetDistributeListResp, error) {
	l := documentlibrarylogic.NewGetDistributeInfoListLogic(ctx, s.svcCtx)
	return l.GetDistributeInfoList(in)
}

// 根据发放列表id获取发放清单信息
func (s *DocumentLibraryServer) GetDistributeApplicationById(ctx context.Context, in *docvault.GetDistributeApplicationReq) (*docvault.GetDistributeApplicationResp, error) {
	l := documentlibrarylogic.NewGetDistributeApplicationByIdLogic(ctx, s.svcCtx)
	return l.GetDistributeApplicationById(in)
}
