// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package server

import (
	"context"

	"docvault/rpc/internal/logic/externaldocumentlibrary"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
)

type ExternalDocumentLibraryServer struct {
	svcCtx *svc.ServiceContext
	docvault.UnimplementedExternalDocumentLibraryServer
}

func NewExternalDocumentLibraryServer(svcCtx *svc.ServiceContext) *ExternalDocumentLibraryServer {
	return &ExternalDocumentLibraryServer{
		svcCtx: svcCtx,
	}
}

// 批量创建文档
func (s *ExternalDocumentLibraryServer) Create(ctx context.Context, in *docvault.ExternalDocumentCreateReq) (*docvault.ExternalDocumentCreateResp, error) {
	l := externaldocumentlibrarylogic.NewCreateLogic(ctx, s.svcCtx)
	return l.Create(in)
}

// 获取文档
func (s *ExternalDocumentLibraryServer) Get(ctx context.Context, in *docvault.ExternalDocumentGetReq) (*docvault.ExternalDocumentGetResp, error) {
	l := externaldocumentlibrarylogic.NewGetLogic(ctx, s.svcCtx)
	return l.Get(in)
}

// 分页查询文档
func (s *ExternalDocumentLibraryServer) Page(ctx context.Context, in *docvault.ExternalDocumentPageReq) (*docvault.ExternalDocumentPageResp, error) {
	l := externaldocumentlibrarylogic.NewPageLogic(ctx, s.svcCtx)
	return l.Page(in)
}

// 修订文档
func (s *ExternalDocumentLibraryServer) Change(ctx context.Context, in *docvault.ExternalDocumentChangeReq) (*docvault.EmptyResp, error) {
	l := externaldocumentlibrarylogic.NewChangeLogic(ctx, s.svcCtx)
	return l.Change(in)
}

// 导入集团文档到公司验重
func (s *ExternalDocumentLibraryServer) ImportCompanyPlagiarismCheck(ctx context.Context, in *docvault.PlagiarismCheckReq) (*docvault.EmptyResp, error) {
	l := externaldocumentlibrarylogic.NewImportCompanyPlagiarismCheckLogic(ctx, s.svcCtx)
	return l.ImportCompanyPlagiarismCheck(in)
}

// 导入集团文档到公司
func (s *ExternalDocumentLibraryServer) ImportGroupDocsToCompany(ctx context.Context, in *docvault.ImportGroupDocsToCompanyReq) (*docvault.ImportGroupDocsToCompanyResp, error) {
	l := externaldocumentlibrarylogic.NewImportGroupDocsToCompanyLogic(ctx, s.svcCtx)
	return l.ImportGroupDocsToCompany(in)
}
