// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package server

import (
	"context"

	"docvault/rpc/internal/logic/book"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
)

type BookServer struct {
	svcCtx *svc.ServiceContext
	docvault.UnimplementedBookServer
}

func NewBookServer(svcCtx *svc.ServiceContext) *BookServer {
	return &BookServer{
		svcCtx: svcCtx,
	}
}

// 新增书籍信息
func (s *BookServer) CreateBook(ctx context.Context, in *docvault.CreateBookReq) (*docvault.CreateBookResp, error) {
	l := booklogic.NewCreateBookLogic(ctx, s.svcCtx)
	return l.CreateBook(in)
}

// 查询书籍列表
func (s *BookServer) GetBookList(ctx context.Context, in *docvault.GetBookListReq) (*docvault.GetBookListResp, error) {
	l := booklogic.NewGetBookListLogic(ctx, s.svcCtx)
	return l.GetBookList(in)
}

// 修改书籍信息
func (s *BookServer) UpdateBook(ctx context.Context, in *docvault.BookInfo) (*docvault.UpdateBookResp, error) {
	l := booklogic.NewUpdateBookLogic(ctx, s.svcCtx)
	return l.UpdateBook(in)
}

// 删除书籍信息
func (s *BookServer) DeleteBook(ctx context.Context, in *docvault.DeleteBookReq) (*docvault.DeleteBookResp, error) {
	l := booklogic.NewDeleteBookLogic(ctx, s.svcCtx)
	return l.DeleteBook(in)
}

// 批量导入书籍信息
func (s *BookServer) ImportBook(ctx context.Context, in *docvault.ImportBookReq) (*docvault.ImportBookResp, error) {
	l := booklogic.NewImportBookLogic(ctx, s.svcCtx)
	return l.ImportBook(in)
}
