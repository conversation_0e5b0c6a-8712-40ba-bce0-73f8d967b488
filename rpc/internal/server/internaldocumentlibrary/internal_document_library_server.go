// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package server

import (
	"context"

	"docvault/rpc/internal/logic/internaldocumentlibrary"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
)

type InternalDocumentLibraryServer struct {
	svcCtx *svc.ServiceContext
	docvault.UnimplementedInternalDocumentLibraryServer
}

func NewInternalDocumentLibraryServer(svcCtx *svc.ServiceContext) *InternalDocumentLibraryServer {
	return &InternalDocumentLibraryServer{
		svcCtx: svcCtx,
	}
}

// 创建文档
func (s *InternalDocumentLibraryServer) Create(ctx context.Context, in *docvault.InternalDocumentCreateReq) (*docvault.InternalDocumentCreateResp, error) {
	l := internaldocumentlibrarylogic.NewCreateLogic(ctx, s.svcCtx)
	return l.Create(in)
}

// 获取文档
func (s *InternalDocumentLibraryServer) Get(ctx context.Context, in *docvault.InternalDocumentGetReq) (*docvault.InternalDocumentGetResp, error) {
	l := internaldocumentlibrarylogic.NewGetLogic(ctx, s.svcCtx)
	return l.Get(in)
}

// 分页查询文档
func (s *InternalDocumentLibraryServer) Page(ctx context.Context, in *docvault.InternalDocumentPageReq) (*docvault.InternalDocumentPageResp, error) {
	l := internaldocumentlibrarylogic.NewPageLogic(ctx, s.svcCtx)
	return l.Page(in)
}

// 修订文档
func (s *InternalDocumentLibraryServer) Change(ctx context.Context, in *docvault.InternalDocumentChangeReq) (*docvault.EmptyResp, error) {
	l := internaldocumentlibrarylogic.NewChangeLogic(ctx, s.svcCtx)
	return l.Change(in)
}

// 批量创建文档
func (s *InternalDocumentLibraryServer) BatchCreate(ctx context.Context, in *docvault.InternalDocumentBatchCreateReq) (*docvault.InternalDocumentBatchCreateResp, error) {
	l := internaldocumentlibrarylogic.NewBatchCreateLogic(ctx, s.svcCtx)
	return l.BatchCreate(in)
}
