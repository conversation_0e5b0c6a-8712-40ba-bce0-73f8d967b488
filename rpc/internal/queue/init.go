package queue

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/kqs"
	"docvault/rpc/internal/svc"
	"docvault/utils"

	"github.com/segmentio/kafka-go"
)

// 注册消费者
func registerConsumers(svcCtx *svc.ServiceContext) []Consumer {
	return []Consumer{
		// "signature_approval": NewSignatureApprovalConsumer(factory),
	}
}

func InitQueue(ctx context.Context, svcCtx *svc.ServiceContext) {
	c := svcCtx.ConfigManager.GetConfig()
	if len(c.Kafka.Consumers) == 0 {
		return
	}

	consumerHandlerMap := utils.ExtractSliceFieldToMap(registerConsumers(svcCtx), func(consumer Consumer) (string, Consumer) {
		return consumer.Name(), consumer
	})

	for _, consumer := range c.Kafka.Consumers {
		// 匹配消费者
		consumerHandle, ok := consumerHandlerMap[consumer.Key]
		if !ok {
			continue
		}
		// 启动消费者
		go kqs.NewKafkaConsumer(kafka.ReaderConfig{
			Brokers:     c.Kafka.Brokers,
			Topic:       consumer.Topic,
			GroupID:     consumer.GroupID,
			MinBytes:    c.Kafka.MinBytes,
			MaxBytes:    c.Kafka.MaxBytes,
			StartOffset: kafka.FirstOffset,
		}, consumerHandle).Start(ctx)
	}
}
