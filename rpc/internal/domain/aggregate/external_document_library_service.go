package aggregate

import (
	"context"
)

// ExternalDocumentLibraryService 外部文档库service接口
// 该接口用于定义外部文档库特有的方法
type ExternalDocumentLibraryService interface {
	// BatchCreate 批量新增外部库文件信息
	BatchCreate(ctx context.Context, externalDocument []ExternalDocumentCreateRequest) (externalDocumentInfos []ExternalDocumentInfo, err error)

	// GetList 获取外部库文件列表信息
	GetList(ctx context.Context, req *ExternalDocumentPageReq) (externalDocumentPageResp *ExternalDocumentPageResp, err error)

	// Change 修改外部库文件信息
	Change(ctx context.Context, externalDocument ExternalDocumentChangeRequest) (err error)

	// ImportGroupDocsToCompany 批量导入集团文档到公司
	// 纳入功能的实现，接收集团文档id，查询到数据后保存到公司下
	ImportGroupDocsToCompany(ctx context.Context, req *ImportGroupDocsToCompanyRequest) (err error)

	// ImportCompanyPlagiarismCheck 集团文档导入公司前验重
	// 在进行批量导入集团文档到公司前，进行文件验重
	ImportCompanyPlagiarismCheck(ctx context.Context, req *ImportCompanyPlagiarismCheckRequest) (err error)
}

type ImportCompanyPlagiarismCheckRequest struct {
	Ids []string // 文档id
}

type ImportGroupDocsToCompanyRequest struct {
	OrgCode string                         // 公司编码
	Data    []ImportGroupDocsToCompanyInfo // 文档信息
}

type ImportGroupDocsToCompanyInfo struct {
	ID              string // 文档id
	OriginalNumber  string // 原文件编号
	OriginalVersion string // 原文件版本
}

type ExternalDocumentChangeRequest struct {
	ID                            string                        // id
	ExternalDocumentCreateRequest ExternalDocumentCreateRequest // 文档信息
}

type ExternalDocumentCreateRequest struct {
	NumberPrefix                    string       // 编号前缀
	Name                            string       // 文件名称
	FileID                          string       // 文件id
	DocType                         string       // 类别
	Domain                          string       // 领域
	Authentications                 []string     // 认证方式
	TypeDictionaryNodeId            string       // 类别ID
	DomainDictionaryNodeId          string       // 领域类型id
	AuthenticationDictionaryNodeIds []string     // 认证类型id
	ApprovalInfo                    ApprovalInfo // 审批信息
	PublishDate                     int64        // 发布日期
	EffectiveDate                   int64        // 实施日期
	OriginalDocNumber               string       // 原文件号
	PublishDocNumber                string       // 发文号
	PublishDepartment               string       // 发文部门
	OrgType                         int          // 类型（1集团，2公司）
	OrgID                           string       // 组织id
	OriginalNumber                  string       // 原文件编号
	OriginalVersion                 string       // 原文件版本
}

type ExternalDocumentInfo struct {
	ID                              string   // id
	TypeDictionaryNodeId            string   // 类别ID
	DomainDictionaryNodeId          string   //领域类型id
	AuthenticationDictionaryNodeIds []string //认证类型id
}

type ExternalDocumentPageReq struct {
	Page                           int      // 页码
	PageSize                       int      // 每页数量
	NoPage                         bool     // 是否不分页
	Number                         string   // 编号
	Name                           string   // 文件名称
	OriginalNumber                 string   // 原文件编号
	OriginalDocNumber              string   // 原文件号
	PublishDocNumber               string   // 发文号
	PublishDepartment              string   // 发文部门
	TypeDictionaryNodeIds          []string // 类别ID
	DomainDictionaryNodeId         string   // 领域类型id
	AuthenticationDictionaryNodeId string   // 认证类型id
	BeAttachedFile                 string   // 是否有附件
	Status                         int      // 状态
	OrgType                        int      // 类型（1集团，2公司）
	OrgID                          string   // 组织id
}

type ExternalDocumentPageResp struct {
	Total int64                      // 总数
	Data  []ExternalDocumentPageList // 数据
}

type ExternalDocumentPageList struct {
	ID                              string   // id
	Number                          string   // 编号
	Version                         string   // 版本
	OriginalNumber                  string   // 原文件编号
	OriginalVersion                 string   // 原文件版本
	Name                            string   // 文件名称
	DocType                         string   // 类别
	Domain                          string   // 领域
	OriginalDocNumber               string   // 原文件号
	PublishDocNumber                string   // 发文号
	PublishDepartment               string   // 发文部门
	PublishDate                     int64    // 发布日期
	EffectiveDate                   int64    // 实施日期
	Authentications                 []string // 认证方式
	Status                          int      // 状态
	TypeDictionaryNodeId            string   // 类别ID
	DomainDictionaryNodeId          string   // 领域类型id
	AuthenticationDictionaryNodeIds []string // 认证类型id
	FileID                          string   // 文件id
}
