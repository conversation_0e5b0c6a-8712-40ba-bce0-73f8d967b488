package aggregate

import (
	"context"
)

// DocumentLibraryService 文档库service接口
// 该接口用于定义文档库公用方法，外部库和内部库都可以使用
type DocumentLibraryService interface {
	// SaveDistributeRecord 保存发放信息，在定义好发放审批信息后需要保存时调用
	// 暂存 -> 待审批 -> 审批通过
	SaveDistributeRecord(ctx context.Context, req *DistributeRecordInfoRequest) error

	// GetDocPermissionUsers 根据文档id、文档类型和文档权限获取文档权限用户
	// 文档类型（1内部文档 | 2外部文档），文档权限（1查阅 | 2查阅/下载 | 3一次下载）
	GetDocPermissionUsers(ctx context.Context, req *GetDocPermissionUsersRequest) (*DocPermissionUsers, error)

	// UpdateDistributeStatus 根据列表id更新发放状态，需要更新发放审批信息状态时调用
	// 比如status: 1待提交 -> 2待审批
	UpdateDistributeStatus(ctx context.Context, id string, status int32) error

	// UpdateDistributeStatusByWorkflowId 根据流程id更新发放状态
	// 比如通过审批信息后 status: 2待审批 -> 3已审批
	UpdateDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error

	// SaveRecycleApprovalInfo 保存回收审批信息，回收审批通过时调用
	SaveRecycleApprovalInfo(ctx context.Context, req *RecycleApprovalRequest) error

	// SaveDisposalApprovalInfo 保存处置审批信息，处置审批通过时调用
	SaveDisposalApprovalInfo(ctx context.Context, req *DisposalApprovalRequest) error

	// GetDistributeListInfos 获取发放审批信息列表
	GetDistributeListInfos(ctx context.Context, req *GetDistributeListRequest) (*GetDistributeListResp, error)

	// SaveDistributeApproval 保存发放审批信息
	SaveDistributeApproval(ctx context.Context, req *DistributeApprovalRequest) error

	// GetDistributeInventories 根据发放列表id获取发放清单信息
	GetDistributeInventories(ctx context.Context, id string) ([]DistributeInventory, error)

	// UpdateDistributeInventory 根据发放列表id更新发放清单信息
	UpdateDistributeInventory(ctx context.Context, req *DistributeRecordInfoRequest) error
}

type DistributeInventory struct {
	ID          string           `json:"id"`          // 发放清单id
	FileId      string           `json:"fileId"`      // 文档id
	FileName    string           `json:"fileName"`    // 文档名称
	Number      string           `json:"number"`      // 文档编号
	Version     string           `json:"version"`     // 文档版本
	Permissions []PermissionResp `json:"permissions"` // 权限列表
}

type PermissionResp struct {
	FileForm       int32      `json:"fileForm"`       // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32      `json:"filePermission"` // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
	ReceivedBy     []Received `json:"receivedBy"`     // 接收人
}

type Received struct {
	UserID   string `json:"userId"`   // 用户id
	Nickname string `json:"nickname"` // 用户昵称
	Status   int    `json:"status"`   // 状态（1未回收 | 2回收审批中 | 3已回收）
}

type DocPermissionUsers struct {
	WaitForApprovalUsers []string // 待审批用户
	NotRecycledUsers     []string // 未回收用户
	RecycleFlowUsers     []string // 回收流程中用户
}

type DistributeRecordInfoRequest struct {
	ID                 string           `json:"id"`                 // 主键id
	WorkflowID         string           `json:"workflowId"`         // 流程id
	Applicant          string           `json:"applicant"`          // 申请人
	ApplyDate          int64            `json:"applyDate"`          // 申请日期
	DistributeType     int32            `json:"distributeType"`     // 发放类型，1内部发放 | 2外部发放
	FileType           int32            `json:"fileType"`           // 文件类型，1内部文件 | 2外部文件
	FileCategory       string           `json:"fileCategory"`       // 文件类别
	TypeDictNodeId     string           `json:"typeDictNodeId"`     // 类型字典节点id
	Reason             string           `json:"reason"`             // 原因
	OtherReason        string           `json:"otherReason"`        // 其他原因
	WishDistributeDate int64            `json:"wishDistributeDate"` // 期望发放日期
	DistributeCount    int              `json:"distributeCount"`    // 发放份数
	SignForCount       int              `json:"signForCount"`       // 签收份数
	DisposeCount       int              `json:"disposeCount"`       // 处置份数
	Status             int              `json:"status"`             // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回 | 5已回收
	ApprovalInfo       ApprovalInfo     `json:"approvalInfo"`       // 审批信息
	DistributeList     []DistributeList `json:"distributeList"`     // 发放清单
}

type DistributeList struct {
	FileID      string       `json:"fileId"`      // 文件id
	FileName    string       `json:"fileName"`    // 文件名
	Number      string       `json:"number"`      // 文件编号
	Version     string       `json:"version"`     // 版本
	Permissions []Permission `json:"permissions"` // 权限
}

type Permission struct {
	FileForm       int32        `json:"fileForm"`       // 文件形式
	FilePermission int32        `json:"filePermission"` // 文件权限
	Recipient      string       `json:"recipient"`      // 接收方
	ReceivedBy     []ReceivedBy `json:"receivedBy"`     // 接收人
}

type ReceivedBy struct {
	UserId        string `json:"userId"`
	UserName      string `json:"userName"`
	SignForStatus int32  `json:"signForStatus"` // 签收状态 1未签收 | 2已签收
	DisposeStatus int32  `json:"disposeStatus"` // 处置状态 1未回收 | 2回收待审批 | 3已回收
}

type GetDocPermissionUsersRequest struct {
	FileId         string `json:"fileId"`
	FileFrom       int32  `json:"fileFrom"`
	FilePermission int32  `json:"filePermission"`
}

type RecycleApprovalRequest struct {
	DistributeID  string        `json:"distributeId"`   // DistributeID
	RecycleBy     string        `json:"recycleBy"`      // 回收人
	RecycleDate   int64         `json:"recycle_date"`   // 回收日期
	RecycleReason string        `json:"recycle_reason"` // 回收原因
	OtherReason   string        `json:"other_reason"`   // 其他原因
	RecycleList   []RecycleList `json:"recycle_list"`   // 回收清单
	WorkflowID    string        `json:"workflowId"`     // 流程id
	ApprovalInfo  ApprovalInfo  `json:"approvalInfo"`   // 审批信息
}

type RecycleList struct {
	FileID      string           `json:"file_id"`     // 文件ID
	Permissions []FilePermission `json:"permissions"` // 文件权限
}

type FilePermission struct {
	FileForm       int      `json:"fileForm"`       // 文件形式
	FilePermission int      `json:"filePermission"` // 文件权限
	ReceivedBy     []string `json:"receivedBy"`     // 接收人
}

type DisposalApprovalRequest struct {
	DistributeID   string         `json:"distributeId"`   // 发放记录ID
	DisposalBy     string         `json:"disposalBy"`     // 处置人
	DisposalDate   int64          `json:"recycle_date"`   // 处置日期
	DisposalReason string         `json:"recycle_reason"` // 处置方式
	DisposalList   []DisposalList `json:"recycle_list"`   // 处置清单
	WorkflowID     string         `json:"workflowId"`     // 流程id
	ApprovalInfo   ApprovalInfo   `json:"approvalInfo"`   // 审批信息
}

type DisposalList struct {
	FileID     string   `json:"file_id"`    // 文件ID
	FileForm   int      `json:"fileForm"`   // 文件形式
	ReceivedBy []string `json:"receivedBy"` // 接收人
}

type PageInfo struct {
	Page     uint64 `json:"page,optional" validate:"number" form:"page,optional"`
	PageSize uint64 `json:"pageSize,optional" validate:"number,max=100000" form:"pageSize,optional"`
	Total    uint64 `json:"total,optional"`
	NoPage   bool   `json:"noPage,optional" form:"noPage,optional"`
}

type GetDistributeListRequest struct {
	PageInfo
	FileNumber     string   `form:"fileNumber,optional"`     // 文件编号
	FileName       string   `form:"fileName,optional"`       // 文件名称
	FileType       int32    `form:"fileType,optional"`       // 文件类型
	FileCategory   []string `form:"fileCategory,optional"`   // 文件类别id
	DistributeType int32    `form:"distributeType,optional"` // 发放类型
	Status         int32    `form:"status,optional"`         // 状态，0全部 | 1待提交 | 2待审批 | 3已审批 | 4已驳回
	Applicant      []string `form:"applicant,optional"`      // 申请人id
}

type GetDistributeListResp struct {
	Total int64                   `json:"total"`
	Data  []GetDistributeListInfo `json:"data"`
}

type GetDistributeListInfo struct {
	ID                 string           `json:"id"`                 // 发放列表id
	Applicant          string           `json:"applicant"`          // 申请人
	ApplyDate          int64            `json:"applyDate"`          // 申请时间
	DistributeType     int              `json:"distributeType"`     // 发放类型，1内部发放 | 2外部发放
	FileType           int              `json:"fileType"`           // 文件类型，1内部文件 | 2外部文件
	FileCategory       string           `json:"fileCategory"`       // 文件类别
	WishDistributeDate int64            `json:"wishDistributeDate"` // 期望发放时间
	Reason             string           `json:"reason"`             // 发放原因
	OtherReason        string           `json:"otherReason"`        // 其他原因
	Status             int              `json:"status"`             // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
	WorkflowID         string           `json:"workflowID"`         // 流程id
	ApprovalInfo       ApprovalInfo     `json:"approvalInfo"`       // 审批信息
	Received           []DistributeUser `json:"received"`           // 已接收人
	NotReceived        []DistributeUser `json:"notReceived"`        // 未接收人
	Recycle            []DistributeUser `json:"recycle"`            // 已回收人
}

type DistributeUser struct {
	UserID   string `json:"userId"`   // 用户id
	FileForm int    `json:"fileForm"` // 文件形式，1电子文件 | 2纸质文件
}

type DistributeApprovalRequest struct {
	WorkflowID   string       `json:"workflowId"`   // 流程id
	Status       int          `json:"status"`       // 审批状态
	ApprovalInfo ApprovalInfo `json:"approvalInfo"` // 审批信息
}
