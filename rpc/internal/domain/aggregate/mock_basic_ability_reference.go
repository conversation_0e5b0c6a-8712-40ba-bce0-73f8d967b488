// Code generated by MockGen. DO NOT EDIT.
// Source: basic_ability_reference.go

// Package aggregate is a generated GoMock package.
package aggregate

import (
	context "context"
	entity "docvault/rpc/internal/domain/entity"
	value "docvault/rpc/internal/domain/value"
	mapper "docvault/rpc/internal/infrastructure/adapter/mapper"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockBasicAbilityReference is a mock of BasicAbilityReference interface.
type MockBasicAbilityReference struct {
	ctrl     *gomock.Controller
	recorder *MockBasicAbilityReferenceMockRecorder
}

// MockBasicAbilityReferenceMockRecorder is the mock recorder for MockBasicAbilityReference.
type MockBasicAbilityReferenceMockRecorder struct {
	mock *MockBasicAbilityReference
}

// NewMockBasicAbilityReference creates a new mock instance.
func NewMockBasicAbilityReference(ctrl *gomock.Controller) *MockBasicAbilityReference {
	mock := &MockBasicAbilityReference{ctrl: ctrl}
	mock.recorder = &MockBasicAbilityReferenceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBasicAbilityReference) EXPECT() *MockBasicAbilityReferenceMockRecorder {
	return m.recorder
}

// GenerateID mocks base method.
func (m *MockBasicAbilityReference) GenerateID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateID")
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateID indicates an expected call of GenerateID.
func (mr *MockBasicAbilityReferenceMockRecorder) GenerateID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateID", reflect.TypeOf((*MockBasicAbilityReference)(nil).GenerateID))
}

// GetFileInfo mocks base method.
func (m *MockBasicAbilityReference) GetFileInfo(ctx context.Context, fileId string) (entity.File, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileInfo", ctx, fileId)
	ret0, _ := ret[0].(entity.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileInfo indicates an expected call of GetFileInfo.
func (mr *MockBasicAbilityReferenceMockRecorder) GetFileInfo(ctx, fileId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileInfo", reflect.TypeOf((*MockBasicAbilityReference)(nil).GetFileInfo), ctx, fileId)
}

// GetWorkflowInfo mocks base method.
func (m *MockBasicAbilityReference) GetWorkflowInfo(ctx context.Context, workflowId string) (value.WorkflowInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowInfo", ctx, workflowId)
	ret0, _ := ret[0].(value.WorkflowInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowInfo indicates an expected call of GetWorkflowInfo.
func (mr *MockBasicAbilityReferenceMockRecorder) GetWorkflowInfo(ctx, workflowId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowInfo", reflect.TypeOf((*MockBasicAbilityReference)(nil).GetWorkflowInfo), ctx, workflowId)
}

// StartTX mocks base method.
func (m *MockBasicAbilityReference) StartTX(ctx context.Context) mapper.Transaction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartTX", ctx)
	ret0, _ := ret[0].(mapper.Transaction)
	return ret0
}

// StartTX indicates an expected call of StartTX.
func (mr *MockBasicAbilityReferenceMockRecorder) StartTX(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTX", reflect.TypeOf((*MockBasicAbilityReference)(nil).StartTX), ctx)
}

// TranslateOrganizationName mocks base method.
func (m *MockBasicAbilityReference) TranslateOrganizationName(ctx context.Context, organizationId string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TranslateOrganizationName", ctx, organizationId)
	ret0, _ := ret[0].(string)
	return ret0
}

// TranslateOrganizationName indicates an expected call of TranslateOrganizationName.
func (mr *MockBasicAbilityReferenceMockRecorder) TranslateOrganizationName(ctx, organizationId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TranslateOrganizationName", reflect.TypeOf((*MockBasicAbilityReference)(nil).TranslateOrganizationName), ctx, organizationId)
}

// TranslateUserNickname mocks base method.
func (m *MockBasicAbilityReference) TranslateUserNickname(ctx context.Context, userId string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TranslateUserNickname", ctx, userId)
	ret0, _ := ret[0].(string)
	return ret0
}

// TranslateUserNickname indicates an expected call of TranslateUserNickname.
func (mr *MockBasicAbilityReferenceMockRecorder) TranslateUserNickname(ctx, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TranslateUserNickname", reflect.TypeOf((*MockBasicAbilityReference)(nil).TranslateUserNickname), ctx, userId)
}
