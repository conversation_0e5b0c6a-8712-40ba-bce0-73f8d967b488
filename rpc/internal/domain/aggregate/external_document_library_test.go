package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/utils"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
)

func TestExternalDocumentLibrary_BatchCreate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRef := NewMockExternalDocumentLibraryReference(ctrl)
	mockBasic := NewMockBasicAbilityReference(ctrl)
	service := NewExternalDocumentLibrary(mockRef, mockBasic)

	ctx := context.Background()
	userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
	userInfo.OrganizationId = "org1"
	ctx = userInfo.SetContext(ctx)

	<PERSON><PERSON>("Test BatchCreate", t, func() {
		<PERSON><PERSON>("Success", func() {
			req := []ExternalDocumentCreateRequest{
				{
					NumberPrefix:                    "NP-",
					Name:                            "外部文档1",
					TypeDictionaryNodeId:            "type1",
					DomainDictionaryNodeId:          "domain1",
					AuthenticationDictionaryNodeIds: []string{"auth1"},
					OrgType:                         1,
					OrgID:                           "org1",
				},
			}

			// mock 检查文档名重复
			mockRef.EXPECT().
				GetExternalDocNamesByOrgID(gomock.Any(), gomock.Any()).
				Return([]string{}, nil)
			// mock 批量获取最大编号
			mockRef.EXPECT().
				BatchGetMaxNumberPostfix(gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]value.DictNodeIdAndNumberPostfix{
					{DictNodeId: "type1", NumberPostfix: 5},
				}, nil)
			// mock GenerateID
			mockBasic.EXPECT().GenerateID().Return("mock-id").AnyTimes()
			// mock StartTX
			mockBasic.EXPECT().StartTX(gomock.Any()).Return((mapper.Transaction)(nil)).AnyTimes()
			// mock Create
			mockRef.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

			resp, err := service.BatchCreate(ctx, req)
			So(err, ShouldBeNil)
			So(resp, ShouldHaveLength, 1)
			So(resp[0].ID, ShouldEqual, "mock-id")
			So(resp[0].TypeDictionaryNodeId, ShouldEqual, "type1")
			So(resp[0].DomainDictionaryNodeId, ShouldEqual, "domain1")
			So(resp[0].AuthenticationDictionaryNodeIds, ShouldResemble, []string{"auth1"})
		})

		Convey("Error - 文档名重复", func() {
			req := []ExternalDocumentCreateRequest{
				{
					NumberPrefix:                    "NP-",
					Name:                            "外部文档1",
					TypeDictionaryNodeId:            "type1",
					DomainDictionaryNodeId:          "domain1",
					AuthenticationDictionaryNodeIds: []string{"auth1"},
					OrgType:                         1,
					OrgID:                           "org1",
				},
			}

			mockRef.EXPECT().
				GetExternalDocNamesByOrgID(gomock.Any(), gomock.Any()).
				Return([]string{"外部文档1"}, nil)

			resp, err := service.BatchCreate(ctx, req)
			So(err, ShouldNotBeNil)
			So(resp, ShouldBeNil)
		})

		Convey("Error - BatchGetMaxNumberPostfix fails", func() {
			req := []ExternalDocumentCreateRequest{
				{
					NumberPrefix:                    "NP-",
					Name:                            "外部文档1",
					TypeDictionaryNodeId:            "type1",
					DomainDictionaryNodeId:          "domain1",
					AuthenticationDictionaryNodeIds: []string{"auth1"},
					OrgType:                         1,
					OrgID:                           "org1",
				},
			}

			mockRef.EXPECT().
				GetExternalDocNamesByOrgID(gomock.Any(), gomock.Any()).
				Return([]string{}, nil)
			mockRef.EXPECT().
				BatchGetMaxNumberPostfix(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil, errors.New("db error"))

			resp, err := service.BatchCreate(ctx, req)
			So(err, ShouldNotBeNil)
			So(resp, ShouldBeNil)
		})

		Convey("Error - Create fails", func() {
			req := []ExternalDocumentCreateRequest{
				{
					NumberPrefix:                    "NP-",
					Name:                            "外部文档1",
					TypeDictionaryNodeId:            "type1",
					DomainDictionaryNodeId:          "domain1",
					AuthenticationDictionaryNodeIds: []string{"auth1"},
					OrgType:                         1,
					OrgID:                           "org1",
				},
			}

			mockRef.EXPECT().
				GetExternalDocNamesByOrgID(gomock.Any(), gomock.Any()).
				Return([]string{}, nil)
			mockRef.EXPECT().
				BatchGetMaxNumberPostfix(gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]value.DictNodeIdAndNumberPostfix{
					{DictNodeId: "type1", NumberPostfix: 5},
				}, nil)
			mockBasic.EXPECT().GenerateID().Return("mock-id").AnyTimes()
			mockBasic.EXPECT().StartTX(gomock.Any()).Return((mapper.Transaction)(nil)).AnyTimes()
			mockRef.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("insert error"))

			resp, err := service.BatchCreate(ctx, req)
			So(err, ShouldNotBeNil)
			So(resp, ShouldBeNil)
		})
	})
}

func TestExternalDocumentLibrary_GetList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRef := NewMockExternalDocumentLibraryReference(ctrl)
	mockBasic := NewMockBasicAbilityReference(ctrl)
	service := NewExternalDocumentLibrary(mockRef, mockBasic)

	ctx := context.Background()
	userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
	userInfo.OrganizationId = "org1"
	ctx = userInfo.SetContext(ctx)

	Convey("Test GetList", t, func() {
		Convey("Success", func() {
			req := &ExternalDocumentPageReq{
				Page:                           1,
				PageSize:                       10,
				NoPage:                         false,
				Number:                         "NP-1",
				Name:                           "外部文档1",
				TypeDictionaryNodeIds:          []string{"type1"},
				DomainDictionaryNodeId:         "domain1",
				AuthenticationDictionaryNodeId: "auth1",
				OrgID:                          "org1",
			}

			mockRef.EXPECT().
				GetExternalDocList(gomock.Any(), gomock.Any()).
				Return(&value.ExternalDocumentPageInfo{
					Total: 1,
					Data: []value.ExternalDocumentList{
						{
							ID:                              "docid1",
							Name:                            "外部文档1",
							Number:                          "NP-1",
							TypeDictionaryNodeId:            "type1",
							DomainDictionaryNodeId:          "domain1",
							AuthenticationDictionaryNodeIds: []string{"auth1"},
						},
					},
				}, nil)

			resp, err := service.GetList(ctx, req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1)
			So(resp.Data, ShouldHaveLength, 1)
			So(resp.Data[0].ID, ShouldEqual, "docid1")
			So(resp.Data[0].Name, ShouldEqual, "外部文档1")
			So(resp.Data[0].TypeDictionaryNodeId, ShouldEqual, "type1")
		})

		Convey("Error - GetExternalDocList fails", func() {
			req := &ExternalDocumentPageReq{
				Page: 1, PageSize: 10, OrgID: "org1",
			}

			mockRef.EXPECT().
				GetExternalDocList(gomock.Any(), gomock.Any()).
				Return(nil, errors.New("db error"))

			resp, err := service.GetList(ctx, req)
			So(err, ShouldNotBeNil)
			So(resp, ShouldBeNil)
		})
	})
}

func TestExternalDocumentLibrary_Change(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRef := NewMockExternalDocumentLibraryReference(ctrl)
	mockBasic := NewMockBasicAbilityReference(ctrl)
	service := NewExternalDocumentLibrary(mockRef, mockBasic)

	// 全局mock，所有分支都能命中
	mockRef.EXPECT().
		GetMaxNumberPostfix(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(5, nil).AnyTimes()
	mockRef.EXPECT().
		ChangeExternalDocument(gomock.Any(), gomock.Any()).
		Return(nil).AnyTimes()

	Convey("Test Change", t, func() {
		Convey("Success", func() {
			ctx := context.Background()
			userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
			userInfo.OrganizationId = "org1"
			ctx = userInfo.SetContext(ctx)

			req := ExternalDocumentChangeRequest{
				ID: "docid1",
				ExternalDocumentCreateRequest: ExternalDocumentCreateRequest{
					Name:                            "新文档名",
					OrgID:                           "org1",
					TypeDictionaryNodeId:            "type1",
					NumberPrefix:                    "NP-",
					OriginalNumber:                  "ON-1",
					OriginalVersion:                 "V1",
					DocType:                         "typeA",
					Domain:                          "domainA",
					OriginalDocNumber:               "ODN-1",
					PublishDocNumber:                "PDN-1",
					PublishDepartment:               "部门A",
					PublishDate:                     time.Now().UnixMilli(),
					EffectiveDate:                   time.Now().UnixMilli(),
					Authentications:                 []string{"auth1"},
					AuthenticationDictionaryNodeIds: []string{"auth1"},
					DomainDictionaryNodeId:          "domain1",
					FileID:                          "fileid1",
				},
			}

			mockRef.EXPECT().
				GetExternalDocumentById(gomock.Any(), "docid1").
				Return(&entity.ExternalDocument{
					ID:                   "docid1",
					Name:                 "旧文档名",
					TypeDictionaryNodeId: "type1",
					Number:               "NP-5",
					Version:              "A/0",
					OrganizationID:       "org1",
				}, nil)
			mockRef.EXPECT().
				GetExternalDocByOrgIDAndName(gomock.Any(), "org1", "新文档名").
				Return(nil, nil)
			mockBasic.EXPECT().GenerateID().Return("mock-id").AnyTimes()

			err := service.Change(ctx, req)
			So(err, ShouldBeNil)
		})

		Convey("Error - GetExternalDocumentById fails", func() {
			ctx := context.Background()
			userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
			userInfo.OrganizationId = "org1"
			ctx = userInfo.SetContext(ctx)

			req := ExternalDocumentChangeRequest{ID: "docid1"}
			mockRef.EXPECT().
				GetExternalDocumentById(gomock.Any(), "docid1").
				Return(nil, errors.New("not found"))

			err := service.Change(ctx, req)
			So(err, ShouldNotBeNil)
		})

		Convey("Error - 文件名重复", func() {
			ctx := context.Background()
			userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
			userInfo.OrganizationId = "org1"
			ctx = userInfo.SetContext(ctx)

			req := ExternalDocumentChangeRequest{
				ID: "docid1",
				ExternalDocumentCreateRequest: ExternalDocumentCreateRequest{
					Name:  "新文档名",
					OrgID: "org1",
				},
			}
			mockRef.EXPECT().
				GetExternalDocumentById(gomock.Any(), "docid1").
				Return(&entity.ExternalDocument{ID: "docid1", OrganizationID: "org1"}, nil)
			mockRef.EXPECT().
				GetExternalDocByOrgIDAndName(gomock.Any(), "org1", "新文档名").
				Return(&entity.ExternalDocument{ID: "otherid"}, nil)

			err := service.Change(ctx, req)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "文件名称已存在")
		})

		Convey("Error - GetMaxNumberPostfix fails", func() {
			ctx := context.Background()
			userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
			userInfo.OrganizationId = "org1"
			ctx = userInfo.SetContext(ctx)

			req := ExternalDocumentChangeRequest{
				ID: "docid1",
				ExternalDocumentCreateRequest: ExternalDocumentCreateRequest{
					Name:                 "新文档名",
					OrgID:                "org1",
					TypeDictionaryNodeId: "type1",
				},
			}
			mockRef.EXPECT().
				GetExternalDocumentById(gomock.Any(), "docid1").
				Return(&entity.ExternalDocument{ID: "docid1", OrganizationID: "org1"}, nil)
			mockRef.EXPECT().
				GetExternalDocByOrgIDAndName(gomock.Any(), "org1", "新文档名").
				Return(nil, nil)
			// 这里不再mock GetMaxNumberPostfix，命中全局mock

			err := service.Change(ctx, req)
			So(err, ShouldNotBeNil)
		})

		Convey("Error - ChangeExternalDocument fails", func() {
			ctx := context.Background()
			userInfo := utils.NewUserLoginInfo("u1", "t1", 0, false)
			userInfo.OrganizationId = "org1"
			ctx = userInfo.SetContext(ctx)

			req := ExternalDocumentChangeRequest{
				ID: "docid1",
				ExternalDocumentCreateRequest: ExternalDocumentCreateRequest{
					Name:                 "新文档名",
					OrgID:                "org1",
					TypeDictionaryNodeId: "type1",
				},
			}
			mockRef.EXPECT().
				GetExternalDocumentById(gomock.Any(), "docid1").
				Return(&entity.ExternalDocument{ID: "docid1", OrganizationID: "org1", TypeDictionaryNodeId: "type1"}, nil)
			mockRef.EXPECT().
				GetExternalDocByOrgIDAndName(gomock.Any(), "org1", "新文档名").
				Return(nil, nil)
			// 这里不再mock ChangeExternalDocument，命中全局mock
			mockBasic.EXPECT().GenerateID().Return("mock-id").AnyTimes()

			err := service.Change(ctx, req)
			So(err, ShouldNotBeNil)
		})
	})
}

func TestExternalDocumentLibrary_valueToResp(t *testing.T) {
	service := NewExternalDocumentLibrary(nil, nil)
	list := []value.ExternalDocumentList{
		{
			ID:                              "id1",
			Name:                            "doc1",
			TypeDictionaryNodeId:            "type1",
			DomainDictionaryNodeId:          "domain1",
			AuthenticationDictionaryNodeIds: []string{"auth1"},
		},
	}
	resp := service.valueToResp(list)
	if len(resp) != 1 || resp[0].ID != "id1" || resp[0].Name != "doc1" {
		t.Errorf("valueToResp failed, got %+v", resp)
	}
}

func TestExternalDocumentLibrary_infoPackaging(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockBasic := NewMockBasicAbilityReference(ctrl)
	mockBasic.EXPECT().GenerateID().Return("mock-id").AnyTimes()
	service := NewExternalDocumentLibrary(nil, mockBasic)

	reqs := []ExternalDocumentCreateRequest{
		{
			NumberPrefix:         "NP-",
			Name:                 "doc1",
			TypeDictionaryNodeId: "type1",
			OrgID:                "org1",
		},
	}
	maxMap := map[string]int{"type1": 5}
	docs, infos := service.infoPackaging(reqs, maxMap, "user1")
	if len(docs) != 1 || docs[0].Name != "doc1" || infos[0].ID != "mock-id" {
		t.Errorf("infoPackaging failed, got %+v %+v", docs, infos)
	}
}

func TestExternalDocumentLibrary_getMaxNumberMap(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRef := NewMockExternalDocumentLibraryReference(ctrl)
	service := NewExternalDocumentLibrary(mockRef, nil)

	ctx := context.Background()
	reqs := []ExternalDocumentCreateRequest{
		{TypeDictionaryNodeId: "type1", OrgID: "org1"},
	}
	mockRef.EXPECT().
		BatchGetMaxNumberPostfix(gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]value.DictNodeIdAndNumberPostfix{
			{DictNodeId: "type1", NumberPostfix: 5},
		}, nil)
	maxMap, err := service.getMaxNumberMap(ctx, reqs)
	if err != nil || maxMap["type1"] != 5 {
		t.Errorf("getMaxNumberMap failed, got %+v, err=%v", maxMap, err)
	}
}

func TestExternalDocumentLibrary_isRepetitionDoc(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRef := NewMockExternalDocumentLibraryReference(ctrl)
	service := NewExternalDocumentLibrary(mockRef, nil)

	ctx := context.Background()
	names := []string{"doc1", "doc2"}
	mockRef.EXPECT().
		GetExternalDocNamesByOrgID(gomock.Any(), gomock.Any()).
		Return([]string{"doc2"}, nil)
	repeat, err := service.isRepetitionDoc(ctx, names, "org1")
	if err != nil || !repeat {
		t.Errorf("isRepetitionDoc failed, got %v, err=%v", repeat, err)
	}
}

func TestExternalDocumentLibrary_getVersion(t *testing.T) {
	service := NewExternalDocumentLibrary(nil, nil)
	ver := service.getVersion("A/0")
	if ver != "A/1" {
		t.Errorf("getVersion failed, got %v", ver)
	}
	ver = service.getVersion("Z/9")
	if ver != "Z/10" {
		t.Errorf("getVersion failed, got %v", ver)
	}
}

func TestExternalDocumentLibrary_getNumber(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRef := NewMockExternalDocumentLibraryReference(ctrl)
	service := NewExternalDocumentLibrary(mockRef, nil)

	ctx := context.Background()
	changeReq := ExternalDocumentChangeRequest{
		ExternalDocumentCreateRequest: ExternalDocumentCreateRequest{
			TypeDictionaryNodeId: "type1",
			NumberPrefix:         "NP-",
		},
	}
	docInfo := &entity.ExternalDocument{
		TypeDictionaryNodeId: "type2",
		Number:               "NP-5",
		OrganizationID:       "org1",
	}
	mockRef.EXPECT().
		GetMaxNumberPostfix(gomock.Any(), "org1", "type1").
		Return(5, nil)
	number, err := service.getNumber(ctx, changeReq, docInfo)
	if err != nil || number != "NP-6" {
		t.Errorf("getNumber failed, got %v, err=%v", number, err)
	}
}
