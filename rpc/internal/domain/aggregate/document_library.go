package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

type DocumentLibrary struct {
	DocumentLibraryReference
	BasicAbilityReference
}

func NewDocumentLibrary(documentLibraryReference DocumentLibraryReference, basicAbilityReference BasicAbilityReference) *DocumentLibrary {
	return &DocumentLibrary{
		DocumentLibraryReference: documentLibraryReference,
		BasicAbilityReference:    basicAbilityReference,
	}
}

func (d *DocumentLibrary) SaveDistributeRecord(ctx context.Context, req *DistributeRecordInfoRequest) error {
	distributeRecordInfo := &entity.DistributeRecordInfo{}
	req.ID = d.GenerateID()
	err := utils.StructCopy(ctx, distributeRecordInfo, req)
	if err != nil {
		logc.Error(ctx, "发放信息 copy 失败:", err)
		return err
	}
	err = d.CreateDistributeRecord(ctx, distributeRecordInfo)
	if err != nil {
		logc.Error(ctx, "发放信息创建失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) GetDocPermissionUsers(ctx context.Context, req *GetDocPermissionUsersRequest) (*DocPermissionUsers, error) {
	docPermissionUsers, err := d.GetUserIdsByFileIdAndFileFromAndFilePermission(ctx, req.FileId, req.FileFrom, req.FilePermission)
	if err != nil {
		logc.Error(ctx, "获取文件权限用户失败：", err)
		return nil, err
	}
	return &DocPermissionUsers{
		WaitForApprovalUsers: docPermissionUsers.WaitForApprovalUsers,
		NotRecycledUsers:     docPermissionUsers.NotRecycledUsers,
		RecycleFlowUsers:     docPermissionUsers.RecycleFlowUsers,
	}, nil
}

func (d *DocumentLibrary) UpdateDistributeStatus(ctx context.Context, id string, status int32) error {
	err := d.AlterDistributeStatus(ctx, id, status)
	return err
}

func (d *DocumentLibrary) UpdateDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error {
	err := d.AlterDistributeStatusByWorkflowId(ctx, workflowId, status)
	return err
}

func (d *DocumentLibrary) SaveRecycleApprovalInfo(ctx context.Context, req *RecycleApprovalRequest) error {
	recycleApprovalInfo := &entity.RecycleApprovalInfo{}
	err := utils.StructCopy(ctx, recycleApprovalInfo, req)
	if err != nil {
		logc.Error(ctx, "回收审批信息 copy 失败:", err)
		return err
	}
	err = d.SaveRecycleInfo(ctx, recycleApprovalInfo)
	if err != nil {
		logc.Error(ctx, "回收审批信息保存失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) SaveDisposalApprovalInfo(ctx context.Context, req *DisposalApprovalRequest) error {
	disposalApprovalInfo := &entity.DisposalApprovalInfo{}
	err := utils.StructCopy(ctx, disposalApprovalInfo, req)
	if err != nil {
		logc.Error(ctx, "处置审批信息 copy 失败:", err)
		return err
	}
	for _, v := range req.DisposalList {
		disposalApprovalInfo.DisposalCount = len(v.ReceivedBy)
	}
	err = d.SaveDisposalInfo(ctx, disposalApprovalInfo)
	if err != nil {
		logc.Error(ctx, "处置审批信息保存失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) GetDistributeListInfos(ctx context.Context, req *GetDistributeListRequest) (*GetDistributeListResp, error) {
	getDistributeListRequest := value.GetDistributeListRequest{
		Page:           req.PageInfo.Page,
		PageSize:       req.PageInfo.PageSize,
		NoPage:         req.PageInfo.NoPage,
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       req.FileType,
		FileCategory:   req.FileCategory,
		DistributeType: req.DistributeType,
		Status:         req.Status,
		Applicant:      req.Applicant,
	}
	total, distributeListInfos, err := d.GetDistributeInfos(ctx, &getDistributeListRequest)
	if err != nil {
		logc.Error(ctx, "获取发放列表信息失败：", err)
		return nil, err
	}
	var getDistributeListInfos []GetDistributeListInfo
	err = utils.StructCopy(ctx, &getDistributeListInfos, distributeListInfos)
	if err != nil {
		logc.Error(ctx, "获取发放列表信息 copy 失败：", err)
		return nil, err
	}
	return &GetDistributeListResp{
		Total: int64(total),
		Data:  getDistributeListInfos,
	}, nil
}

func (d *DocumentLibrary) SaveDistributeApproval(ctx context.Context, req *DistributeApprovalRequest) error {
	// 发放审批流程信息
	distributeApprovalInfo := &value.DistributeApprovalRequest{}
	err := utils.StructCopy(ctx, distributeApprovalInfo, req)
	if err != nil {
		logc.Error(ctx, "发放审批信息 copy 失败:", err)
		return err
	}
	// 持久化发放审批流程信息
	err = d.SaveDistributeApprovalWorkflow(ctx, distributeApprovalInfo)
	if err != nil {
		logc.Error(ctx, "发放审批信息保存失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) GetDistributeInventories(ctx context.Context, id string) ([]DistributeInventory, error) {
	distributeInventories, err := d.GetDistributeInventoryByID(ctx, id)
	if err != nil {
		logc.Error(ctx, "获取发放清单信息失败：", err)
		return nil, err
	}
	var distributeInventorys []DistributeInventory
	err = utils.StructCopy(ctx, distributeInventorys, distributeInventories)
	if err != nil {
		logc.Error(ctx, "发放清单信息 copy 失败：", err)
		return nil, err
	}
	return distributeInventorys, nil
}

func (d *DocumentLibrary) UpdateDistributeInventory(ctx context.Context, req *DistributeRecordInfoRequest) error {
	distributeRecordInfo := &entity.DistributeRecordInfo{}
	err := utils.StructCopy(ctx, distributeRecordInfo, req)
	if err != nil {
		logc.Error(ctx, "发放信息 copy 失败:", err)
		return err
	}

	// 更新发放列表信息，保存发放清单信息
	err = d.UpdateDistributeInventoryByID(ctx, distributeRecordInfo)
	if err != nil {
		logc.Error(ctx, "更新发放清单信息失败：", err)
		return err
	}

	return nil
}
