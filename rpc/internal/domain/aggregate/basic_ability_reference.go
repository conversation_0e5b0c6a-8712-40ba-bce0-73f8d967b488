package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
)

//go:generate mockgen -source=basic_ability_reference.go -destination=mock_basic_ability_reference.go -package=aggregate
type BasicAbilityReference interface {
	// 生成ID
	GenerateID() string

	// 翻译名称
	TranslateUserNickname(ctx context.Context, userId string) string

	// 翻译组织名称
	TranslateOrganizationName(ctx context.Context, organizationId string) string

	// 获取文件信息
	GetFileInfo(ctx context.Context, fileId string) (entity.File, error)

	// 获取审批流信息
	GetWorkflowInfo(ctx context.Context, workflowId string) (workflowInfo value.WorkflowInfo, err error)

	// 开始事务
	StartTX(ctx context.Context) mapper.Transaction
}
