package aggregate

import (
	"context"
	"time"
)

// InternalDocumentLibraryService 内部文档库service接口
// 该接口用于定义内部文档库特有的方法
type InternalDocumentLibraryService interface {
	// 新增
	BatchCreate(ctx context.Context, internalDocument []InternalDocumentCreateRequest) (internalDocumentInfos []InternalDocumentInfo, err error)

	// 变更
	Change(ctx context.Context, internalDocument InternalDocumentChangeRequest) error
}

type InternalDocumentCreateRequest struct {
	NoPrefix          string       // 编号前缀
	Name              string       // 文件名称
	FileID            string       // 文件id
	DocCategoryID     string       // 类别ID
	DepartmentID      string       // 编制部门ID
	ApprovalInfo      ApprovalInfo // 审批信息
	PublishDate       time.Time    // 发布日期
	EffectiveDate     time.Time    // 实施日期
	OriginalNo        string       // 原文件编号
	OriginalVersionNo string       // 原文件版本
	OrganizationID    string       // 子公司ID
	AuthorID          string       // 编制人ID
}

type ApprovalInfo struct {
	Auditors  []ApprovalInfoItem `json:"auditors"`  // 审核人
	Approvers []ApprovalInfoItem `json:"approvers"` // 批准人
}

type ApprovalInfoItem struct {
	UserID     string `json:"userId"`
	PassedDate int64  `json:"passedDate"`
}

type InternalDocumentChangeRequest struct {
	ID                string       // 主键
	NoPrefix          string       // 编号前缀
	Name              string       // 文件名称
	FileID            string       // 文件id
	DocCategoryID     string       // 类别ID
	DepartmentID      string       // 编制部门ID
	ApprovalInfo      ApprovalInfo // 审批信息
	PublishDate       time.Time    // 发布日期
	EffectiveDate     time.Time    // 实施日期
	OriginalNo        string       // 原文件编号
	OriginalVersionNo string       // 原文件版本
	OrganizationID    string       // 子公司ID
	AuthorID          string       // 编制人ID
}

type InternalDocumentInfo struct {
	ID                string    // 主键
	Name              string    // 文件名称
	No                int       // 文件编号
	NoPrefix          string    // 编号前缀
	VersionNo         int       // 版本号
	FileID            string    // 文件id
	DocCategoryID     string    // 类别ID
	DepartmentID      string    // 编制部门ID
	PublishDate       time.Time // 发布日期
	EffectiveDate     time.Time // 实施日期
	OriginalNo        string    // 原文件编号
	OriginalVersionNo string    // 原文件版本
	OrganizationID    string    // 子公司ID
	AuthorID          string    // 编制人ID
	Status            int       // 状态
}
