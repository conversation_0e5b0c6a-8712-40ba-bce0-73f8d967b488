package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
)

//go:generate mockgen -source=external_document_library_reference.go -destination=mock_external_document_library_reference.go -package=aggregate
type ExternalDocumentLibraryReference interface {
	// 批量获取最大编号后缀
	BatchGetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds []string) ([]value.DictNodeIdAndNumberPostfix, error)

	// 批量保存
	Create(ctx context.Context, externalDocuments []entity.ExternalDocument) error

	// 获取外部文档列表
	GetExternalDocList(ctx context.Context, req *value.ExternalDocumentPage) (externalDocumentGroupPageResp *value.ExternalDocumentPageInfo, err error)

	// 修改外部文档信息
	ChangeExternalDocument(ctx context.Context, externalDocument entity.ExternalDocument) error

	// 根据id查询文档信息
	GetExternalDocumentById(ctx context.Context, id string) (*entity.ExternalDocument, error)

	// 获取最大编号后缀
	GetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds string) (int, error)

	// 根据组织id获取文件名称
	GetExternalDocNamesByOrgID(ctx context.Context, orgID string) ([]string, error)

	// 根据组织id和文件名称获取文件信息
	GetExternalDocByOrgIDAndName(ctx context.Context, orgID string, name string) (*entity.ExternalDocument, error)

	// 根据id批量查询文档信息
	GetExternalDocsByIds(ctx context.Context, ids []string) ([]entity.ExternalDocument, error)

	// 根据组织id和文件名称批量查询文档信息
	GetExternalDocsByOrgIDAndNames(ctx context.Context, orgID string, names []string) ([]entity.ExternalDocument, error)
}
