// Code generated by MockGen. DO NOT EDIT.
// Source: document_library_reference.go

// Package aggregate is a generated GoMock package.
package aggregate

import (
	context "context"
	entity "docvault/rpc/internal/domain/entity"
	value "docvault/rpc/internal/domain/value"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockDocumentLibraryReference is a mock of DocumentLibraryReference interface.
type MockDocumentLibraryReference struct {
	ctrl     *gomock.Controller
	recorder *MockDocumentLibraryReferenceMockRecorder
}

// MockDocumentLibraryReferenceMockRecorder is the mock recorder for MockDocumentLibraryReference.
type MockDocumentLibraryReferenceMockRecorder struct {
	mock *MockDocumentLibraryReference
}

// NewMockDocumentLibraryReference creates a new mock instance.
func NewMockDocumentLibraryReference(ctrl *gomock.Controller) *MockDocumentLibraryReference {
	mock := &MockDocumentLibraryReference{ctrl: ctrl}
	mock.recorder = &MockDocumentLibraryReferenceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocumentLibraryReference) EXPECT() *MockDocumentLibraryReferenceMockRecorder {
	return m.recorder
}

// AlterDistributeStatus mocks base method.
func (m *MockDocumentLibraryReference) AlterDistributeStatus(ctx context.Context, id string, status int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AlterDistributeStatus", ctx, id, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// AlterDistributeStatus indicates an expected call of AlterDistributeStatus.
func (mr *MockDocumentLibraryReferenceMockRecorder) AlterDistributeStatus(ctx, id, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AlterDistributeStatus", reflect.TypeOf((*MockDocumentLibraryReference)(nil).AlterDistributeStatus), ctx, id, status)
}

// AlterDistributeStatusByWorkflowId mocks base method.
func (m *MockDocumentLibraryReference) AlterDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AlterDistributeStatusByWorkflowId", ctx, workflowId, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// AlterDistributeStatusByWorkflowId indicates an expected call of AlterDistributeStatusByWorkflowId.
func (mr *MockDocumentLibraryReferenceMockRecorder) AlterDistributeStatusByWorkflowId(ctx, workflowId, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AlterDistributeStatusByWorkflowId", reflect.TypeOf((*MockDocumentLibraryReference)(nil).AlterDistributeStatusByWorkflowId), ctx, workflowId, status)
}

// CreateDistributeRecord mocks base method.
func (m *MockDocumentLibraryReference) CreateDistributeRecord(ctx context.Context, distributeRecordInfo *entity.DistributeRecordInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDistributeRecord", ctx, distributeRecordInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDistributeRecord indicates an expected call of CreateDistributeRecord.
func (mr *MockDocumentLibraryReferenceMockRecorder) CreateDistributeRecord(ctx, distributeRecordInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDistributeRecord", reflect.TypeOf((*MockDocumentLibraryReference)(nil).CreateDistributeRecord), ctx, distributeRecordInfo)
}

// GetDistributeInfos mocks base method.
func (m *MockDocumentLibraryReference) GetDistributeInfos(ctx context.Context, req *value.GetDistributeListRequest) (int, []*entity.GetDistributeListInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDistributeInfos", ctx, req)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].([]*entity.GetDistributeListInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDistributeInfos indicates an expected call of GetDistributeInfos.
func (mr *MockDocumentLibraryReferenceMockRecorder) GetDistributeInfos(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDistributeInfos", reflect.TypeOf((*MockDocumentLibraryReference)(nil).GetDistributeInfos), ctx, req)
}

// GetUserIdsByFileIdAndFileFromAndFilePermission mocks base method.
func (m *MockDocumentLibraryReference) GetUserIdsByFileIdAndFileFromAndFilePermission(ctx context.Context, fileId string, fileFrom, filePermission int32) (*value.DocPermissionUsers, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserIdsByFileIdAndFileFromAndFilePermission", ctx, fileId, fileFrom, filePermission)
	ret0, _ := ret[0].(*value.DocPermissionUsers)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserIdsByFileIdAndFileFromAndFilePermission indicates an expected call of GetUserIdsByFileIdAndFileFromAndFilePermission.
func (mr *MockDocumentLibraryReferenceMockRecorder) GetUserIdsByFileIdAndFileFromAndFilePermission(ctx, fileId, fileFrom, filePermission interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserIdsByFileIdAndFileFromAndFilePermission", reflect.TypeOf((*MockDocumentLibraryReference)(nil).GetUserIdsByFileIdAndFileFromAndFilePermission), ctx, fileId, fileFrom, filePermission)
}

// SaveDisposalInfo mocks base method.
func (m *MockDocumentLibraryReference) SaveDisposalInfo(ctx context.Context, disposalApprovalRequest *entity.DisposalApprovalInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveDisposalInfo", ctx, disposalApprovalRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveDisposalInfo indicates an expected call of SaveDisposalInfo.
func (mr *MockDocumentLibraryReferenceMockRecorder) SaveDisposalInfo(ctx, disposalApprovalRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveDisposalInfo", reflect.TypeOf((*MockDocumentLibraryReference)(nil).SaveDisposalInfo), ctx, disposalApprovalRequest)
}

// SaveDistributeApprovalWorkflow mocks base method.
func (m *MockDocumentLibraryReference) SaveDistributeApprovalWorkflow(ctx context.Context, req *value.DistributeApprovalRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveDistributeApprovalWorkflow", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveDistributeApprovalWorkflow indicates an expected call of SaveDistributeApprovalWorkflow.
func (mr *MockDocumentLibraryReferenceMockRecorder) SaveDistributeApprovalWorkflow(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveDistributeApprovalWorkflow", reflect.TypeOf((*MockDocumentLibraryReference)(nil).SaveDistributeApprovalWorkflow), ctx, req)
}

// SaveRecycleInfo mocks base method.
func (m *MockDocumentLibraryReference) SaveRecycleInfo(ctx context.Context, recycleApprovalRequest *entity.RecycleApprovalInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveRecycleInfo", ctx, recycleApprovalRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveRecycleInfo indicates an expected call of SaveRecycleInfo.
func (mr *MockDocumentLibraryReferenceMockRecorder) SaveRecycleInfo(ctx, recycleApprovalRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveRecycleInfo", reflect.TypeOf((*MockDocumentLibraryReference)(nil).SaveRecycleInfo), ctx, recycleApprovalRequest)
}
