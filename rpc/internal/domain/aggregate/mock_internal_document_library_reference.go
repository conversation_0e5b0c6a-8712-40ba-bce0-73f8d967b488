// Code generated by MockGen. DO NOT EDIT.
// Source: internal_document_library_reference.go

// Package aggregate is a generated GoMock package.
package aggregate

import (
	context "context"
	entity "docvault/rpc/internal/domain/entity"
	value "docvault/rpc/internal/domain/value"
	mapper "docvault/rpc/internal/infrastructure/adapter/mapper"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockInternalDocumentLibraryReference is a mock of InternalDocumentLibraryReference interface.
type MockInternalDocumentLibraryReference struct {
	ctrl     *gomock.Controller
	recorder *MockInternalDocumentLibraryReferenceMockRecorder
}

// MockInternalDocumentLibraryReferenceMockRecorder is the mock recorder for MockInternalDocumentLibraryReference.
type MockInternalDocumentLibraryReferenceMockRecorder struct {
	mock *MockInternalDocumentLibraryReference
}

// NewMockInternalDocumentLibraryReference creates a new mock instance.
func NewMockInternalDocumentLibraryReference(ctrl *gomock.Controller) *MockInternalDocumentLibraryReference {
	mock := &MockInternalDocumentLibraryReference{ctrl: ctrl}
	mock.recorder = &MockInternalDocumentLibraryReferenceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInternalDocumentLibraryReference) EXPECT() *MockInternalDocumentLibraryReferenceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockInternalDocumentLibraryReference) Create(ctx context.Context, tx mapper.Transaction, internalDocument entity.InternalDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, tx, internalDocument)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockInternalDocumentLibraryReferenceMockRecorder) Create(ctx, tx, internalDocument interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockInternalDocumentLibraryReference)(nil).Create), ctx, tx, internalDocument)
}

// CreateHistory mocks base method.
func (m *MockInternalDocumentLibraryReference) CreateHistory(ctx context.Context, tx mapper.Transaction, historyVersion value.InternalDocumentHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHistory", ctx, tx, historyVersion)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateHistory indicates an expected call of CreateHistory.
func (mr *MockInternalDocumentLibraryReferenceMockRecorder) CreateHistory(ctx, tx, historyVersion interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHistory", reflect.TypeOf((*MockInternalDocumentLibraryReference)(nil).CreateHistory), ctx, tx, historyVersion)
}

// Get mocks base method.
func (m *MockInternalDocumentLibraryReference) Get(ctx context.Context, id string) (entity.InternalDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(entity.InternalDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockInternalDocumentLibraryReferenceMockRecorder) Get(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockInternalDocumentLibraryReference)(nil).Get), ctx, id)
}

// GetCountByNames mocks base method.
func (m *MockInternalDocumentLibraryReference) GetCountByNames(ctx context.Context, names []string, organizationID string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountByNames", ctx, names, organizationID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountByNames indicates an expected call of GetCountByNames.
func (mr *MockInternalDocumentLibraryReferenceMockRecorder) GetCountByNames(ctx, names, organizationID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountByNames", reflect.TypeOf((*MockInternalDocumentLibraryReference)(nil).GetCountByNames), ctx, names, organizationID)
}

// GetNoMaxAndLockByNoPrefix mocks base method.
func (m *MockInternalDocumentLibraryReference) GetNoMaxAndLockByNoPrefix(ctx context.Context, tx mapper.Transaction, noPrefix, organizationID string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoMaxAndLockByNoPrefix", ctx, tx, noPrefix, organizationID)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoMaxAndLockByNoPrefix indicates an expected call of GetNoMaxAndLockByNoPrefix.
func (mr *MockInternalDocumentLibraryReferenceMockRecorder) GetNoMaxAndLockByNoPrefix(ctx, tx, noPrefix, organizationID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoMaxAndLockByNoPrefix", reflect.TypeOf((*MockInternalDocumentLibraryReference)(nil).GetNoMaxAndLockByNoPrefix), ctx, tx, noPrefix, organizationID)
}

// Update mocks base method.
func (m *MockInternalDocumentLibraryReference) Update(ctx context.Context, tx mapper.Transaction, internalDocument entity.InternalDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, tx, internalDocument)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockInternalDocumentLibraryReferenceMockRecorder) Update(ctx, tx, internalDocument interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockInternalDocumentLibraryReference)(nil).Update), ctx, tx, internalDocument)
}
