package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/utils"
	"errors"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
)

type InternalDocumentLibrary struct {
	InternalDocumentLibraryReference
	BasicAbilityReference
}

func NewInternalDocumentLibrary(internalDocumentLibraryReference InternalDocumentLibraryReference, basicAbilityReference BasicAbilityReference) *InternalDocumentLibrary {
	return &InternalDocumentLibrary{
		InternalDocumentLibraryReference: internalDocumentLibraryReference,
		BasicAbilityReference:            basicAbilityReference,
	}
}

func (i *InternalDocumentLibrary) BatchCreate(ctx context.Context, internalDocumentCreateRequest []InternalDocumentCreateRequest) (internalDocumentInfos []InternalDocumentInfo, err error) {
	// 判断文件是否重复
	err = i.checkFileIsRepeat(ctx, internalDocumentCreateRequest)
	if err != nil {
		return nil, err
	}

	tx := i.BasicAbilityReference.StartTX(ctx)
	defer tx.AutoCommit(&err)
	for _, internalDocumentCreateRequest := range internalDocumentCreateRequest {
		internalDocument, err := i.buildInternalDocument(ctx, internalDocumentCreateRequest, tx)
		if err != nil {
			return nil, err
		}

		if err := i.InternalDocumentLibraryReference.Create(ctx, tx, internalDocument); err != nil {
			return nil, err
		}

		internalDocumentInfos = append(internalDocumentInfos, i.buildInternalDocumentInfo(internalDocument))
		historyVersion := i.buildHistoryVersion(ctx, internalDocument, value.OperatedTypeAdd)
		if err := i.InternalDocumentLibraryReference.CreateHistory(ctx, tx, historyVersion); err != nil {
			return nil, err
		}
	}
	return internalDocumentInfos, nil
}

func (i *InternalDocumentLibrary) Change(ctx context.Context, req InternalDocumentChangeRequest) error {
	e, err := i.InternalDocumentLibraryReference.Get(ctx, req.ID)
	if err != nil {
		logc.Error(ctx, "InternalDocumentLibrary.Change", "Get", err)
		return errors.New("")
	}

	if e.Name != req.Name {
		err = i.checkFileIsRepeat(ctx, []InternalDocumentCreateRequest{
			{
				Name: req.Name,
			},
		})
		if err != nil {
			return err
		}
	}

	historyVersion := i.buildHistoryVersion(ctx, e, value.OperatedTypeRevise)

	e.Name = req.Name
	e.FileID = req.FileID
	e.DocCategoryID = req.DocCategoryID
	e.DepartmentID = req.DepartmentID
	e.PublishDate = req.PublishDate
	e.EffectiveDate = req.EffectiveDate
	e.OriginalNo = req.OriginalNo
	e.OriginalVersionNo = req.OriginalVersionNo
	e.AuthorID = req.AuthorID
	e.Revise(utils.GetContextUserID(ctx))

	tx := i.BasicAbilityReference.StartTX(ctx)
	if req.NoPrefix != e.NoPrefix {
		// 查询前缀最大值
		maxNo, err := i.InternalDocumentLibraryReference.GetNoMaxAndLockByNoPrefix(ctx, tx, req.NoPrefix, req.OrganizationID)
		if err != nil {
			tx.Rollback()
			return err
		}
		e.SerialNo = maxNo + 1
		e.No = fmt.Sprintf("%s-%d", req.NoPrefix, e.SerialNo)
		e.NoPrefix = req.NoPrefix
	}

	if err := i.InternalDocumentLibraryReference.Update(ctx, tx, e); err != nil {
		tx.Rollback()
		return err
	}

	// 保存历史版本
	if err := i.InternalDocumentLibraryReference.CreateHistory(ctx, tx, historyVersion); err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit(); err != nil {
		return err
	}
	return nil
}

func (i *InternalDocumentLibrary) buildHistoryVersion(ctx context.Context, e entity.InternalDocument, operatedType value.OperatedType) value.InternalDocumentHistory {
	historyVersion := value.InternalDocumentHistory{}
	utils.StructCopy(ctx, &historyVersion, &e)
	historyVersion.ID = i.BasicAbilityReference.GenerateID()
	historyVersion.MainID = e.ID
	historyVersion.OperatedType = operatedType
	historyVersion.UpdatedAt = time.Now()
	historyVersion.UpdatedBy = utils.GetContextUserID(ctx)
	return historyVersion
}

func (i *InternalDocumentLibrary) buildInternalDocumentInfo(internalDocument entity.InternalDocument) InternalDocumentInfo {
	return InternalDocumentInfo{
		ID:                internalDocument.ID,
		Name:              internalDocument.Name,
		FileID:            internalDocument.FileID,
		DocCategoryID:     internalDocument.DocCategoryID,
		DepartmentID:      internalDocument.DepartmentID,
		PublishDate:       internalDocument.PublishDate,
		EffectiveDate:     internalDocument.EffectiveDate,
		OriginalNo:        internalDocument.OriginalNo,
		OriginalVersionNo: internalDocument.OriginalVersionNo,
		OrganizationID:    internalDocument.OrganizationID,
		AuthorID:          internalDocument.AuthorID,
		Status:            int(internalDocument.Status),
		NoPrefix:          internalDocument.NoPrefix,
		No:                internalDocument.SerialNo,
		VersionNo:         internalDocument.VersionNo,
	}
}

// 判断文件名是否重复
func (i *InternalDocumentLibrary) checkFileIsRepeat(ctx context.Context, internalDocumentCreateRequest []InternalDocumentCreateRequest) error {
	names := utils.ExtractSliceField(internalDocumentCreateRequest, func(v InternalDocumentCreateRequest) string {
		return v.Name
	})
	count, err := i.InternalDocumentLibraryReference.GetCountByNames(ctx, names, utils.GetContextOrganizationID(ctx))
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("文件名称已存在")
	}
	return nil
}

func (i *InternalDocumentLibrary) buildInternalDocument(ctx context.Context, internalDocumentCreateRequest InternalDocumentCreateRequest, tx mapper.Transaction) (entity.InternalDocument, error) {
	internalDocument := entity.InternalDocument{}
	if err := utils.StructDeepCopy(ctx, &internalDocument, internalDocumentCreateRequest); err != nil {
		return entity.InternalDocument{}, err
	}
	now := time.Now()
	internalDocument.ID = i.BasicAbilityReference.GenerateID()
	internalDocument.VersionNo = 0
	internalDocument.CreatedAt = now
	internalDocument.UpdatedAt = now
	internalDocument.CreatedBy = utils.GetContextUserID(ctx)
	internalDocument.UpdatedBy = utils.GetContextUserID(ctx)

	// 查询前缀最大值
	maxNo, err := i.InternalDocumentLibraryReference.GetNoMaxAndLockByNoPrefix(ctx, tx, internalDocument.NoPrefix, internalDocument.OrganizationID)
	if err != nil {
		tx.Rollback()
		return entity.InternalDocument{}, err
	}

	internalDocument.VersionNo = 1
	internalDocument.SerialNo = maxNo + 1
	internalDocument.AutoUpdateStatus()
	internalDocument.No = fmt.Sprintf("%s-%d", internalDocument.NoPrefix, internalDocument.SerialNo)
	internalDocument.TenantID = utils.GetContextTenantID(ctx)
	return internalDocument, nil
}
