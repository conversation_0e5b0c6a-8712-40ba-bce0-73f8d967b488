package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
)

//go:generate mockgen -source=internal_document_library_reference.go -destination=mock_internal_document_library_reference.go -package=aggregate
type InternalDocumentLibraryReference interface {
	Create(ctx context.Context, tx mapper.Transaction, internalDocument entity.InternalDocument) error
	Update(ctx context.Context, tx mapper.Transaction, internalDocument entity.InternalDocument) error
	Get(ctx context.Context, id string) (entity.InternalDocument, error)
	CreateHistory(ctx context.Context, tx mapper.Transaction, historyVersion value.InternalDocumentHistory) error
	GetNoMaxAndLockByNoPrefix(ctx context.Context, tx mapper.Transaction, noPrefix string, organizationID string) (int, error)
	GetCountByNames(ctx context.Context, names []string, organizationID string) (int64, error)
}
