package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/utils"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logc"
	"strconv"
	"strings"
	"time"
)

type ExternalDocumentLibrary struct {
	ExternalDocumentLibraryReference
	BasicAbilityReference
}

func NewExternalDocumentLibrary(externalDocumentLibraryReference ExternalDocumentLibraryReference, basicAbilityReference BasicAbilityReference) *ExternalDocumentLibrary {
	return &ExternalDocumentLibrary{
		ExternalDocumentLibraryReference: externalDocumentLibraryReference,
		BasicAbilityReference:            basicAbilityReference,
	}
}

// 批量添加
func (e *ExternalDocumentLibrary) BatchCreate(ctx context.Context, externalDocument []ExternalDocumentCreateRequest) (externalDocumentInfos []ExternalDocumentInfo, err error) {
	if len(externalDocument) == 0 {
		return nil, nil
	}
	userId := utils.GetContextUserID(ctx)

	orgId := externalDocument[0].OrgID
	var names []string
	for _, v := range externalDocument {
		names = append(names, v.Name)
	}
	isRepetitionDoc, err := e.isRepetitionDoc(ctx, names, orgId)
	if err != nil {
		return nil, err
	}
	if isRepetitionDoc {
		return nil, errors.New("存在重复的文档名称")
	}

	// 查询最大文档编号
	maxNumberPostfixMap, err := e.getMaxNumberMap(ctx, externalDocument)
	if err != nil {
		return nil, err
	}

	// 数据库信息和返回信息组装
	externalDocuments, externalDocumentInfos := e.infoPackaging(externalDocument, maxNumberPostfixMap, userId)

	// 保存到数据库
	err = e.Create(ctx, externalDocuments)
	if err != nil {
		return nil, err
	}

	return externalDocumentInfos, nil
}

func (e *ExternalDocumentLibrary) infoPackaging(externalDocument []ExternalDocumentCreateRequest, maxNumberPostfixMap map[string]int, userId string) (externalDocuments []entity.ExternalDocument, externalDocumentInfos []ExternalDocumentInfo) {
	orgId := externalDocument[0].OrgID
	// 设置版本
	version := "A/0"
	number := ""

	// 组装需要新增的数据
	for _, v := range externalDocument {
		// 设置编号
		numberPostfix := 1
		if maxNumberPostfix, ok := maxNumberPostfixMap[v.TypeDictionaryNodeId]; !ok {
			newNum := fmt.Sprintf("%d", numberPostfix)
			number = v.NumberPrefix + newNum
		} else {
			numberPostfix = maxNumberPostfix + 1
			newNum := fmt.Sprintf("%d", numberPostfix)
			number = v.NumberPrefix + newNum
		}
		maxNumberPostfixMap[v.TypeDictionaryNodeId] = numberPostfix

		status := 2
		if time.Now().UnixMilli() >= v.EffectiveDate {
			status = 3
		}

		// 组装保存信息
		id := e.GenerateID()
		externalDocuments = append(externalDocuments, entity.ExternalDocument{
			ID:                              id,
			Number:                          number,
			Version:                         version,
			NumberPostfix:                   numberPostfix,
			OriginalNumber:                  v.OriginalNumber,
			OriginalVersion:                 v.OriginalVersion,
			Name:                            v.Name,
			DocType:                         v.DocType,
			Domain:                          v.Domain,
			OriginalDocNumber:               v.OriginalDocNumber,
			PublishDocNumber:                v.PublishDocNumber,
			PublishDepartment:               v.PublishDepartment,
			PublishDate:                     time.UnixMilli(v.PublishDate),
			EffectiveDate:                   time.UnixMilli(v.EffectiveDate),
			Authentications:                 v.Authentications,
			Status:                          status,
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
			FileID:                          v.FileID,
			OrganizationID:                  orgId,
			CreatedBy:                       userId,
			UpdatedBy:                       userId,
		})

		// 组装返回信息
		externalDocumentInfos = append(externalDocumentInfos, ExternalDocumentInfo{
			ID:                              id,
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
		})
	}
	return externalDocuments, externalDocumentInfos
}

func (e *ExternalDocumentLibrary) getMaxNumberMap(ctx context.Context, externalDocument []ExternalDocumentCreateRequest) (map[string]int, error) {
	if len(externalDocument) == 0 {
		return nil, nil
	}
	orgId := externalDocument[0].OrgID
	var typeDictNodeIds []string
	for _, v := range externalDocument {
		typeDictNodeIds = append(typeDictNodeIds, v.TypeDictionaryNodeId)
	}
	maxNumberPostfixInfos, err := e.BatchGetMaxNumberPostfix(ctx, orgId, typeDictNodeIds)
	if err != nil {
		return nil, err
	}
	// map将maxNumber和DictNodeId绑定
	maxNumberPostfixMap := make(map[string]int)
	for _, v := range maxNumberPostfixInfos {
		maxNumberPostfixMap[v.DictNodeId] = v.NumberPostfix
	}
	return maxNumberPostfixMap, nil
}

// 查询列表
func (e *ExternalDocumentLibrary) GetList(ctx context.Context, req *ExternalDocumentPageReq) (externalDocumentPageResp *ExternalDocumentPageResp, err error) {
	externalDocumentPageInfo, err := e.GetExternalDocList(ctx, &value.ExternalDocumentPage{
		Page:                           req.Page,
		PageSize:                       req.PageSize,
		NoPage:                         req.NoPage,
		Number:                         req.Number,
		Name:                           req.Name,
		OriginalNumber:                 req.OriginalNumber,
		OriginalDocNumber:              req.OriginalDocNumber,
		PublishDocNumber:               req.PublishDocNumber,
		PublishDepartment:              req.PublishDepartment,
		TypeDictionaryNodeIds:          req.TypeDictionaryNodeIds,
		DomainDictionaryNodeId:         req.DomainDictionaryNodeId,
		AuthenticationDictionaryNodeId: req.AuthenticationDictionaryNodeId,
		BeAttachedFile:                 req.BeAttachedFile,
		Status:                         req.Status,
		OrgID:                          req.OrgID,
	})
	if err != nil {
		return nil, err
	}
	externalDocumentPageResp = &ExternalDocumentPageResp{}
	externalDocumentPageList := e.valueToResp(externalDocumentPageInfo.Data)
	externalDocumentPageResp.Total = externalDocumentPageInfo.Total
	externalDocumentPageResp.Data = externalDocumentPageList
	return externalDocumentPageResp, nil
}

func (e *ExternalDocumentLibrary) valueToResp(externalDocumentList []value.ExternalDocumentList) (externalDocumentPageList []ExternalDocumentPageList) {
	for _, v := range externalDocumentList {
		externalDocumentPageList = append(externalDocumentPageList, ExternalDocumentPageList{
			ID:                              v.ID,
			Number:                          v.Number,
			Version:                         v.Version,
			OriginalNumber:                  v.OriginalNumber,
			OriginalVersion:                 v.OriginalVersion,
			Name:                            v.Name,
			DocType:                         v.DocType,
			Domain:                          v.Domain,
			OriginalDocNumber:               v.OriginalDocNumber,
			PublishDocNumber:                v.PublishDocNumber,
			PublishDepartment:               v.PublishDepartment,
			PublishDate:                     v.PublishDate,
			EffectiveDate:                   v.EffectiveDate,
			Authentications:                 v.Authentications,
			Status:                          v.Status,
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
			FileID:                          v.FileID,
		})
	}
	return externalDocumentPageList
}

// 修改
func (e *ExternalDocumentLibrary) Change(ctx context.Context, externalDocument ExternalDocumentChangeRequest) (err error) {
	userID := utils.GetContextUserID(ctx)
	// 查询是否存在
	externalDocumentInfo, err := e.GetExternalDocumentById(ctx, externalDocument.ID)
	if err != nil {
		return err
	}
	if externalDocumentInfo == nil {
		return errors.New("external document not found")
	}
	// 查询修改是否重复
	docInfo, err := e.GetExternalDocByOrgIDAndName(ctx, externalDocument.ExternalDocumentCreateRequest.OrgID, externalDocument.ExternalDocumentCreateRequest.Name)
	if err != nil {
		return err
	}
	if docInfo != nil && docInfo.ID != externalDocument.ID {
		return errors.New("文件名称已存在")
	}
	// 设置编号
	number, err := e.getNumber(ctx, externalDocument, externalDocumentInfo)
	if err != nil {
		return err
	}
	// 设置版本
	version := e.getVersion(externalDocumentInfo.Version)
	if version == "" {
		return errors.New("版本号格式错误")
	}

	status := 2
	if time.Now().UnixMilli() >= externalDocument.ExternalDocumentCreateRequest.EffectiveDate {
		status = 3
	}

	err = e.ChangeExternalDocument(ctx, entity.ExternalDocument{
		ID:                              externalDocument.ID,
		Number:                          number,
		Version:                         version,
		OriginalNumber:                  externalDocument.ExternalDocumentCreateRequest.OriginalNumber,
		OriginalVersion:                 externalDocument.ExternalDocumentCreateRequest.OriginalVersion,
		Name:                            externalDocument.ExternalDocumentCreateRequest.Name,
		DocType:                         externalDocument.ExternalDocumentCreateRequest.DocType,
		Domain:                          externalDocument.ExternalDocumentCreateRequest.Domain,
		OriginalDocNumber:               externalDocument.ExternalDocumentCreateRequest.OriginalDocNumber,
		PublishDocNumber:                externalDocument.ExternalDocumentCreateRequest.PublishDocNumber,
		PublishDepartment:               externalDocument.ExternalDocumentCreateRequest.PublishDepartment,
		PublishDate:                     time.UnixMilli(externalDocument.ExternalDocumentCreateRequest.PublishDate),
		EffectiveDate:                   time.UnixMilli(externalDocument.ExternalDocumentCreateRequest.EffectiveDate),
		Authentications:                 externalDocument.ExternalDocumentCreateRequest.Authentications,
		AuthenticationDictionaryNodeIds: externalDocument.ExternalDocumentCreateRequest.AuthenticationDictionaryNodeIds,
		TypeDictionaryNodeId:            externalDocument.ExternalDocumentCreateRequest.TypeDictionaryNodeId,
		DomainDictionaryNodeId:          externalDocument.ExternalDocumentCreateRequest.DomainDictionaryNodeId,
		FileID:                          externalDocument.ExternalDocumentCreateRequest.FileID,
		UpdatedBy:                       userID,
		Status:                          status,
	})
	if err != nil {
		return err
	}

	return nil
}

func (e *ExternalDocumentLibrary) getVersion(version string) string {
	// 分割字母和数字部分
	var letter string
	var numberStr string
	_, err := fmt.Sscanf(version, "%1s/%s", &letter, &numberStr)
	if err != nil {
		return ""
	}

	// 解析数字部分
	number, err := strconv.Atoi(numberStr)
	if err != nil {
		return ""
	}

	// 如果字母不是 'Z'
	if letter != "Z" {
		// 数字加1
		number++
		// 如果数字超过9，字母加1，数字重置为0
		if number > 9 {
			// 字母递增（A-Z）
			nextLetter := string(rune(letter[0]) + 1)
			letter = nextLetter
			number = 0
		}
	} else {
		// 如果字母是 'Z'，直接数字加1
		number++
	}

	// 返回新的版本号
	return fmt.Sprintf("%s/%d", letter, number)
}

func (e *ExternalDocumentLibrary) getNumber(ctx context.Context, externalDocument ExternalDocumentChangeRequest, externalDocumentInfo *entity.ExternalDocument) (string, error) {
	number := ""
	if externalDocument.ExternalDocumentCreateRequest.TypeDictionaryNodeId != externalDocumentInfo.TypeDictionaryNodeId {
		// 查询最大文档编号
		maxNumberPostfix, err := e.GetMaxNumberPostfix(ctx, externalDocumentInfo.OrganizationID, externalDocument.ExternalDocumentCreateRequest.TypeDictionaryNodeId)
		if err != nil {
			return "", err
		}
		newNum := maxNumberPostfix + 1
		number = fmt.Sprintf("%s%d", externalDocument.ExternalDocumentCreateRequest.NumberPrefix, newNum)
	} else {
		number = externalDocumentInfo.Number
	}

	return number, nil
}

func (e *ExternalDocumentLibrary) isRepetitionDoc(ctx context.Context, names []string, orgID string) (bool, error) {
	externalDocNames, err := e.GetExternalDocNamesByOrgID(ctx, orgID)
	if err != nil {
		return false, err
	}
	existingNameMap := make(map[string]bool)
	for _, v := range externalDocNames {
		existingNameMap[v] = true
	}
	for _, name := range names {
		if existingNameMap[name] {
			return true, nil
		}
	}
	return false, nil
}

func (e *ExternalDocumentLibrary) ImportCompanyPlagiarismCheck(ctx context.Context, req *ImportCompanyPlagiarismCheckRequest) (err error) {
	orgID := utils.GetContextOrganizationID(ctx)
	// 获取文档信息,判断是否已存在
	documents, err := e.GetExternalDocsByIds(ctx, req.Ids)
	if err != nil {
		logc.Error(ctx, "获取文档信息失败：", err)
		return err
	}
	var names []string
	for _, v := range documents {
		names = append(names, v.Name)
	}
	docs, err := e.GetExternalDocsByOrgIDAndNames(ctx, orgID, names)
	if err != nil {
		logc.Error(ctx, "获取文档信息失败：", err)
		return err
	}
	if len(docs) > 0 {
		return errors.New("文件已纳入，无法纳入")
	}
	return nil
}

func (e *ExternalDocumentLibrary) ImportGroupDocsToCompany(ctx context.Context, req *ImportGroupDocsToCompanyRequest) (err error) {
	userLoginInfo := utils.GetCurrentLoginUser(ctx)
	// 获取文档信息
	var ids []string
	for _, v := range req.Data {
		ids = append(ids, v.ID)
	}
	docInfos, err := e.GetExternalDocsByIds(ctx, ids)
	if err != nil {
		logc.Error(ctx, "获取文档信息失败：", err)
		return err
	}
	// 获取文档编号
	documents, err := e.getDocsNumber(ctx, docInfos, userLoginInfo.OrganizationId, req.OrgCode)
	if err != nil {
		return err
	}
	// 组装信息
	documentInfos := e.assemblyInfo(req.Data, documents, userLoginInfo.OrganizationId, userLoginInfo.UserId)
	// 批量保存
	err = e.Create(ctx, documentInfos)
	if err != nil {
		logc.Error(ctx, "批量保存文档信息失败：", err)
		return err
	}
	return nil
}

func (e *ExternalDocumentLibrary) assemblyInfo(data []ImportGroupDocsToCompanyInfo, docInfos []entity.ExternalDocument, orgID, userID string) []entity.ExternalDocument {
	dataMap := make(map[string]ImportGroupDocsToCompanyInfo)
	for _, v := range data {
		dataMap[v.ID] = v
	}
	var documentInfos []entity.ExternalDocument
	for _, v := range docInfos {
		v.OriginalNumber = dataMap[v.ID].OriginalNumber
		v.OriginalVersion = dataMap[v.ID].OriginalVersion
		v.OrganizationID = orgID
		v.Version = "A/0"
		v.CreatedBy = userID
		v.UpdatedBy = userID
		v.ID = e.GenerateID()
		documentInfos = append(documentInfos, v)
	}
	return documentInfos
}

func (e *ExternalDocumentLibrary) getDocsNumber(ctx context.Context, docInfos []entity.ExternalDocument, orgID, orgCode string) ([]entity.ExternalDocument, error) {
	var typeDictNodeIds []string
	for _, v := range docInfos {
		typeDictNodeIds = append(typeDictNodeIds, v.TypeDictionaryNodeId)
	}
	postfixes, err := e.BatchGetMaxNumberPostfix(ctx, orgID, typeDictNodeIds)
	if err != nil {
		return nil, err
	}
	postfixMap := make(map[string]int)
	for _, v := range postfixes {
		postfixMap[v.DictNodeId] = v.NumberPostfix
	}
	var documents []entity.ExternalDocument
	for _, v := range docInfos {
		if strings.Contains(v.Number, "/") && strings.Contains(v.Number, "-") {
			slashIndex := strings.Index(v.Number, "/")
			lastDashIndex := strings.LastIndex(v.Number, "-")
			if lastDashIndex > slashIndex {
				// 截取 "/" 之后到最后一个 "-" 之前的部分(包含"/" 和 "-")
				v.Number = orgCode + v.Number[slashIndex:lastDashIndex+1]
			}
		} else {
			v.Number = orgCode + "/"
		}

		if postfix, ok := postfixMap[v.TypeDictionaryNodeId]; ok {
			v.Number = fmt.Sprintf("%s%d", v.Number, postfix+1)
			v.NumberPostfix = postfix + 1
			postfixMap[v.TypeDictionaryNodeId] = postfix + 1
		} else {
			v.Number += "1"
			v.NumberPostfix = 1
			postfixMap[v.TypeDictionaryNodeId] = 1
		}
		documents = append(documents, v)
	}
	return documents, nil
}
