// Code generated by MockGen. DO NOT EDIT.
// Source: external_document_library_reference.go

// Package aggregate is a generated GoMock package.
package aggregate

import (
	context "context"
	entity "docvault/rpc/internal/domain/entity"
	value "docvault/rpc/internal/domain/value"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockExternalDocumentLibraryReference is a mock of ExternalDocumentLibraryReference interface.
type MockExternalDocumentLibraryReference struct {
	ctrl     *gomock.Controller
	recorder *MockExternalDocumentLibraryReferenceMockRecorder
}

// MockExternalDocumentLibraryReferenceMockRecorder is the mock recorder for MockExternalDocumentLibraryReference.
type MockExternalDocumentLibraryReferenceMockRecorder struct {
	mock *MockExternalDocumentLibraryReference
}

// NewMockExternalDocumentLibraryReference creates a new mock instance.
func NewMockExternalDocumentLibraryReference(ctrl *gomock.Controller) *MockExternalDocumentLibraryReference {
	mock := &MockExternalDocumentLibraryReference{ctrl: ctrl}
	mock.recorder = &MockExternalDocumentLibraryReferenceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExternalDocumentLibraryReference) EXPECT() *MockExternalDocumentLibraryReferenceMockRecorder {
	return m.recorder
}

// BatchGetMaxNumberPostfix mocks base method.
func (m *MockExternalDocumentLibraryReference) BatchGetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds []string) ([]value.DictNodeIdAndNumberPostfix, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMaxNumberPostfix", ctx, orgID, TypeDictionaryNodeIds)
	ret0, _ := ret[0].([]value.DictNodeIdAndNumberPostfix)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMaxNumberPostfix indicates an expected call of BatchGetMaxNumberPostfix.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) BatchGetMaxNumberPostfix(ctx, orgID, TypeDictionaryNodeIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMaxNumberPostfix", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).BatchGetMaxNumberPostfix), ctx, orgID, TypeDictionaryNodeIds)
}

// ChangeExternalDocument mocks base method.
func (m *MockExternalDocumentLibraryReference) ChangeExternalDocument(ctx context.Context, externalDocument entity.ExternalDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeExternalDocument", ctx, externalDocument)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChangeExternalDocument indicates an expected call of ChangeExternalDocument.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) ChangeExternalDocument(ctx, externalDocument interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeExternalDocument", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).ChangeExternalDocument), ctx, externalDocument)
}

// Create mocks base method.
func (m *MockExternalDocumentLibraryReference) Create(ctx context.Context, externalDocuments []entity.ExternalDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, externalDocuments)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) Create(ctx, externalDocuments interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).Create), ctx, externalDocuments)
}

// GetExternalDocByOrgIDAndName mocks base method.
func (m *MockExternalDocumentLibraryReference) GetExternalDocByOrgIDAndName(ctx context.Context, orgID, name string) (*entity.ExternalDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExternalDocByOrgIDAndName", ctx, orgID, name)
	ret0, _ := ret[0].(*entity.ExternalDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExternalDocByOrgIDAndName indicates an expected call of GetExternalDocByOrgIDAndName.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetExternalDocByOrgIDAndName(ctx, orgID, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExternalDocByOrgIDAndName", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetExternalDocByOrgIDAndName), ctx, orgID, name)
}

// GetExternalDocList mocks base method.
func (m *MockExternalDocumentLibraryReference) GetExternalDocList(ctx context.Context, req *value.ExternalDocumentPage) (*value.ExternalDocumentPageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExternalDocList", ctx, req)
	ret0, _ := ret[0].(*value.ExternalDocumentPageInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExternalDocList indicates an expected call of GetExternalDocList.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetExternalDocList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExternalDocList", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetExternalDocList), ctx, req)
}

// GetExternalDocNamesByOrgID mocks base method.
func (m *MockExternalDocumentLibraryReference) GetExternalDocNamesByOrgID(ctx context.Context, orgID string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExternalDocNamesByOrgID", ctx, orgID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExternalDocNamesByOrgID indicates an expected call of GetExternalDocNamesByOrgID.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetExternalDocNamesByOrgID(ctx, orgID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExternalDocNamesByOrgID", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetExternalDocNamesByOrgID), ctx, orgID)
}

// GetExternalDocsByIds mocks base method.
func (m *MockExternalDocumentLibraryReference) GetExternalDocsByIds(ctx context.Context, ids []string) ([]entity.ExternalDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExternalDocsByIds", ctx, ids)
	ret0, _ := ret[0].([]entity.ExternalDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExternalDocsByIds indicates an expected call of GetExternalDocsByIds.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetExternalDocsByIds(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExternalDocsByIds", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetExternalDocsByIds), ctx, ids)
}

// GetExternalDocsByOrgIDAndNames mocks base method.
func (m *MockExternalDocumentLibraryReference) GetExternalDocsByOrgIDAndNames(ctx context.Context, orgID string, names []string) ([]entity.ExternalDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExternalDocsByOrgIDAndNames", ctx, orgID, names)
	ret0, _ := ret[0].([]entity.ExternalDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExternalDocsByOrgIDAndNames indicates an expected call of GetExternalDocsByOrgIDAndNames.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetExternalDocsByOrgIDAndNames(ctx, orgID, names interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExternalDocsByOrgIDAndNames", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetExternalDocsByOrgIDAndNames), ctx, orgID, names)
}

// GetExternalDocumentById mocks base method.
func (m *MockExternalDocumentLibraryReference) GetExternalDocumentById(ctx context.Context, id string) (*entity.ExternalDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExternalDocumentById", ctx, id)
	ret0, _ := ret[0].(*entity.ExternalDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExternalDocumentById indicates an expected call of GetExternalDocumentById.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetExternalDocumentById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExternalDocumentById", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetExternalDocumentById), ctx, id)
}

// GetMaxNumberPostfix mocks base method.
func (m *MockExternalDocumentLibraryReference) GetMaxNumberPostfix(ctx context.Context, orgID, TypeDictionaryNodeIds string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxNumberPostfix", ctx, orgID, TypeDictionaryNodeIds)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxNumberPostfix indicates an expected call of GetMaxNumberPostfix.
func (mr *MockExternalDocumentLibraryReferenceMockRecorder) GetMaxNumberPostfix(ctx, orgID, TypeDictionaryNodeIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxNumberPostfix", reflect.TypeOf((*MockExternalDocumentLibraryReference)(nil).GetMaxNumberPostfix), ctx, orgID, TypeDictionaryNodeIds)
}
