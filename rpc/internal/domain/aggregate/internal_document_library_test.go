package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/utils"
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
)

// TestInternalDocumentLibrary_BatchCreate 测试批量创建内部文档。
func TestInternalDocumentLibrary_BatchCreate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockInternalRef := NewMockInternalDocumentLibraryReference(ctrl)
	mockBasicRef := NewMockBasicAbilityReference(ctrl)

	ctx := context.Background()
	ctx = context.WithValue(ctx, "OrganizationId", "org123")
	ctx = context.WithValue(ctx, "UserId", "user123")
	ctx = context.WithValue(ctx, "TenantId", "tenant123")
	tx := mapper.NewMockTransaction(ctrl)

	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(utils.GetContextOrganizationID, func(ctx context.Context) string { return "org123" })
	patches.ApplyFunc(utils.GetContextUserID, func(ctx context.Context) string { return "user123" })
	patches.ApplyFunc(utils.GetContextTenantID, func(ctx context.Context) string { return "tenant123" })

	service := NewInternalDocumentLibrary(mockInternalRef, mockBasicRef)

	createRequests := []InternalDocumentCreateRequest{
		{Name: "doc1", NoPrefix: "DP", OrganizationID: "org123"},
		{Name: "doc2", NoPrefix: "DP", OrganizationID: "org123"},
	}

	Convey("Test BatchCreate", t, func() {
		Convey("Success", func() {
			// 模拟：检查文档名称是否重复，返回0表示不重复
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), []string{"doc1", "doc2"}, "org123").Return(int64(0), nil)
			// 模拟：开始事务
			mockBasicRef.EXPECT().StartTX(gomock.Any()).Return(tx)
			// 模拟：事务的AutoCommit方法
			tx.EXPECT().AutoCommit(gomock.Any()).AnyTimes()

			// 模拟：为第一个文档生成ID、获取并锁定编号、创建文档、生成历史记录ID、创建历史记录
			mockBasicRef.EXPECT().GenerateID().Return("new-id-1")
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(0, nil)
			mockInternalRef.EXPECT().Create(gomock.Any(), tx, gomock.Any()).Return(nil)
			mockBasicRef.EXPECT().GenerateID().Return("history-id-1")
			mockInternalRef.EXPECT().CreateHistory(gomock.Any(), tx, gomock.Any()).Return(nil)

			// 模拟：为第二个文档生成ID、获取并锁定编号、创建文档、生成历史记录ID、创建历史记录
			mockBasicRef.EXPECT().GenerateID().Return("new-id-2")
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
			mockInternalRef.EXPECT().Create(gomock.Any(), tx, gomock.Any()).Return(nil)
			mockBasicRef.EXPECT().GenerateID().Return("history-id-2")
			mockInternalRef.EXPECT().CreateHistory(gomock.Any(), tx, gomock.Any()).Return(nil)

			// 调用被测方法
			infos, err := service.BatchCreate(ctx, createRequests)
			// 断言结果
			So(err, ShouldBeNil)
			So(infos, ShouldHaveLength, 2)
			So(infos[0].ID, ShouldEqual, "new-id-1")
			So(infos[1].ID, ShouldEqual, "new-id-2")
		})

		Convey("Error - File is repeat", func() {
			// 模拟：检查文档名称是否重复，返回1表示重复
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)
			_, err := service.BatchCreate(ctx, createRequests)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "文件名称已存在")
		})

		Convey("Error - GetCountByNames fails", func() {
			// 模拟：检查文档名称时数据库出错
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), errors.New("db error"))
			_, err := service.BatchCreate(ctx, createRequests)
			So(err, ShouldNotBeNil)
		})

		Convey("Error - GetNoMaxAndLockByNoPrefix fails", func() {
			// 模拟：获取并锁定编号时出错
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)
			mockBasicRef.EXPECT().StartTX(gomock.Any()).Return(tx)
			// 模拟：事务的Rollback方法
			tx.EXPECT().Rollback().Return(nil)

			mockBasicRef.EXPECT().GenerateID().Return("new-id-1")
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(0, errors.New("lock error"))

			_, err := service.BatchCreate(ctx, createRequests)
			So(err, ShouldNotBeNil)
		})
	})
}

// TestInternalDocumentLibrary_Change 测试更改内部文档。
func TestInternalDocumentLibrary_Change(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockInternalRef := NewMockInternalDocumentLibraryReference(ctrl)
	mockBasicRef := NewMockBasicAbilityReference(ctrl)
	ctx := context.Background()
	ctx = context.WithValue(ctx, "UserId", "user456")
	ctx = context.WithValue(ctx, "OrganizationId", "org123")

	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(utils.GetContextUserID, func(ctx context.Context) string { return "user456" })
	patches.ApplyFunc(utils.GetContextOrganizationID, func(ctx context.Context) string { return "org123" })
	tx := mapper.NewMockTransaction(ctrl)
	service := NewInternalDocumentLibrary(mockInternalRef, mockBasicRef)

	req := InternalDocumentChangeRequest{
		ID:             "doc-id-1",
		Name:           "new name",
		NoPrefix:       "NEW-DP",
		OrganizationID: "org123",
	}

	existingDoc := entity.InternalDocument{
		ID:             "doc-id-1",
		Name:           "old name",
		NoPrefix:       "OLD-DP",
		OrganizationID: "org123",
		UpdatedBy:      "user123",
	}

	Convey("Test Change", t, func() {
		Convey("Success - NoPrefix changed", func() {
			// 模拟：获取现有文档、生成历史记录ID、开始事务、获取并锁定新编号、更新文档、创建历史记录、提交事务
			mockInternalRef.EXPECT().Get(gomock.Any(), req.ID).Return(existingDoc, nil)
			// 修复：添加名称重复检查的 mock 期望
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), []string{"new name"}, "org123").Return(int64(0), nil)
			mockBasicRef.EXPECT().GenerateID().Return("history-id-1")
			mockBasicRef.EXPECT().StartTX(gomock.Any()).Return(tx)
			// 模拟：事务的Commit方法
			tx.EXPECT().Commit().Return(nil)
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(99, nil)
			mockInternalRef.EXPECT().Update(gomock.Any(), tx, gomock.Any()).DoAndReturn(func(ctx context.Context, tx mapper.Transaction, e entity.InternalDocument) error {
				// 断言更新后的文档属性
				So(e.Name, ShouldEqual, "new name")
				So(e.NoPrefix, ShouldEqual, "NEW-DP")
				So(e.SerialNo, ShouldEqual, 100)
				So(e.No, ShouldEqual, "NEW-DP-100")
				So(e.UpdatedBy, ShouldEqual, "user456")
				return nil
			})
			mockInternalRef.EXPECT().CreateHistory(gomock.Any(), tx, gomock.Any()).Return(nil)

			err := service.Change(ctx, req)
			So(err, ShouldBeNil)
		})

		Convey("Error - Get fails", func() {
			// 模拟：获取文档失败
			mockInternalRef.EXPECT().Get(gomock.Any(), req.ID).Return(entity.InternalDocument{}, errors.New("get error"))
			err := service.Change(ctx, req)
			So(err, ShouldNotBeNil)
		})

		Convey("Error - Commit fails", func() {
			// 模拟：事务提交失败
			mockInternalRef.EXPECT().Get(gomock.Any(), req.ID).Return(existingDoc, nil)
			// 修复：添加名称重复检查的 mock 期望
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), []string{"new name"}, "org123").Return(int64(0), nil)
			mockBasicRef.EXPECT().GenerateID().Return("history-id-6")
			mockBasicRef.EXPECT().StartTX(gomock.Any()).Return(tx)
			// 模拟：事务的Commit方法（会失败）
			tx.EXPECT().Commit().Return(errors.New("commit error"))
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(99, nil)
			mockInternalRef.EXPECT().Update(gomock.Any(), tx, gomock.Any()).Return(nil)
			mockInternalRef.EXPECT().CreateHistory(gomock.Any(), tx, gomock.Any()).Return(nil)

			err := service.Change(ctx, req)
			So(err, ShouldNotBeNil)
		})
	})
}

// TestInternalDocumentLibrary_buildHistoryVersion 测试构建历史版本记录。
func TestInternalDocumentLibrary_buildHistoryVersion(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockBasicRef := NewMockBasicAbilityReference(ctrl)
	service := NewInternalDocumentLibrary(nil, mockBasicRef)

	ctx := context.Background()
	ctx = context.WithValue(ctx, "UserId", "user123")

	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(utils.GetContextUserID, func(ctx context.Context) string { return "user123" })

	doc := entity.InternalDocument{
		ID:   "doc1",
		Name: "Test Doc",
	}

	Convey("Test buildHistoryVersion", t, func() {
		mockBasicRef.EXPECT().GenerateID().Return("history-id")

		history := service.buildHistoryVersion(ctx, doc, value.OperatedTypeAdd)

		// 断言历史记录的属性
		So(history.ID, ShouldEqual, "history-id")
		So(history.MainID, ShouldEqual, "doc1")
		So(history.Name, ShouldEqual, "Test Doc")
		So(history.OperatedType, ShouldEqual, value.OperatedTypeAdd)
		So(history.UpdatedBy, ShouldEqual, "user123")
		So(history.UpdatedAt, ShouldNotBeZeroValue)
	})
}

// TestInternalDocumentLibrary_buildInternalDocumentInfo 测试构建内部文档信息DTO。
func TestInternalDocumentLibrary_buildInternalDocumentInfo(t *testing.T) {
	service := NewInternalDocumentLibrary(nil, nil)
	doc := entity.InternalDocument{
		ID:     "doc1",
		Name:   "Test Doc",
		Status: 1,
	}
	Convey("Test buildInternalDocumentInfo", t, func() {
		info := service.buildInternalDocumentInfo(doc)
		// 断言DTO的属性
		So(info.ID, ShouldEqual, "doc1")
		So(info.Name, ShouldEqual, "Test Doc")
		So(info.Status, ShouldEqual, 1)
	})
}

// TestInternalDocumentLibrary_checkFileIsRepeat 测试检查文件是否重复。
func TestInternalDocumentLibrary_checkFileIsRepeat(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockInternalRef := NewMockInternalDocumentLibraryReference(ctrl)
	service := NewInternalDocumentLibrary(mockInternalRef, nil)

	ctx := context.Background()
	ctx = context.WithValue(ctx, "OrganizationId", "org123")

	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(utils.GetContextOrganizationID, func(ctx context.Context) string {
		return "org123"
	})

	reqs := []InternalDocumentCreateRequest{{Name: "doc1"}, {Name: "doc2"}}

	Convey("Test checkFileIsRepeat", t, func() {
		Convey("No repeat", func() {
			// 模拟：不重复
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), []string{"doc1", "doc2"}, "org123").Return(int64(0), nil)
			err := service.checkFileIsRepeat(ctx, reqs)
			So(err, ShouldBeNil)
		})

		Convey("Repeat", func() {
			// 模拟：重复
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), []string{"doc1", "doc2"}, "org123").Return(int64(1), nil)
			err := service.checkFileIsRepeat(ctx, reqs)
			So(err, ShouldNotBeNil)
		})

		Convey("DB Error", func() {
			// 模拟：数据库错误
			mockInternalRef.EXPECT().GetCountByNames(gomock.Any(), []string{"doc1", "doc2"}, "org123").Return(int64(0), errors.New("db error"))
			err := service.checkFileIsRepeat(ctx, reqs)
			So(err, ShouldNotBeNil)
		})
	})
}

// TestInternalDocumentLibrary_buildInternalDocument 测试构建内部文档实体。
func TestInternalDocumentLibrary_buildInternalDocument(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockInternalRef := NewMockInternalDocumentLibraryReference(ctrl)
	mockBasicRef := NewMockBasicAbilityReference(ctrl)

	service := NewInternalDocumentLibrary(mockInternalRef, mockBasicRef)

	ctx := context.Background()
	ctx = context.WithValue(ctx, "UserId", "user123")
	ctx = context.WithValue(ctx, "TenantId", "tenant123")
	tx := mapper.NewMockTransaction(ctrl)
	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(utils.GetContextUserID, func(ctx context.Context) string {
		return "user123"
	})
	patches.ApplyFunc(utils.GetContextTenantID, func(ctx context.Context) string {
		return "tenant123"
	})

	req := InternalDocumentCreateRequest{
		Name:           "Test Doc",
		NoPrefix:       "PRE",
		OrganizationID: "org123",
	}

	Convey("Test buildInternalDocument", t, func() {
		Convey("Success", func() {
			// 模拟：生成ID、获取并锁定编号
			mockBasicRef.EXPECT().GenerateID().Return("new-doc-id")
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(41, nil)

			doc, err := service.buildInternalDocument(ctx, req, tx)
			So(err, ShouldBeNil)
			// 断言文档实体的属性
			So(doc.ID, ShouldEqual, "new-doc-id")
			So(doc.Name, ShouldEqual, "Test Doc")
			So(doc.SerialNo, ShouldEqual, 42)
			So(doc.No, ShouldEqual, "PRE-42")
			So(doc.VersionNo, ShouldEqual, 1)
			So(doc.CreatedBy, ShouldEqual, "user123")
			So(doc.TenantID, ShouldEqual, "tenant123")
		})

		Convey("Error - GetNoMaxAndLockByNoPrefix fails", func() {
			// 模拟：获取并锁定编号失败
			mockBasicRef.EXPECT().GenerateID().Return("new-doc-id")
			mockInternalRef.EXPECT().GetNoMaxAndLockByNoPrefix(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(0, errors.New("lock error"))
			tx.EXPECT().Rollback().Return(nil)

			_, err := service.buildInternalDocument(ctx, req, tx)
			So(err, ShouldNotBeNil)
		})
	})
}
