// Code generated by MockGen. DO NOT EDIT.
// Source: factory.go

// Package factory is a generated GoMock package.
package factory

import (
	aggregate "docvault/rpc/internal/domain/aggregate"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockFactory is a mock of Factory interface.
type MockFactory struct {
	ctrl     *gomock.Controller
	recorder *MockFactoryMockRecorder
}

// MockFactoryMockRecorder is the mock recorder for MockFactory.
type MockFactoryMockRecorder struct {
	mock *MockFactory
}

// NewMockFactory creates a new mock instance.
func NewMockFactory(ctrl *gomock.Controller) *MockFactory {
	mock := &MockFactory{ctrl: ctrl}
	mock.recorder = &MockFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFactory) EXPECT() *MockFactoryMockRecorder {
	return m.recorder
}

// NewExternalDocumentLibrary mocks base method.
func (m *MockFactory) NewExternalDocumentLibrary() aggregate.ExternalDocumentLibraryService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewExternalDocumentLibrary")
	ret0, _ := ret[0].(aggregate.ExternalDocumentLibraryService)
	return ret0
}

// NewExternalDocumentLibrary indicates an expected call of NewExternalDocumentLibrary.
func (mr *MockFactoryMockRecorder) NewExternalDocumentLibrary() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewExternalDocumentLibrary", reflect.TypeOf((*MockFactory)(nil).NewExternalDocumentLibrary))
}

// NewInternalDocumentLibrary mocks base method.
func (m *MockFactory) NewInternalDocumentLibrary() aggregate.InternalDocumentLibraryService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewInternalDocumentLibrary")
	ret0, _ := ret[0].(aggregate.InternalDocumentLibraryService)
	return ret0
}

// NewInternalDocumentLibrary indicates an expected call of NewInternalDocumentLibrary.
func (mr *MockFactoryMockRecorder) NewInternalDocumentLibrary() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewInternalDocumentLibrary", reflect.TypeOf((*MockFactory)(nil).NewInternalDocumentLibrary))
}
