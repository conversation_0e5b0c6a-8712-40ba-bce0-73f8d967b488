package factory

import (
	"docvault/rpc/internal/config"
	"docvault/rpc/internal/domain/aggregate"
)

type Impl struct {
	c                                config.Config
	basicAbilityReference            aggregate.BasicAbilityReference
	internalDocumentLibraryReference aggregate.InternalDocumentLibraryReference
	externalDocumentLibraryReference aggregate.ExternalDocumentLibraryReference
	documentLibraryReference         aggregate.DocumentLibraryReference
}

func NewImpl(
	c config.Config,
	basicAbilityReference aggregate.BasicAbilityReference,
	internalDocumentLibraryReference aggregate.InternalDocumentLibraryReference,
	externalDocumentLibraryReference aggregate.ExternalDocumentLibraryReference,
	documentLibraryReference aggregate.DocumentLibraryReference,
) Factory {
	return &Impl{
		c: c, basicAbilityReference: basicAbilityReference,
		internalDocumentLibraryReference: internalDocumentLibraryReference,
		externalDocumentLibraryReference: externalDocumentLibraryReference,
		documentLibraryReference:         documentLibraryReference,
	}
}

func (i *Impl) NewInternalDocumentLibrary() aggregate.InternalDocumentLibraryService {
	return aggregate.NewInternalDocumentLibrary(i.internalDocumentLibraryReference, i.basicAbilityReference)
}

func (i *Impl) NewExternalDocumentLibrary() aggregate.ExternalDocumentLibraryService {
	return aggregate.NewExternalDocumentLibrary(i.externalDocumentLibraryReference, i.basicAbilityReference)
}

func (i *Impl) NewDocumentLibrary() aggregate.DocumentLibraryService {
	return aggregate.NewDocumentLibrary(i.documentLibraryReference, i.basicAbilityReference)
}
