package entity

import "time"

type ExternalDocument struct {
	ID                              string    `json:"id"`
	Number                          string    `json:"number"`
	Version                         string    `json:"version"`
	NumberPostfix                   int       `json:"numberPostfix"`
	OriginalNumber                  string    `json:"originalNumber"`
	OriginalVersion                 string    `json:"originalVersion"`
	Name                            string    `json:"name"`
	DocType                         string    `json:"docType"`
	Domain                          string    `json:"domain"`
	OriginalDocNumber               string    `json:"originalDocNumber"`
	PublishDocNumber                string    `json:"publishDocNumber"`
	PublishDepartment               string    `json:"publishDepartment"`
	PublishDate                     time.Time `json:"publishDate"`
	EffectiveDate                   time.Time `json:"effectiveDate"`
	Authentications                 []string  `json:"authentication"`
	Status                          int       `json:"status"`
	TypeDictionaryNodeId            string    `json:"typeDictionaryNodeId"`
	DomainDictionaryNodeId          string    `json:"domainDictionaryNodeId"`
	AuthenticationDictionaryNodeIds []string  `json:"authenticationDictionaryNodeId"`
	FileID                          string    `json:"fileId"`
	OrganizationID                  string    `json:"organizationId"`
	CreatedBy                       string    `json:"createdBy"`
	UpdatedBy                       string    `json:"updatedBy"`
}
