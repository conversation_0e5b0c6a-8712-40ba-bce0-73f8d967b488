package entity

import (
	"docvault/rpc/internal/domain/value"
	"time"
)

const (
	InternalDocumentStatusInvalid = 1 // 即将作废
	InternalDocumentStatusPending = 2 // 即将实施
	InternalDocumentStatusValid   = 3 // 有效
	InternalDocumentStatusRevise  = 4 // 拟修订
)

type InternalDocument struct {
	ID             string             // 主键
	OrganizationID string             // 子公司ID
	NoPrefix       string             // 编号前缀
	No             string             // 编号
	SerialNo       int                // 编号序列号
	Name           string             // 文件名称
	FileID         string             // 文件id
	DocCategoryID  string             // 类别ID
	DepartmentID   string             // 编制部门ID
	AuthorID       string             // 编制人ID
	Status         int8               // 状态 1- 即将作废，2- 即将实施，3- 有效，4- 拟修订
	ApprovalInfo   value.ApprovalInfo // 审批信息
	VersionNo      int                // 版本
	PublishDate    time.Time          // 发布日期
	EffectiveDate  time.Time          // 实施日期
	// 原文件编号
	OriginalNo        string    // 原文件编号
	OriginalVersionNo string    // 原文件版本
	CreatedAt         time.Time // 创建时间
	UpdatedAt         time.Time // 更新时间
	CreatedBy         string    // 创建人
	UpdatedBy         string    // 更新人
	TenantID          string    // 租户ID
}

func (d *InternalDocument) AutoUpdateStatus() {
	if d.Status > 3 {
		return
	}

	if d.EffectiveDate.Before(time.Now()) {
		d.Status = InternalDocumentStatusValid
	} else {
		d.Status = InternalDocumentStatusPending
	}
}

// Revise 修订文档，版本号+1，更新修订人和时间
func (d *InternalDocument) Revise(updatedBy string) {
	d.AutoUpdateStatus()
	d.VersionNo++
	d.UpdatedAt = time.Now()
	d.UpdatedBy = updatedBy
}
