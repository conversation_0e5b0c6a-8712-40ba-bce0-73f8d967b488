package entity

type DistributeRecordInfo struct {
	ID                 string           `json:"id"`                 // 主键id
	WorkflowID         string           `json:"workflowId"`         // 流程id
	Applicant          string           `json:"applicant"`          // 申请人,用户id
	ApplyDate          int64            `json:"applyDate"`          // 申请日期
	DistributeType     int32            `json:"distributeType"`     // 发放类型，1内部发放 | 2外部发放
	FileType           int32            `json:"fileType"`           // 文件类型，1内部文件 | 2外部文件
	FileCategory       string           `json:"fileCategory"`       // 文件类别
	TypeDictNodeId     string           `json:"typeDictNodeId"`     // 类型字典节点id
	Reason             string           `json:"reason"`             // 原因
	OtherReason        string           `json:"otherReason"`        // 其他原因
	WishDistributeDate int64            `json:"wishDistributeDate"` // 期望发放日期
	DistributeCount    int              `json:"distributeCount"`    // 发放份数
	SignForCount       int              `json:"signForCount"`       // 签收份数
	DisposeCount       int              `json:"disposeCount"`       // 处置份数
	Status             int              `json:"status"`             // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
	ApprovalInfo       ApprovalInfo     `json:"approvalInfo"`       // 审批信息
	DistributeList     []DistributeList `json:"distributeList"`     // 发放清单
}

type DistributeList struct {
	FileID      string       `json:"fileId"`      // 文件id
	FileName    string       `json:"fileName"`    // 文件名   舍弃
	Number      string       `json:"number"`      // 文件编号 舍弃
	Version     string       `json:"version"`     // 版本
	Permissions []Permission `json:"permissions"` // 权限
}

type Permission struct {
	FileForm       int32        `json:"fileForm"`       // 文件形式,1电子文件 | 2纸质文件
	FilePermission int32        `json:"filePermission"` // 文件权限,1查阅 | 2查阅/下载 | 3一次下载
	Recipient      string       `json:"recipient"`      // 接收方
	ReceivedBy     []ReceivedBy `json:"receivedBy"`     // 接收人
}

type ReceivedBy struct {
	UserId        string `json:"userId"`
	UserName      string `json:"userName"`
	SignForStatus int32  `json:"signForStatus"` // 签收状态,1未签收 | 2已签收
	DisposeStatus int32  `json:"disposeStatus"` // 处置状态,1未回收 | 2已回收
}

type ApprovalInfo struct {
	Auditors  []ApprovalItem `json:"auditors"`  // 审核人
	Approvers []ApprovalItem `json:"approvers"` // 批准人
}

type ApprovalItem struct {
	UserID     string `json:"userId"`
	PassedDate int64  `json:"passedDate"`
}

type RecycleApprovalInfo struct {
	ID            string        `json:"id"`             // 主键id
	DistributeID  string        `json:"distributeId"`   // 发放记录ID
	RecycleBy     string        `json:"recycleBy"`      // 回收人
	RecycleDate   int64         `json:"recycle_date"`   // 回收日期
	RecycleReason string        `json:"recycle_reason"` // 回收原因
	OtherReason   string        `json:"other_reason"`   // 其他原因
	RecycleList   []RecycleList `json:"recycle_list"`   // 回收清单
	WorkflowID    string        `json:"workflowId"`     // 流程id
	ApprovalInfo  ApprovalInfo  `json:"approvalInfo"`   // 审批信息
}

type RecycleList struct {
	FileID      string           `json:"file_id"`     // 文件ID
	Permissions []FilePermission `json:"permissions"` // 文件权限
}

type FilePermission struct {
	FileForm       int      `json:"fileForm"`       // 文件形式
	FilePermission int      `json:"filePermission"` // 文件权限
	ReceivedBy     []string `json:"receivedBy"`     // 接收人
}

type DisposalApprovalInfo struct {
	ID             string         `json:"id"`             // 主键id
	DistributeID   string         `json:"distributeId"`   // 发放记录ID
	DisposalBy     string         `json:"disposalBy"`     // 处置人
	DisposalDate   int64          `json:"recycle_date"`   // 处置日期
	DisposalReason string         `json:"recycle_reason"` // 处置方式
	DisposalCount  int            `json:"disposeCount"`   // 处置份数，累加，判断累加之后是否小于等于发放份数
	DisposalList   []DisposalList `json:"recycle_list"`   // 处置清单
	WorkflowID     string         `json:"workflowId"`     // 流程id
	ApprovalInfo   ApprovalInfo   `json:"approvalInfo"`   // 审批信息
}

type DisposalList struct {
	FileID     string   `json:"file_id"`    // 文件ID
	FileForm   int      `json:"fileForm"`   // 文件形式
	ReceivedBy []string `json:"receivedBy"` // 接收人
}

type GetDistributeListInfo struct {
	ID                 string           `json:"id"`                 // 发放列表id
	Applicant          string           `json:"applicant"`          // 申请人
	ApplyDate          int64            `json:"applyDate"`          // 申请时间
	DistributeType     int              `json:"distributeType"`     // 发放类型，1内部发放 | 2外部发放
	FileType           int              `json:"fileType"`           // 文件类型，1内部文件 | 2外部文件
	FileCategory       string           `json:"fileCategory"`       // 文件类别
	WishDistributeDate int64            `json:"wishDistributeDate"` // 期望发放时间
	Reason             string           `json:"reason"`             // 发放原因
	OtherReason        string           `json:"otherReason"`        // 其他原因
	Status             int              `json:"status"`             // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
	WorkflowID         string           `json:"workflowID"`         // 流程id
	ApprovalInfo       ApprovalInfo     `json:"approvalInfo"`       // 审批信息
	Received           []DistributeUser `json:"received"`           // 已接收人 接收人的签收状态 SignForStatus = 2
	NotReceived        []DistributeUser `json:"notReceived"`        // 未接收人 接收人的签收状态 SignForStatus = 1
	Recycle            []DistributeUser `json:"recycle"`            // 已回收人 接收人的处置状态 DisposeStatus = 2
}

type DistributeUser struct {
	UserID   string `json:"userId"`   // 用户id
	FileForm int    `json:"fileForm"` // 文件形式，1电子文件 | 2纸质文件
}

type DistributeInventory struct {
	ID          string           `json:"id"`          // 发放清单id
	FileId      string           `json:"fileId"`      // 文档id
	FileName    string           `json:"fileName"`    // 文档名称
	Number      string           `json:"number"`      // 文档编号
	Version     string           `json:"version"`     // 文档版本
	Permissions []PermissionResp `json:"permissions"` // 权限列表
}

type PermissionResp struct {
	FileForm       int32      `json:"fileForm"`       // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32      `json:"filePermission"` // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
	ReceivedBy     []Received `json:"receivedBy"`     // 接收人
}

type Received struct {
	UserID   string `json:"userId"`   // 用户id
	Nickname string `json:"nickname"` // 用户昵称
	Status   int    `json:"status"`   // 状态（1未回收 | 2回收审批中 | 3已回收）
}
