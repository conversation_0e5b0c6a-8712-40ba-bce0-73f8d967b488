package value

import (
	"time"
)

type OperatedType int8

const (
	OperatedTypeAdd     OperatedType = 1 // 新增
	OperatedTypeRevise  OperatedType = 2 // 修订
	OperatedTypeInvalid OperatedType = 3 // 作废
)

type InternalDocumentHistory struct {
	ID             string       // 主键
	MainID         string       // 关联主表ID
	OrganizationID string       // 子公司ID
	NoPrefix       string       // 编号前缀
	No             string       // 编号
	SerialNo       int          // 编号序列号
	Name           string       // 文件名称
	FileID         string       // 文件id
	DocCategoryID  string       // 类别ID
	DepartmentID   string       // 编制部门ID
	AuthorID       string       // 编制人 id
	ApprovalInfo   ApprovalInfo // 审批信息
	VersionNo      int          // 版本
	PublishDate    time.Time    // 发布日期
	EffectiveDate  time.Time    // 实施日期
	OperatedType   OperatedType // 操作类型 1-新增,2-修订,3-作废
	CreatedAt      time.Time    // 创建时间
	UpdatedAt      time.Time    // 更新时间
	CreatedBy      string       // 创建人
	UpdatedBy      string       // 更新人
}
