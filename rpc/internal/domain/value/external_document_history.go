package value

type DictNodeIdAndNumberPostfix struct {
	DictNodeId    string
	NumberPostfix int
}

type ExternalDocumentPage struct {
	Page                           int      // 页码
	PageSize                       int      // 每页数量
	NoPage                         bool     // 是否不分页
	Number                         string   // 编号
	Name                           string   // 文件名称
	OriginalNumber                 string   // 原文件编号
	OriginalDocNumber              string   // 原文件号
	PublishDocNumber               string   // 发文号
	PublishDepartment              string   // 发文部门
	TypeDictionaryNodeIds          []string // 类别ID
	DomainDictionaryNodeId         string   // 领域类型id
	AuthenticationDictionaryNodeId string   // 认证类型id
	BeAttachedFile                 string   // 是否有附件
	Status                         int      // 状态
	OrgID                          string   // 组织id
}

type ExternalDocumentPageInfo struct {
	Total int64                  // 总数
	Data  []ExternalDocumentList // 数据
}

type ExternalDocumentList struct {
	ID                              string   // id
	Number                          string   // 编号
	Version                         string   // 版本
	OriginalNumber                  string   // 原文件编号
	OriginalVersion                 string   // 原文件版本
	Name                            string   // 文件名称
	DocType                         string   // 类别
	Domain                          string   // 领域
	OriginalDocNumber               string   // 原文件号
	PublishDocNumber                string   // 发文号
	PublishDepartment               string   // 发文部门
	PublishDate                     int64    // 发布日期
	EffectiveDate                   int64    // 实施日期
	Authentications                 []string // 认证方式
	Status                          int      // 状态
	TypeDictionaryNodeId            string   // 类别ID
	DomainDictionaryNodeId          string   // 领域类型id
	AuthenticationDictionaryNodeIds []string // 认证类型id
	FileID                          string   // 文件id
}
