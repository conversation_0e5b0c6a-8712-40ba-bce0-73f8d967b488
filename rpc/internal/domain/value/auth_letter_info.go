package value

const (
	AuthLetterFieldTypeText          = "text"
	AuthLetterFieldTypeImage         = "image"
	AuthLetterFieldFillingTypeText   = "text"
	AuthLetterFieldFillingTypeBase64 = "base64"
)

const (
	// 申请人
	AuthLetterFieldKeyApplicant = "applicant"
	// 审批人
	AuthLetterFieldKeyApprover = "approver"
	// 最后一个审批人
	AuthLetterFieldKeyLastApprover = "lastApprover"

	// 签名图片
	AuthLetterFieldKeySignatureImage = "signatureImage"
	// 授权年限
	AuthLetterFieldKeyEnableYear = "enableYear"
	// 授权年限月
	AuthLetterFieldKeyEnableMonth = "enableMonth"
	// 授权年限日
	AuthLetterFieldKeyEnableDay = "enableDay"

	// 审批人年
	AuthLetterFieldKeyApproverYear = "approverYear"
	// 审批人月
	AuthLetterFieldKeyApproverMonth = "approverMonth"
	// 审批日
	AuthLetterFieldKeyApproverDay = "approverDay"
	// 申请年
	AuthLetterFieldKeyApplicantYear = "applicantYear"
	// 申请月
	AuthLetterFieldKeyApplicantMonth = "applicantMonth"
	// 申请日
	AuthLetterFieldKeyApplicantDay = "applicantDay"
)

type AuthLetterInfo struct {
	AuthLetterFileName string
	AuthLetterParams   []AuthLetterParam
}

type AuthLetterParam struct {
	// 字段类型: text, image
	FieldType string `json:"fieldType"`
	// 字段key
	FieldKey string `json:"fieldKey"`
	// 填充类型: text, base64(fieldType=image格式)
	FieldFillingType string `json:"fieldFillingType"`
	// 填充值
	FieldValue interface{} `json:"fieldValue"`
	// 图片宽度
	ImgWidth int `json:"imgWidth"`
	// 图片高度
	ImgHeight int `json:"imgHeight"`
}
