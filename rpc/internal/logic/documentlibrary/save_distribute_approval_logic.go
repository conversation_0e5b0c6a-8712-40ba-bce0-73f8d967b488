package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/utils"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveDistributeApprovalLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSaveDistributeApprovalLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveDistributeApprovalLogic {
	return &SaveDistributeApprovalLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 保存发放审批信息
func (l *SaveDistributeApprovalLogic) SaveDistributeApproval(in *docvault.DistributeApprovalReq) (*docvault.EmptyResp, error) {
	// 创建审批信息实体
	approvalInfo := &aggregate.ApprovalInfo{}
	err := utils.StructCopy(l.ctx, approvalInfo, in.ApprovalInfo)
	if err != nil {
		l.Logger.Errorf("拷贝审批信息失败：%v", err)
		return nil, err
	}
	// 调用聚合根保存审批信息
	err = l.svcCtx.Factory.NewDocumentLibrary().SaveDistributeApproval(l.ctx, &aggregate.DistributeApprovalRequest{
		WorkflowID:   in.WorkflowId,
		Status:       int(in.Status),
		ApprovalInfo: *approvalInfo,
	})
	if err != nil {
		l.Logger.Errorf("保存发放审批信息失败：%v", err)
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}
