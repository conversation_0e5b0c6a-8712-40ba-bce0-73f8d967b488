package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/utils"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveDisposalApprovalInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSaveDisposalApprovalInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveDisposalApprovalInfoLogic {
	return &SaveDisposalApprovalInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 保存处置审批信息
func (l *SaveDisposalApprovalInfoLogic) SaveDisposalApprovalInfo(in *docvault.DisposalApprovalInfo) (*docvault.EmptyResp, error) {
	request := &aggregate.DisposalApprovalRequest{}
	err := utils.StructCopy(l.ctx, request, in)
	if err != nil {
		l.Logger.Errorf("处置信息copy失败: %v", err)
		return nil, err
	}
	request.DistributeID = in.DistributeId
	request.WorkflowID = in.WorkflowId
	request.DisposalBy = utils.GetContextUserID(l.ctx)
	err = l.svcCtx.Factory.NewDocumentLibrary().SaveDisposalApprovalInfo(l.ctx, request)
	if err != nil {
		l.Logger.Errorf("保存处置信息失败: %v", err)
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}
