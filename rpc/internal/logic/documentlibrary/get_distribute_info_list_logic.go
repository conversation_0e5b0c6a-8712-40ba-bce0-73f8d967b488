package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/utils"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeInfoListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDistributeInfoListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeInfoListLogic {
	return &GetDistributeInfoListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetDistributeInfoListLogic) GetDistributeInfoList(in *docvault.GetDistributeListReq) (*docvault.GetDistributeListResp, error) {
	// 创建聚合请求
	var request aggregate.GetDistributeListRequest
	err := utils.StructCopy(l.ctx, &request, in)
	if err != nil {
		l.Logger.Errorf("获取发放列表入参 copy 失败: %v", err)
		return nil, err
	}

	// 调用聚合服务获取发放列表信息
	infos, err := l.svcCtx.Factory.NewDocumentLibrary().GetDistributeListInfos(l.ctx, &request)
	if err != nil {
		l.Logger.Errorf("获取发放列表失败: %v", err)
		return nil, err
	}
	// 创建返回值
	resp := &docvault.GetDistributeListResp{}
	err = utils.StructCopy(l.ctx, resp, infos)
	if err != nil {
		l.Logger.Errorf("获取发放列表返回值 copy 失败: %v", err)
		return nil, err
	}

	return resp, nil
}
