package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDocPermissionUsersLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDocPermissionUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDocPermissionUsersLogic {
	return &GetDocPermissionUsersLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取文档权限用户
func (l *GetDocPermissionUsersLogic) GetDocPermissionUsers(in *docvault.GetDocPermissionUsersReq) (*docvault.GetDocPermissionUsersResp, error) {
	docPermissionUsers, err := l.svcCtx.Factory.NewDocumentLibrary().GetDocPermissionUsers(l.ctx, &aggregate.GetDocPermissionUsersRequest{
		FileId:         in.FileId,
		FileFrom:       in.FileFrom,
		FilePermission: in.FilePermission,
	})
	if err != nil {
		return nil, err
	}

	return &docvault.GetDocPermissionUsersResp{
		WaitForApprovalUsers: docPermissionUsers.WaitForApprovalUsers,
		NotRecycledUsers:     docPermissionUsers.NotRecycledUsers,
		RecycleFlowUsers:     docPermissionUsers.RecycleFlowUsers,
	}, nil
}
