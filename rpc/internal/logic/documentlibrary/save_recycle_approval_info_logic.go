package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/utils"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveRecycleApprovalInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSaveRecycleApprovalInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveRecycleApprovalInfoLogic {
	return &SaveRecycleApprovalInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 保存回收审批信息
func (l *SaveRecycleApprovalInfoLogic) SaveRecycleApprovalInfo(in *docvault.RecycleApprovalInfo) (*docvault.EmptyResp, error) {
	request := &aggregate.RecycleApprovalRequest{}
	err := utils.StructCopy(l.ctx, request, in)
	if err != nil {
		l.Logger.Errorf("回收信息copy失败: %v", err)
		return nil, err
	}
	request.DistributeID = in.DistributeId
	request.WorkflowID = in.WorkflowId
	request.RecycleBy = utils.GetContextUserID(l.ctx)
	err = l.svcCtx.Factory.NewDocumentLibrary().SaveRecycleApprovalInfo(l.ctx, request)
	if err != nil {
		l.Logger.Errorf("保存回收审批信息失败: %v", err)
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}
