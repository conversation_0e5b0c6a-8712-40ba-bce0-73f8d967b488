package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeApplicationByIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDistributeApplicationByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeApplicationByIdLogic {
	return &GetDistributeApplicationByIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 根据发放列表id获取发放清单信息
func (l *GetDistributeApplicationByIdLogic) GetDistributeApplicationById(in *docvault.GetDistributeApplicationReq) (*docvault.GetDistributeApplicationResp, error) {
	inventories, err := l.svcCtx.Factory.NewDocumentLibrary().GetDistributeInventories(l.ctx, in.Id)
	if err != nil {
		l.Logger.Errorf("获取发放清单信息失败: %v", err)
		return nil, err
	}

	// 数据转换
	distributeInventories := l.dataConversion(inventories)

	return &docvault.GetDistributeApplicationResp{
		Data: distributeInventories,
	}, nil
}

func (l *GetDistributeApplicationByIdLogic) dataConversion(inventories []aggregate.DistributeInventory) []*docvault.DistributeInventory {
	if len(inventories) == 0 {
		return nil
	}
	var distributeInventories []*docvault.DistributeInventory
	for _, inventory := range inventories {
		var permissionResp []*docvault.PermissionResp
		for _, permission := range inventory.Permissions {
			var receivedBy []*docvault.ReceivedBy
			for _, user := range permission.ReceivedBy {
				receivedBy = append(receivedBy, &docvault.ReceivedBy{
					UserId:   user.UserID,
					Nickname: user.Nickname,
					Status:   int32(user.Status),
				})
			}
			permissionResp = append(permissionResp, &docvault.PermissionResp{
				FileForm:       permission.FileForm,
				FilePermission: permission.FilePermission,
				ReceivedBy:     receivedBy,
			})
		}
		distributeInventories = append(distributeInventories, &docvault.DistributeInventory{
			Id:          inventory.ID,
			FileId:      inventory.FileId,
			FileName:    inventory.FileName,
			Number:      inventory.Number,
			Version:     inventory.Version,
			Permissions: permissionResp,
		})
	}
	return distributeInventories
}
