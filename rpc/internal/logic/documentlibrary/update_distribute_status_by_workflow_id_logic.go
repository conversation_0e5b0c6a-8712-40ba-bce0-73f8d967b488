package documentlibrarylogic

import (
	"context"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDistributeStatusByWorkflowIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDistributeStatusByWorkflowIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDistributeStatusByWorkflowIdLogic {
	return &UpdateDistributeStatusByWorkflowIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新发放状态
func (l *UpdateDistributeStatusByWorkflowIdLogic) UpdateDistributeStatusByWorkflowId(in *docvault.UpdateDistributeStatusReq) (*docvault.EmptyResp, error) {
	err := l.svcCtx.Factory.NewDocumentLibrary().UpdateDistributeStatusByWorkflowId(l.ctx, in.WorkflowId, in.Status)
	return &docvault.EmptyResp{}, err
}
