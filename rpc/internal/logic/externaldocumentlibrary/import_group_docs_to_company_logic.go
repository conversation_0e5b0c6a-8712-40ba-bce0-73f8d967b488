package externaldocumentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type ImportGroupDocsToCompanyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewImportGroupDocsToCompanyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ImportGroupDocsToCompanyLogic {
	return &ImportGroupDocsToCompanyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 导入集团文档到公司
func (l *ImportGroupDocsToCompanyLogic) ImportGroupDocsToCompany(in *docvault.ImportGroupDocsToCompanyReq) (*docvault.ImportGroupDocsToCompanyResp, error) {
	var data []aggregate.ImportGroupDocsToCompanyInfo
	for _, v := range in.Data {
		data = append(data, aggregate.ImportGroupDocsToCompanyInfo{
			ID:              v.Id,
			OriginalNumber:  v.OriginalNumber,
			OriginalVersion: v.OriginalVersion,
		})
	}
	err := l.svcCtx.Factory.NewExternalDocumentLibrary().ImportGroupDocsToCompany(l.ctx, &aggregate.ImportGroupDocsToCompanyRequest{
		OrgCode: in.OrgCode,
		Data:    data,
	})
	if err != nil {
		l.Logger.Errorf("从集团纳入文件到子公司失败：%v", err)
		return nil, err
	}
	return &docvault.ImportGroupDocsToCompanyResp{}, nil
}
