package externaldocumentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

type PageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewPageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PageLogic {
	return &PageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 分页查询文档
func (l *PageLogic) Page(in *docvault.ExternalDocumentPageReq) (*docvault.ExternalDocumentPageResp, error) {
	externalDocumentInfo, err := l.svcCtx.Factory.NewExternalDocumentLibrary().GetList(l.ctx, &aggregate.ExternalDocumentPageReq{
		Page:                           int(in.PageInfo.Page),
		PageSize:                       int(in.PageInfo.PageSize),
		NoPage:                         in.PageInfo.NoPage,
		Number:                         in.Number,
		Name:                           in.Name,
		OriginalNumber:                 in.OriginalNumber,
		OriginalDocNumber:              in.OriginalDocNumber,
		PublishDocNumber:               in.PublishDocNumber,
		PublishDepartment:              in.PublishDepartment,
		TypeDictionaryNodeIds:          in.TypeDictionaryNodeIds,
		DomainDictionaryNodeId:         in.DomainDictionaryNodeId,
		AuthenticationDictionaryNodeId: in.AuthenticationDictionaryNodeId,
		BeAttachedFile:                 in.BeAttachedFile,
		Status:                         int(in.Status),
		OrgType:                        int(in.OrgType),
		OrgID:                          in.OrgId,
	})
	if err != nil {
		return nil, err
	}
	externalDocumentPageInfo := l.aggToResp(externalDocumentInfo.Data)
	return &docvault.ExternalDocumentPageResp{
		Total: externalDocumentInfo.Total,
		Data:  externalDocumentPageInfo,
	}, nil
}

func (l *PageLogic) aggToResp(externalDocumentInfo []aggregate.ExternalDocumentPageList) (externalDocumentPageInfo []*docvault.ExternalDocumentPageInfo) {
	for _, v := range externalDocumentInfo {
		authentication := strings.Join(v.Authentications, "、")
		externalDocumentPageInfo = append(externalDocumentPageInfo, &docvault.ExternalDocumentPageInfo{
			Id:                              v.ID,
			Number:                          v.Number,
			Version:                         v.Version,
			OriginalNumber:                  v.OriginalNumber,
			OriginalVersion:                 v.OriginalVersion,
			Name:                            v.Name,
			DocType:                         v.DocType,
			Domain:                          v.Domain,
			OriginalDocNumber:               v.OriginalDocNumber,
			PublishDocNumber:                v.PublishDocNumber,
			PublishDepartment:               v.PublishDepartment,
			PublishDate:                     v.PublishDate,
			EffectiveDate:                   v.EffectiveDate,
			Authentication:                  authentication,
			Status:                          int32(v.Status),
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
			FileId:                          v.FileID,
		})
	}
	return externalDocumentPageInfo
}
