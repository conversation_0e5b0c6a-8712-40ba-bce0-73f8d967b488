package externaldocumentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeLogic {
	return &ChangeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修订文档
func (l *ChangeLogic) Change(in *docvault.ExternalDocumentChangeReq) (*docvault.EmptyResp, error) {
	err := l.svcCtx.Factory.NewExternalDocumentLibrary().Change(l.ctx, aggregate.ExternalDocumentChangeRequest{
		ID: in.Id,
		ExternalDocumentCreateRequest: aggregate.ExternalDocumentCreateRequest{
			NumberPrefix:                    in.NumberPrefix,
			Name:                            in.Name,
			FileID:                          in.FileId,
			DocType:                         in.DocType,
			Domain:                          in.Domain,
			Authentications:                 in.Authentications,
			TypeDictionaryNodeId:            in.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          in.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: in.AuthenticationDictionaryNodeIds,
			PublishDate:                     in.PublishDate,
			EffectiveDate:                   in.EffectiveDate,
			OriginalDocNumber:               in.OriginalDocNumber,
			PublishDocNumber:                in.PublishDocNumber,
			PublishDepartment:               in.PublishDepartment,
			OriginalVersion:                 in.OriginalVersion,
			OriginalNumber:                  in.OriginalNumber,
			OrgID:                           utils.GetContextOrganizationID(l.ctx),
		},
	})
	if err != nil {
		return nil, err
	}

	return &docvault.EmptyResp{}, nil
}
