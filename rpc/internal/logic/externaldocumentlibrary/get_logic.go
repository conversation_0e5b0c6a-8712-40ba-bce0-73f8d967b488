package externaldocumentlibrarylogic

import (
	"context"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取文档
func (l *GetLogic) Get(in *docvault.ExternalDocumentGetReq) (*docvault.ExternalDocumentGetResp, error) {
	// todo: add your logic here and delete this line

	return &docvault.ExternalDocumentGetResp{}, nil
}
