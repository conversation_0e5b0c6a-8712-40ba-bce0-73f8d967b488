package externaldocumentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type ImportCompanyPlagiarismCheckLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewImportCompanyPlagiarismCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ImportCompanyPlagiarismCheckLogic {
	return &ImportCompanyPlagiarismCheckLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 导入集团文档到公司验重
func (l *ImportCompanyPlagiarismCheckLogic) ImportCompanyPlagiarismCheck(in *docvault.PlagiarismCheckReq) (*docvault.EmptyResp, error) {
	err := l.svcCtx.Factory.NewExternalDocumentLibrary().ImportCompanyPlagiarismCheck(l.ctx, &aggregate.ImportCompanyPlagiarismCheckRequest{
		Ids: in.Ids,
	})
	if err != nil {
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}
