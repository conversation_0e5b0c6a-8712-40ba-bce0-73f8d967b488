package externaldocumentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建文档
func (l *CreateLogic) Create(in *docvault.ExternalDocumentCreateReq) (*docvault.ExternalDocumentCreateResp, error) {
	var externalDocumentCreateRequest []aggregate.ExternalDocumentCreateRequest
	for _, v := range in.Data {
		externalDocumentCreateRequest = append(externalDocumentCreateRequest, aggregate.ExternalDocumentCreateRequest{
			NumberPrefix:                    v.NumberPrefix,
			Name:                            v.Name,
			FileID:                          v.FileId,
			DocType:                         v.DocType,
			Domain:                          v.Domain,
			Authentications:                 v.Authentications,
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
			ApprovalInfo:                    aggregate.ApprovalInfo{},
			PublishDate:                     v.PublishDate,
			EffectiveDate:                   v.EffectiveDate,
			OriginalDocNumber:               v.OriginalDocNumber,
			PublishDocNumber:                v.PublishDocNumber,
			PublishDepartment:               v.PublishDepartment,
			OrgType:                         int(in.OrgType),
			OrgID:                           in.OrgId,
			OriginalVersion:                 v.OriginalVersion,
			OriginalNumber:                  v.OriginalNumber,
		})
	}

	externalDocumentInfo, err := l.svcCtx.Factory.NewExternalDocumentLibrary().BatchCreate(l.ctx, externalDocumentCreateRequest)
	if err != nil {
		return nil, err
	}

	var externalDocumentCreateRespList []*docvault.ExternalDocumentCreateRespList
	for _, v := range externalDocumentInfo {
		externalDocumentCreateRespList = append(externalDocumentCreateRespList, &docvault.ExternalDocumentCreateRespList{
			Id:                              v.ID,
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
		})
	}

	return &docvault.ExternalDocumentCreateResp{
		Data: externalDocumentCreateRespList,
	}, nil
}
