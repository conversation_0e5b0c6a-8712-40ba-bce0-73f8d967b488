package booklogic

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"errors"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateBookLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBookLogic {
	return &UpdateBookLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修改书籍信息
func (l *UpdateBookLogic) UpdateBook(in *docvault.BookInfo) (*docvault.UpdateBookResp, error) {
	bookClient := mapper.NewBooksClient(l.svcCtx.DocvaultDB)
	bookInfo, err := bookClient.GetBookInfoById(l.ctx, in.Id)
	if err != nil {
		l.Logger.Errorf("查询书籍信息失败：%v", err)
		return nil, err
	}
	book, err := bookClient.GetBookByOrgIdAndName(l.ctx, in.OrganizationId, in.Name)
	if err != nil {
		l.Logger.Errorf("查询书籍信息失败：%v", err)
		return nil, err
	}
	if book.Id != "" && book.Id != in.Id {
		return nil, errors.New("书籍名称已存在")
	}
	if in.RegisterCount < bookInfo.ReceiveCount+bookInfo.BorrowCount {
		return nil, errors.New("修改失败，在册总数小于使用数量。")
	}
	in.SurplusCount = in.RegisterCount - bookInfo.ReceiveCount - bookInfo.BorrowCount
	// 领用中或借出中，只能修改在册总数
	if bookInfo.ReceiveCount > 0 || bookInfo.OnBorrow {
		err = bookClient.UpdateRegisterCountById(l.ctx, in.Id, in.UpdatedBy, int(in.RegisterCount), int(in.SurplusCount))
		if err != nil {
			l.Logger.Errorf("修改书籍信息失败：%v", err)
			return nil, err
		}
		return &docvault.UpdateBookResp{}, nil
	}

	// 设置编号和类型
	number, bookType, sort, err := l.getNumber(in, bookClient, bookInfo)
	if err != nil {
		return nil, err
	}

	err = bookClient.Update(l.ctx, mapper.Books{
		Id:               in.Id,
		Number:           number,
		Name:             in.Name,
		Author:           in.Author,
		Publisher:        in.Publisher,
		BookType:         bookType,
		RegisterCount:    in.RegisterCount,
		SurplusCount:     in.RegisterCount,
		UpdatedBy:        in.UpdatedBy,
		DictionaryNodeID: in.DictionaryNodeId,
		FileID:           in.FileId,
		Sort:             sort,
	})
	if err != nil {
		return nil, err
	}
	return &docvault.UpdateBookResp{}, nil
}

func (l *UpdateBookLogic) getNumber(in *docvault.BookInfo, bookClient *mapper.BooksClient, bookInfo mapper.Books) (string, string, int, error) {
	if in.DictionaryNodeId == bookInfo.DictionaryNodeID {
		in.Number = bookInfo.Number
		in.BookType = bookInfo.BookType
		return in.Number, in.BookType, bookInfo.Sort, nil
	}

	bookInfo, err := bookClient.GetMaxNumberByDictNodeId(l.ctx, in.OrganizationId, in.DictionaryNodeId)
	if err != nil {
		return "", "", 0, err
	}

	sort := 1
	if bookInfo.Number == "" {
		in.Number += "001"
	} else {
		sort = bookInfo.Sort + 1
		newNum := fmt.Sprintf("%03d", sort)
		in.Number += newNum
	}
	return in.Number, in.BookType, sort, nil
}
