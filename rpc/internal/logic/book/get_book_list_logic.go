package booklogic

import (
	"context"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBookListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetBookListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBookListLogic {
	return &GetBookListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 查询书籍列表
func (l *GetBookListLogic) GetBookList(in *docvault.GetBookListReq) (*docvault.GetBookListResp, error) {
	bookList, total, err := mapper.NewBooksClient(l.svcCtx.DocvaultDB).
		GetBookList(l.ctx, in.PageInfo.NoPage, int(in.PageInfo.Page), int(in.PageInfo.PageSize), in.Number, in.Name, in.Author, in.Publisher, in.OnBorrow, in.DictionaryNodeIds)
	if err != nil {
		l.Logger.Errorf("查询书籍列表失败")
		return nil, err
	}

	// 组装返回
	var list []*docvault.BookInfo
	for _, b := range bookList {
		list = append(list, &docvault.BookInfo{
			Id:               b.Id,
			Status:           int32(b.Status),
			Number:           b.Number,
			Name:             b.Name,
			Author:           b.Author,
			Publisher:        b.Publisher,
			BookType:         b.BookType,
			RegisterCount:    b.RegisterCount,
			ReceiveCount:     b.ReceiveCount,
			BorrowCount:      b.BorrowCount,
			OnBorrow:         b.OnBorrow,
			SurplusCount:     b.SurplusCount,
			CreatedTime:      b.CreatedTime.UnixMilli(),
			UpdatedTime:      b.UpdatedTime.UnixMilli(),
			CreatedBy:        b.CreatedBy,
			UpdatedBy:        b.UpdatedBy,
			DictionaryNodeId: b.DictionaryNodeID,
			FileId:           b.FileID,
			OrganizationId:   b.OrganizationId,
		})
	}

	return &docvault.GetBookListResp{
		Data:  list,
		Total: int32(total),
	}, nil
}
