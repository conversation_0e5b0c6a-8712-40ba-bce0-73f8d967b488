package booklogic

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateBookLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBookLogic {
	return &CreateBookLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 新增书籍信息
func (l *CreateBookLogic) CreateBook(in *docvault.CreateBookReq) (*docvault.CreateBookResp, error) {
	bookClient := mapper.NewBooksClient(l.svcCtx.DocvaultDB)

	book, err := bookClient.GetBookByOrgIdAndName(l.ctx, in.Name, in.OrganizationId)
	if err != nil {
		return nil, err
	}
	if book.Name != "" {
		return nil, errors.New("书籍名称已存在")
	}

	bookInfo, err := bookClient.GetMaxNumberByDictNodeId(l.ctx, in.OrganizationId, in.DictionaryNodeId)
	if err != nil {
		return nil, err
	}

	number := in.OrganizationCode + "/" + in.BookNumber
	sort := 1

	// 设置编号
	if bookInfo.Number == "" {
		number += "001"
	} else {
		sort = bookInfo.Sort + 1
		newNum := fmt.Sprintf("%03d", sort)
		number += newNum
	}

	id := l.svcCtx.IDGenerator.GenerateIDString()
	err = bookClient.Create(l.ctx, mapper.Books{
		Id:               id,
		Number:           number,
		Name:             in.Name,
		Author:           in.Author,
		Publisher:        in.Publisher,
		BookType:         in.BookType,
		RegisterCount:    in.RegisterCount,
		ReceiveCount:     0,
		BorrowCount:      0,
		OnBorrow:         false,
		SurplusCount:     in.RegisterCount,
		DictionaryNodeID: in.DictionaryNodeId,
		CreatedBy:        in.UserId,
		UpdatedBy:        in.UserId,
		OrganizationId:   in.OrganizationId,
		Status:           1,
		IsDelete:         0,
		FileID:           in.FileId,
		Sort:             sort,
	})
	if err != nil {
		l.Logger.Errorf("书籍信息新建失败：%v", err)
		return nil, err
	}
	return &docvault.CreateBookResp{
		Id: id,
	}, nil
}
