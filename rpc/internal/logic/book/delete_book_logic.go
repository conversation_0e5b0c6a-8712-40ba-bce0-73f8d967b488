package booklogic

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteBookLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBookLogic {
	return &DeleteBookLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除书籍信息
func (l *DeleteBookLogic) DeleteBook(in *docvault.DeleteBookReq) (*docvault.DeleteBookResp, error) {
	bookClient := mapper.NewBooksClient(l.svcCtx.DocvaultDB)
	bookInfo, err := bookClient.GetBookInfoById(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}
	if bookInfo.OnBorrow || bookInfo.ReceiveCount > 0 {
		return &docvault.DeleteBookResp{
			Code: 1,
			Msg:  "无法删除，该书籍处于领用或借出中。",
		}, nil
	}
	err = mapper.NewBooksClient(l.svcCtx.DocvaultDB).Delete(l.ctx, in.Id)
	if err != nil {
		l.Logger.Errorf("删除书籍信息失败：%v", err)
		return nil, err
	}
	return &docvault.DeleteBookResp{}, nil
}
