package booklogic

import (
	"context"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
)

type ImportBookLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewImportBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ImportBookLogic {
	return &ImportBookLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 批量导入书籍信息
func (l *ImportBookLogic) ImportBook(in *docvault.ImportBookReq) (*docvault.ImportBookResp, error) {
	bookClient := mapper.NewBooksClient(l.svcCtx.DocvaultDB)
	userLoginInfo := utils.GetCurrentLoginUser(l.ctx)

	// 查询现有书籍的名称，封装到map
	existingNameMap, err := l.getExistingBook(bookClient, userLoginInfo.OrganizationId)
	if err != nil {
		l.Logger.Errorf("获取书籍信息失败：%v", err)
		return nil, err
	}

	var mapperBooks []mapper.Books
	var importRespInfo []*docvault.ImportRespInfo
	bookNumberMap := make(map[string]int)

	// 设置新建书籍信息
	for k, v := range in.ImportBookInfo {

		// 检查导入书籍是否同名
		if existingNameMap[v.Name] {
			return nil, errors.New("导入失败，存在同名书籍")
		}
		existingNameMap[v.Name] = true

		// 设置书籍编号
		number, bookMap, sort, err := l.setNumber(bookNumberMap, bookClient, v.DictionaryNodeId, userLoginInfo.OrganizationId, v.Number)
		if err != nil {
			l.Logger.Errorf("设置书籍编号失败：%v", err)
			return nil, err
		}
		bookNumberMap = bookMap
		// 添加新的编号
		in.ImportBookInfo[k].Number = number

		// 封装导入数据库数据
		id := l.svcCtx.IDGenerator.GenerateIDString()
		mapperBooks = append(mapperBooks, mapper.Books{
			Id:               id,
			Number:           number,
			Name:             v.Name,
			Author:           v.Author,
			Publisher:        v.Publisher,
			BookType:         v.BookType,
			RegisterCount:    v.RegisterCount,
			ReceiveCount:     0,
			BorrowCount:      0,
			OnBorrow:         false,
			SurplusCount:     v.RegisterCount,
			DictionaryNodeID: v.DictionaryNodeId,
			CreatedBy:        userLoginInfo.UserId,
			UpdatedBy:        userLoginInfo.UserId,
			OrganizationId:   userLoginInfo.OrganizationId,
			Status:           1,
			IsDelete:         0,
			FileID:           v.FileId,
			Sort:             sort,
		})

		// 组装返回信息
		importRespInfo = append(importRespInfo, &docvault.ImportRespInfo{
			DictionaryNodeId: v.DictionaryNodeId,
			BookId:           id,
		})
	}

	// 执行批量导入数据库
	err = bookClient.BatchAddition(l.ctx, mapperBooks)
	if err != nil {
		l.Logger.Errorf("书籍信息导入失败：%v", err)
		return nil, err
	}

	return &docvault.ImportBookResp{
		Data: importRespInfo,
	}, nil
}

func (l *ImportBookLogic) getExistingBook(bookClient *mapper.BooksClient, orgID string) (map[string]bool, error) {
	orgBooks, err := bookClient.GetBooksByOrgId(l.ctx, orgID)
	if err != nil {
		return nil, err
	}
	existingNameMap := make(map[string]bool)
	for _, orgBook := range orgBooks {
		existingNameMap[orgBook.Name] = true
	}
	return existingNameMap, nil
}

func (l *ImportBookLogic) setNumber(bookInfoMap map[string]int, bookClient *mapper.BooksClient, dictionaryNodeId, orgId, number string) (string, map[string]int, int, error) {
	sort := 1
	if mapV, ok := bookInfoMap[dictionaryNodeId]; !ok {
		// 查询最大编号
		bookInfo, err := bookClient.GetMaxNumberByDictNodeId(l.ctx, orgId, dictionaryNodeId)
		if err != nil {
			return "", nil, 0, err
		}

		// 设置后续编号
		if bookInfo.Number == "" {
			number += "001"
		} else {
			sort = bookInfo.Sort + 1
			newNum := fmt.Sprintf("%03d", sort)
			number += newNum
		}
		bookInfoMap[dictionaryNodeId] = sort
	} else {
		// 设置后续编号
		sort = mapV + 1
		newNum := fmt.Sprintf("%03d", sort)
		number += newNum
		bookInfoMap[dictionaryNodeId] = sort
	}
	return number, bookInfoMap, sort, nil
}
