package internaldocumentlibrarylogic

import (
	"context"
	"time"

	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeLogic {
	return &ChangeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修订文档
func (l *ChangeLogic) Change(in *docvault.InternalDocumentChangeReq) (*docvault.EmptyResp, error) {
	err := l.svcCtx.Factory.NewInternalDocumentLibrary().Change(l.ctx, aggregate.InternalDocumentChangeRequest{
		ID:                in.Id,
		Name:              in.Name,
		FileID:            in.FileId,
		DocCategoryID:     in.DocCategoryId,
		DepartmentID:      in.DepartmentId,
		PublishDate:       time.UnixMilli(in.PublishDate),
		EffectiveDate:     time.UnixMilli(in.EffectiveDate),
		OriginalNo:        in.OriginalNo,        // 原文件编号
		OriginalVersionNo: in.OriginalVersionNo, // 原文件版本
		AuthorID:          in.AuthorId,          // 编制人
		NoPrefix:          in.NoPrefix,          // 编号前缀
		OrganizationID:    utils.GetContextOrganizationID(l.ctx),
	})
	if err != nil {
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}
