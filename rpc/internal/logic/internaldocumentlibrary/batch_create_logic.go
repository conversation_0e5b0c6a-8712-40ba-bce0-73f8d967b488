package internaldocumentlibrarylogic

import (
	"context"
	"time"

	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchCreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewBatchCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchCreateLogic {
	return &BatchCreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 批量创建文档
func (l *BatchCreateLogic) BatchCreate(in *docvault.InternalDocumentBatchCreateReq) (*docvault.InternalDocumentBatchCreateResp, error) {
	infos, err := l.svcCtx.Factory.NewInternalDocumentLibrary().BatchCreate(l.ctx, l.buildInternalDocumentCreateRequest(in))
	if err != nil {
		return nil, err
	}

	return &docvault.InternalDocumentBatchCreateResp{
		InternalDocumentInfos: l.buildInternalDocumentGetResp(infos),
	}, nil
}

func (l *BatchCreateLogic) buildInternalDocumentCreateRequest(in *docvault.InternalDocumentBatchCreateReq) []aggregate.InternalDocumentCreateRequest {
	internalDocumentCreateRequests := make([]aggregate.InternalDocumentCreateRequest, 0)
	for _, internalDocumentCreateReq := range in.InternalDocumentCreateReqs {
		internalDocumentCreateRequests = append(internalDocumentCreateRequests, aggregate.InternalDocumentCreateRequest{
			NoPrefix:          internalDocumentCreateReq.NoPrefix,
			Name:              internalDocumentCreateReq.Name,
			FileID:            internalDocumentCreateReq.FileId,
			DocCategoryID:     internalDocumentCreateReq.DocCategoryId,
			DepartmentID:      internalDocumentCreateReq.DepartmentId,
			PublishDate:       time.UnixMilli(internalDocumentCreateReq.PublishDate),
			EffectiveDate:     time.UnixMilli(internalDocumentCreateReq.EffectiveDate),
			OriginalNo:        internalDocumentCreateReq.OriginalNo,
			OriginalVersionNo: internalDocumentCreateReq.OriginalVersionNo,
			AuthorID:          internalDocumentCreateReq.AuthorId,
			OrganizationID:    utils.GetContextOrganizationID(l.ctx),
		})
	}
	return internalDocumentCreateRequests
}

func (l *BatchCreateLogic) buildInternalDocumentGetResp(infos []aggregate.InternalDocumentInfo) []*docvault.InternalDocumentGetResp {
	internalDocumentGetResps := make([]*docvault.InternalDocumentGetResp, 0)
	for _, info := range infos {
		internalDocumentGetResps = append(internalDocumentGetResps, &docvault.InternalDocumentGetResp{
			Id:                info.ID,
			No:                getViewNo(info.NoPrefix, info.No),
			VersionNo:         getVersionNo(info.VersionNo),
			OriginalNo:        info.OriginalNo,
			OriginalVersionNo: info.OriginalVersionNo,
			Name:              info.Name,
			DocCategoryId:     info.DocCategoryID,
			DepartmentId:      info.DepartmentID,
			AuthorId:          info.AuthorID,
			PublishDate:       info.PublishDate.UnixMilli(),
			EffectiveDate:     info.EffectiveDate.UnixMilli(),
			Status:            int32(info.Status),
			NoPrefix:          info.NoPrefix,
			FileId:            info.FileID,
		})
	}
	return internalDocumentGetResps
}
