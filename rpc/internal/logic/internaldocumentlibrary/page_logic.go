package internaldocumentlibrarylogic

import (
	"context"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type PageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewPageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PageLogic {
	return &PageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 分页查询文档
func (l *PageLogic) Page(in *docvault.InternalDocumentPageReq) (*docvault.InternalDocumentPageResp, error) {
	docs, total, err := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB).Page(l.ctx, mapper.InternalDocumentLibraryPageReq{
		Page:           int(in.PageInfo.Page),
		PageSize:       int(in.PageInfo.PageSize),
		NoPage:         in.PageInfo.NoPage,
		Name:           in.Name,
		No:             in.No,
		OriginalNo:     in.OriginalNo,
		Ids:            in.Ids,
		DocCategoryIds: in.DocCategoryIds,
		DepartmentIds:  in.DepartmentIds,
		Status:         in.Status,
		OrganizationID: utils.GetContextOrganizationID(l.ctx),
		HasAttachment:  in.HasAttachment,
	})
	if err != nil {
		return nil, err
	}

	items := l.buildItems(docs)

	return &docvault.InternalDocumentPageResp{
		Data:  items,
		Total: total,
	}, nil
}

func (*PageLogic) buildItems(docs []mapper.InternalDocumentLibrary) []*docvault.InternalDocumentPageItem {
	items := make([]*docvault.InternalDocumentPageItem, len(docs))
	for i, doc := range docs {
		items[i] = &docvault.InternalDocumentPageItem{
			Id:                doc.ID,
			No:                getViewNo(doc.NoPrefix, doc.SerialNo),
			VersionNo:         getVersionNo(doc.VersionNo),
			OriginalNo:        doc.OriginalNo,
			OriginalVersionNo: doc.OriginalVersionNo,
			Name:              doc.Name,
			DocCategoryId:     doc.DocCategoryID,
			DepartmentId:      doc.DepartmentID,
			AuthorId:          doc.AuthorID,
			PublishDate:       doc.PublishDate.UnixMilli(),
			EffectiveDate:     doc.EffectiveDate.UnixMilli(),
			Status:            int32(doc.Status),
			ApprovalInfo:      buildApprovalInfo(doc.GetApprovalInfo()),
		}
	}
	return items
}
