package internaldocumentlibrarylogic

import (
	"context"
	"time"

	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建文档
func (l *CreateLogic) Create(in *docvault.InternalDocumentCreateReq) (*docvault.InternalDocumentCreateResp, error) {
	infos, err := l.svcCtx.Factory.NewInternalDocumentLibrary().BatchCreate(l.ctx, []aggregate.InternalDocumentCreateRequest{
		{
			NoPrefix:          in.NoPrefix,
			Name:              in.Name,
			FileID:            in.FileId,
			DocCategoryID:     in.DocCategoryId,
			DepartmentID:      in.DepartmentId,
			PublishDate:       time.UnixMilli(in.PublishDate),
			EffectiveDate:     time.UnixMilli(in.EffectiveDate),
			OriginalNo:        in.OriginalNo,
			OriginalVersionNo: in.OriginalVersionNo,
			AuthorID:          in.AuthorId,
			OrganizationID:    utils.GetContextOrganizationID(l.ctx),
		},
	})
	if err != nil {
		return nil, err
	}
	return &docvault.InternalDocumentCreateResp{
		Id: infos[0].ID,
	}, nil
}
