package internaldocumentlibrarylogic

import (
	"context"
	"fmt"
	"strconv"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取文档
func (l *GetLogic) Get(in *docvault.InternalDocumentGetReq) (*docvault.InternalDocumentGetResp, error) {
	doc, err := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB).GetByID(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}
	return &docvault.InternalDocumentGetResp{
		Id:                doc.ID,
		No:                getViewNo(doc.NoPrefix, doc.SerialNo),
		VersionNo:         getVersionNo(doc.VersionNo),
		OriginalNo:        doc.OriginalNo,
		OriginalVersionNo: doc.OriginalVersionNo,
		Name:              doc.Name,
		DocCategoryId:     doc.DocCategoryID,
		DepartmentId:      doc.DepartmentID,
		AuthorId:          doc.AuthorID,
		PublishDate:       doc.PublishDate.UnixMilli(),
		EffectiveDate:     doc.EffectiveDate.UnixMilli(),
		Status:            int32(doc.Status),
		ApprovalInfo:      buildApprovalInfo(doc.GetApprovalInfo()),
		NoPrefix:          doc.NoPrefix,
		FileId:            doc.FileID,
	}, nil
}

func getViewNo(noPrefix string, no int) string {
	return fmt.Sprintf("%s-%d", noPrefix, no)
}

func buildApprovalInfo(approvalInfo mapper.ApprovalInfo) *docvault.ApprovalInfo {
	var auditors []*docvault.ApprovalInfoItem
	if len(approvalInfo.Approvers) != 0 {
		auditors = make([]*docvault.ApprovalInfoItem, len(approvalInfo.Auditors))
		for i, auditor := range approvalInfo.Auditors {
			auditors[i] = &docvault.ApprovalInfoItem{
				UserId:     auditor.UserID,
				PassedDate: auditor.PassedDate,
			}
		}
	}

	var approvers []*docvault.ApprovalInfoItem
	if len(approvalInfo.Approvers) != 0 {
		approvers = make([]*docvault.ApprovalInfoItem, len(approvalInfo.Approvers))
		for i, approver := range approvalInfo.Approvers {
			approvers[i] = &docvault.ApprovalInfoItem{
				UserId:     approver.UserID,
				PassedDate: approver.PassedDate,
			}
		}
	}

	return &docvault.ApprovalInfo{
		Auditors:  auditors,
		Approvers: approvers,
	}
}

// 序号转版本号，第一版本为序号 0 版本 A/0,修订后为序号 1 版本 A/1，依次类推，序号 9 版本A/9 之后序号10 变为B/0，B/9之后变为C/0， C之后不再增加
func getVersionNo(versionNo int) string {
	if versionNo < 9 {
		return "A/" + strconv.Itoa(versionNo)
	} else if versionNo < 19 {
		return "B/" + strconv.Itoa(versionNo-10)
	} else {
		return "C/" + strconv.Itoa(versionNo-19)
	}
}
