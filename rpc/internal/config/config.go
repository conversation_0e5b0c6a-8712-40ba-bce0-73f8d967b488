package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	MicroServices  MicroServices
	Kafka          KafkaConf
	DocvaultDB     DBConf
	Snowflake      Snowflake
	ScheduledTasks []ScheduledTaskConf `json:",optional"`
}

// DBConf 数据库配置
type DBConf struct {
	Type            string `json:",optional"`
	Host            string
	Port            string
	User            string
	Password        string
	Schema          string
	MaxIdleConns    int
	ConnMaxLifetime int64
	MaxOpenConns    int
}

type Snowflake struct {
	//节点占位
	NodeBits uint8
	// 步骤占位
	StepBits uint8
	// 元年
	Epoch int64
	// 默认节点
	Node int64
}

type MicroServices map[string]MicroService

func (m MicroServices) GetService(name string) MicroService {

	v, ok := m[name]
	if !ok {
		logx.Errorf("未配置服务：%s\n", name)
	}
	return v
}

type MicroService struct {
	Url        string
	Desc       string
	Parameters map[string]string
}

type KafkaConf struct {
	Brokers   []string
	MinBytes  int
	MaxBytes  int
	Consumers []QueueConf `json:",optional"`
}

type QueueConf struct {
	Topic   string
	GroupID string
	Key     string
}

type ScheduledTaskConf struct {
	Name     string
	CronExpr string
	Enabled  bool `json:",optional"`
}
