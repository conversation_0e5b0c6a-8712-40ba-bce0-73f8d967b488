package interceptor

import (
	"context"
	"docvault/utils/statusx"

	"google.golang.org/grpc"
)

// rpc service logger interceptor
func ErrorUnaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	return handler(ctx, req)
}

// rpc service logger interceptor
func ErrorStreamInterceptor(srv any, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	return convertError(handler(ss.Context(), ss))
}

func convertError(err error) error {
	if err == nil {
		return nil
	}
	if statusx.IsRpcError(err) {
		return err
	}
	return statusx.NewRpcInternalError(err.Error())
}
