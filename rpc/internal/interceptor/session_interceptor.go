package interceptor

import (
	"context"
	"docvault/utils"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	TraceIDPrefix   = "x-trace-"
	SessionIDPrefix = "x-session-"
)

// rpc service logger interceptor
func SessionUnaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	return handler(extractSession(ctx), req)
}

// rpc service logger interceptor
func SessionStreamInterceptor(srv any, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	ctx := extractSession(ss.Context())
	// 用新 context 包装 ServerStream
	wss := &wrappedServerStream{
		ServerStream: ss,
		wrappedCtx:   ctx,
	}
	return handler(ss.Context(), wss)
}

func extractSession(ctx context.Context) context.Context {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		md = metadata.New(nil)
	}
	for k, v := range md {
		if len(v) == 0 {
			continue
		}

		if strings.HasPrefix(k, TraceIDPrefix) {
			ctx = context.WithValue(ctx, utils.KebabToCamel(strings.TrimPrefix(k, TraceIDPrefix)), v[0])
			ctx = logx.ContextWithFields(ctx, logx.Field("traceId", v[0]))
		} else if strings.HasPrefix(k, SessionIDPrefix) {
			xk := utils.KebabToCamel(strings.TrimPrefix(k, SessionIDPrefix))
			ctx = context.WithValue(ctx, xk, v[0])
		}
	}

	return ctx
}

type wrappedServerStream struct {
	grpc.ServerStream
	wrappedCtx context.Context
}

func (w *wrappedServerStream) Context() context.Context {
	return w.wrappedCtx
}
